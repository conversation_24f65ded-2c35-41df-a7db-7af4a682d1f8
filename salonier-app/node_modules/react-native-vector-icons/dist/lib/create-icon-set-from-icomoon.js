var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=createIconSetFromIcoMoon;var _createIconSet=_interopRequireDefault(require("./create-icon-set"));function createIconSetFromIcoMoon(config,fontFamilyArg,fontFile){var glyphMap={};config.icons.forEach(function(icon){icon.properties.name.split(/\s*,\s*/g).forEach(function(name){glyphMap[name]=icon.properties.code;});});var fontFamily=fontFamilyArg||config.preferences.fontPref.metadata.fontFamily;return(0,_createIconSet.default)(glyphMap,fontFamily,fontFile||fontFamily+".ttf");}