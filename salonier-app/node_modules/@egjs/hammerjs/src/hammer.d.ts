import * as <PERSON> from "hammerjs";

export default Hammer;
export {
  INPUT_START,
	INPUT_MOVE,
	INPUT_END,
	INPUT_CANCEL,
	STATE_POSSIBLE,
	STATE_BEGAN,
	STATE_CHANGED,
	STATE_ENDED,
	STATE_RECOGNIZED,
	STATE_CANCELLED,
	STATE_FAILED,

	DIRECTION_NONE,
	DIRECTION_LEFT,
	DIRECTION_RIGHT,
	DIRECTION_UP,
	DIRECTION_DOWN,
	DIRECTION_HORIZONTAL,
	DIRECTION_VERTICAL,
	DIRECTION_ALL,
	Manager,
	Input,
	TouchAction,
	TouchInput,
	MouseInput,
	PointerEventInput,
	TouchMouseInput,
	SingleTouchInput,
	Recognizer,
	AttrRecognizer,
	Tap,
	Pan,
	Swipe,
	Pinch,
	Rotate,
	Press,
	on,
	off,
	each,
	merge,
	extend,
	inherit,
	bindFn,
	prefixed,
	defaults,
} from "hammerjs";
