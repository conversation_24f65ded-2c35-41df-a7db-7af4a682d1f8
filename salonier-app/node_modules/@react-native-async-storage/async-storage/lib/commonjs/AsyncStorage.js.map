{"version": 3, "names": ["_mergeOptions", "_interopRequireDefault", "require", "e", "__esModule", "default", "merge", "mergeOptions", "bind", "concatArrays", "ignoreUndefined", "mergeLocalStorageItem", "key", "value", "oldValue", "window", "localStorage", "getItem", "oldObject", "JSON", "parse", "newObject", "nextValue", "stringify", "setItem", "createPromise", "getValue", "callback", "Promise", "resolve", "reject", "err", "createPromiseAll", "promises", "processResult", "all", "then", "result", "errors", "AsyncStorage", "removeItem", "mergeItem", "clear", "getAllKeys", "numberOfKeys", "length", "keys", "i", "push", "flushGetRequests", "undefined", "multiGet", "map", "multiSet", "keyValuePairs", "item", "multiRemove", "multiMerge", "_default", "exports"], "sourceRoot": "../../src", "sources": ["AsyncStorage.ts"], "mappings": ";;;;;;AAQA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAyC,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AARzC;AACA;AACA;AACA;AACA;AACA;AACA;;AASA;;AAEA;;AAGA,MAAMG,KAAK,GAAGC,qBAAY,CAACC,IAAI,CAAC;EAC9BC,YAAY,EAAE,IAAI;EAClBC,eAAe,EAAE;AACnB,CAAC,CAAC;AAEF,SAASC,qBAAqBA,CAACC,GAAW,EAAEC,KAAa,EAAE;EACzD,MAAMC,QAAQ,GAAGC,MAAM,CAACC,YAAY,CAACC,OAAO,CAACL,GAAG,CAAC;EACjD,IAAIE,QAAQ,EAAE;IACZ,MAAMI,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACN,QAAQ,CAAC;IACtC,MAAMO,SAAS,GAAGF,IAAI,CAACC,KAAK,CAACP,KAAK,CAAC;IACnC,MAAMS,SAAS,GAAGH,IAAI,CAACI,SAAS,CAACjB,KAAK,CAACY,SAAS,EAAEG,SAAS,CAAC,CAAC;IAC7DN,MAAM,CAACC,YAAY,CAACQ,OAAO,CAACZ,GAAG,EAAEU,SAAS,CAAC;EAC7C,CAAC,MAAM;IACLP,MAAM,CAACC,YAAY,CAACQ,OAAO,CAACZ,GAAG,EAAEC,KAAK,CAAC;EACzC;AACF;AAEA,SAASY,aAAaA,CACpBC,QAAsB,EACtBC,QAAmB,EACF;EACjB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,IAAI;MACF,MAAMjB,KAAK,GAAGa,QAAQ,CAAC,CAAC;MACxBC,QAAQ,GAAG,IAAI,EAAEd,KAAK,CAAC;MACvBgB,OAAO,CAAChB,KAAK,CAAC;IAChB,CAAC,CAAC,OAAOkB,GAAG,EAAE;MACZJ,QAAQ,GAAGI,GAAG,CAAC;MACfD,MAAM,CAACC,GAAG,CAAC;IACb;EACF,CAAC,CAAC;AACJ;AAEA,SAASC,gBAAgBA,CAKvBC,QAA2B,EAC3BN,QAA2C,EAC3CO,aAA+B,EACV;EACrB,OAAON,OAAO,CAACO,GAAG,CAACF,QAAQ,CAAC,CAACG,IAAI,CAC9BC,MAAM,IAAK;IACV,MAAMxB,KAAK,GAAGqB,aAAa,GAAGG,MAAM,CAAC,IAAI,IAAI;IAC7CV,QAAQ,GAAG,IAAI,EAAEd,KAAK,CAAC;IACvB,OAAOe,OAAO,CAACC,OAAO,CAAChB,KAAK,CAAC;EAC/B,CAAC,EACAyB,MAAM,IAAK;IACVX,QAAQ,GAAGW,MAAM,CAAC;IAClB,OAAOV,OAAO,CAACE,MAAM,CAACQ,MAAM,CAAC;EAC/B,CACF,CAAC;AACH;AAEA,MAAMC,YAAgC,GAAG;EACvC;AACF;AACA;EACEtB,OAAO,EAAEA,CAACL,GAAG,EAAEe,QAAQ,KAAK;IAC1B,OAAOF,aAAa,CAAC,MAAMV,MAAM,CAACC,YAAY,CAACC,OAAO,CAACL,GAAG,CAAC,EAAEe,QAAQ,CAAC;EACxE,CAAC;EAED;AACF;AACA;EACEH,OAAO,EAAEA,CAACZ,GAAG,EAAEC,KAAK,EAAEc,QAAQ,KAAK;IACjC,OAAOF,aAAa,CAClB,MAAMV,MAAM,CAACC,YAAY,CAACQ,OAAO,CAACZ,GAAG,EAAEC,KAAK,CAAC,EAC7Cc,QACF,CAAC;EACH,CAAC;EAED;AACF;AACA;EACEa,UAAU,EAAEA,CAAC5B,GAAG,EAAEe,QAAQ,KAAK;IAC7B,OAAOF,aAAa,CAAC,MAAMV,MAAM,CAACC,YAAY,CAACwB,UAAU,CAAC5B,GAAG,CAAC,EAAEe,QAAQ,CAAC;EAC3E,CAAC;EAED;AACF;AACA;EACEc,SAAS,EAAEA,CAAC7B,GAAG,EAAEC,KAAK,EAAEc,QAAQ,KAAK;IACnC,OAAOF,aAAa,CAAC,MAAMd,qBAAqB,CAACC,GAAG,EAAEC,KAAK,CAAC,EAAEc,QAAQ,CAAC;EACzE,CAAC;EAED;AACF;AACA;EACEe,KAAK,EAAGf,QAAQ,IAAK;IACnB,OAAOF,aAAa,CAAC,MAAMV,MAAM,CAACC,YAAY,CAAC0B,KAAK,CAAC,CAAC,EAAEf,QAAQ,CAAC;EACnE,CAAC;EAED;AACF;AACA;EACEgB,UAAU,EAAGhB,QAAQ,IAAK;IACxB,OAAOF,aAAa,CAAC,MAAM;MACzB,MAAMmB,YAAY,GAAG7B,MAAM,CAACC,YAAY,CAAC6B,MAAM;MAC/C,MAAMC,IAAc,GAAG,EAAE;MACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,YAAY,EAAEG,CAAC,IAAI,CAAC,EAAE;QACxC,MAAMnC,GAAG,GAAGG,MAAM,CAACC,YAAY,CAACJ,GAAG,CAACmC,CAAC,CAAC,IAAI,EAAE;QAC5CD,IAAI,CAACE,IAAI,CAACpC,GAAG,CAAC;MAChB;MACA,OAAOkC,IAAI;IACb,CAAC,EAAEnB,QAAQ,CAAC;EACd,CAAC;EAED;AACF;AACA;EACEsB,gBAAgB,EAAEA,CAAA,KAAMC,SAAS;EAEjC;AACF;AACA;AACA;AACA;AACA;EACEC,QAAQ,EAAEA,CAACL,IAAI,EAAEnB,QAAQ,KAAK;IAC5B,MAAMM,QAAQ,GAAGa,IAAI,CAACM,GAAG,CAAExC,GAAG,IAAK2B,YAAY,CAACtB,OAAO,CAACL,GAAG,CAAC,CAAC;IAC7D,MAAMsB,aAAa,GAAIG,MAAgB,IACrCA,MAAM,CAACe,GAAG,CAAC,CAACvC,KAAK,EAAEkC,CAAC,KAAK,CAACD,IAAI,CAACC,CAAC,CAAC,EAAElC,KAAK,CAAC,CAAC;IAC5C,OAAOmB,gBAAgB,CAACC,QAAQ,EAAEN,QAAQ,EAAEO,aAAa,CAAC;EAC5D,CAAC;EAED;AACF;AACA;AACA;EACEmB,QAAQ,EAAEA,CAACC,aAAa,EAAE3B,QAAQ,KAAK;IACrC,MAAMM,QAAQ,GAAGqB,aAAa,CAACF,GAAG,CAAEG,IAAI,IACtChB,YAAY,CAACf,OAAO,CAAC+B,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CACvC,CAAC;IACD,OAAOvB,gBAAgB,CAACC,QAAQ,EAAEN,QAAQ,CAAC;EAC7C,CAAC;EAED;AACF;AACA;EACE6B,WAAW,EAAEA,CAACV,IAAI,EAAEnB,QAAQ,KAAK;IAC/B,MAAMM,QAAQ,GAAGa,IAAI,CAACM,GAAG,CAAExC,GAAG,IAAK2B,YAAY,CAACC,UAAU,CAAC5B,GAAG,CAAC,CAAC;IAChE,OAAOoB,gBAAgB,CAACC,QAAQ,EAAEN,QAAQ,CAAC;EAC7C,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACE8B,UAAU,EAAEA,CAACH,aAAa,EAAE3B,QAAQ,KAAK;IACvC,MAAMM,QAAQ,GAAGqB,aAAa,CAACF,GAAG,CAAEG,IAAI,IACtChB,YAAY,CAACE,SAAS,CAACc,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CACzC,CAAC;IACD,OAAOvB,gBAAgB,CAACC,QAAQ,EAAEN,QAAQ,CAAC;EAC7C;AACF,CAAC;AAAC,IAAA+B,QAAA,GAAAC,OAAA,CAAAtD,OAAA,GAEakC,YAAY", "ignoreList": []}