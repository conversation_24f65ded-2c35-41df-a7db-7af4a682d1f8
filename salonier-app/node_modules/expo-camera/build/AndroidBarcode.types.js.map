{"version": 3, "file": "AndroidBarcode.types.js", "sourceRoot": "", "sources": ["../src/AndroidBarcode.types.ts"], "names": [], "mappings": "", "sourcesContent": ["interface AndroidBarcodeType {\n  type:\n    | 'contactInfo'\n    | 'geoPoint'\n    | 'sms'\n    | 'url'\n    | 'calendarEvent'\n    | 'driverLicense'\n    | 'email'\n    | 'phone'\n    | 'wifi';\n}\n\ninterface ContactInfoBarcode extends AndroidBarcodeType {\n  type: 'contactInfo';\n  firstName?: string;\n  middleName?: string;\n  lastName?: string;\n  title?: string;\n  organization?: string;\n  email?: string;\n  phone?: string;\n  url?: string;\n  address?: string;\n}\n\ninterface GeoPointBarcode extends AndroidBarcodeType {\n  type: 'geoPoint';\n  lat: string;\n  lng: string;\n}\n\ninterface SmsBarcode extends AndroidBarcodeType {\n  type: 'sms';\n  phoneNumber?: string;\n  message?: string;\n}\n\ninterface UrlBarcode extends AndroidBarcodeType {\n  type: 'url';\n  url?: string;\n}\n\ninterface CalendarEventBarcode extends AndroidBarcodeType {\n  type: 'calendarEvent';\n  summary?: string;\n  description?: string;\n  location?: string;\n  start?: string;\n  end?: string;\n}\n\ninterface DriverLicenseBarcode extends AndroidBarcodeType {\n  type: 'driverLicense';\n  firstName?: string;\n  middleName?: string;\n  lastName?: string;\n  licenseNumber?: string;\n  expiryDate?: string;\n  issueDate?: string;\n  addressStreet?: string;\n  addressCity?: string;\n  addressState?: string;\n}\n\ninterface EmailBarcode extends AndroidBarcodeType {\n  type: 'email';\n  address?: string;\n  subject?: string;\n  body?: string;\n}\n\ninterface PhoneBarcode extends AndroidBarcodeType {\n  type: 'phone';\n  number?: string;\n  phoneNumberType?: string;\n}\n\ninterface WifiBarcode extends AndroidBarcodeType {\n  type: 'wifi';\n  ssid?: string;\n  password?: string;\n  encryptionType?: string;\n}\n\nexport type AndroidBarcode =\n  | ContactInfoBarcode\n  | GeoPointBarcode\n  | SmsBarcode\n  | UrlBarcode\n  | CalendarEventBarcode\n  | DriverLicenseBarcode\n  | EmailBarcode\n  | PhoneBarcode\n  | WifiBarcode;\n"]}