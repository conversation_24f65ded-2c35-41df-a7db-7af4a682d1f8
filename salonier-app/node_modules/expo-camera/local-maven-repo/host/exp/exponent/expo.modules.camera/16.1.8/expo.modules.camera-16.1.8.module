{"formatVersion": "1.1", "component": {"group": "host.exp.exponent", "module": "expo.modules.camera", "version": "16.1.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.13"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.exifinterface", "module": "exifinterface", "version": {"requires": "1.3.7"}}, {"group": "androidx.appcompat", "module": "appcompat", "version": {"requires": "1.1.0"}}], "files": [{"name": "expo.modules.camera-16.1.8.aar", "url": "expo.modules.camera-16.1.8.aar", "size": 235972, "sha512": "6b857731f5e662a70bc4a9628128c7e1514f05c6d3d236c31131b0d9a04dddcecbf9cf06ec31b056073c2e2d108a023522d4cb70a4387dec94d517166d25b557", "sha256": "adaaa3630e1986015621e0dbda2bcc8cb66c5ebd98f4cd9dd1b3186167f9fb63", "sha1": "3803c3d16df84bae931e2a99ecdb384ccd52583c", "md5": "7a9c2159dfb67a14e9b0f8abefb13db5"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7", "version": {"requires": "2.0.21"}}, {"group": "androidx.camera", "module": "camera-core", "version": {"requires": "1.4.1"}}, {"group": "androidx.camera", "module": "camera-camera2", "version": {"requires": "1.4.1"}}, {"group": "androidx.camera", "module": "camera-lifecycle", "version": {"requires": "1.4.1"}}, {"group": "androidx.camera", "module": "camera-video", "version": {"requires": "1.4.1"}}, {"group": "com.google.android.gms", "module": "play-services-code-scanner", "version": {"requires": "16.1.0"}}, {"group": "androidx.camera", "module": "camera-view", "version": {"requires": "1.4.1"}}, {"group": "androidx.camera", "module": "camera-extensions", "version": {"requires": "1.4.1"}}, {"group": "com.google.mlkit", "module": "barcode-scanning", "version": {"requires": "17.2.0"}}, {"group": "androidx.camera", "module": "camera-mlkit-vision", "version": {"requires": "1.4.1"}}, {"group": "androidx.exifinterface", "module": "exifinterface", "version": {"requires": "1.3.7"}}, {"group": "androidx.appcompat", "module": "appcompat", "version": {"requires": "1.1.0"}}], "files": [{"name": "expo.modules.camera-16.1.8.aar", "url": "expo.modules.camera-16.1.8.aar", "size": 235972, "sha512": "6b857731f5e662a70bc4a9628128c7e1514f05c6d3d236c31131b0d9a04dddcecbf9cf06ec31b056073c2e2d108a023522d4cb70a4387dec94d517166d25b557", "sha256": "adaaa3630e1986015621e0dbda2bcc8cb66c5ebd98f4cd9dd1b3186167f9fb63", "sha1": "3803c3d16df84bae931e2a99ecdb384ccd52583c", "md5": "7a9c2159dfb67a14e9b0f8abefb13db5"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "expo.modules.camera-16.1.8-sources.jar", "url": "expo.modules.camera-16.1.8-sources.jar", "size": 27347, "sha512": "b1ff83240f398aa668a38242faf205fc7cf73ebce1d1921aac7568434616a60d473c1c98f8749a85f23ab790a46ef935120bcc2bb6122c60683fc2e42531bdd6", "sha256": "537af441edf8bd8303da027cca761da8b8b8810c29880c1f604466c71f700c8c", "sha1": "acd0b6ae62797826a8a4d695c70e27074f025885", "md5": "ce1bbc4ae775439ce1d5e49d1b55a461"}]}]}