var e=require("@hookform/resolvers"),r=require("@standard-schema/utils");function t(){return t=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var s in t)({}).hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e},t.apply(null,arguments)}exports.standardSchemaResolver=function(s,a,n){return void 0===n&&(n={}),function(a,i,o){try{var u=function(){if(l.issues){var s=function(e,s){for(var a={},n=0;n<e.length;n++){var i=e[n],o=r.getDotPath(i);if(o&&(a[o]||(a[o]={message:i.message,type:""}),s)){var u,l=a[o].types||{};a[o].types=t({},l,((u={})[Object.keys(l).length]=i.message,u))}}return a}(l.issues,!o.shouldUseNativeValidation&&"all"===o.criteriaMode);return{values:{},errors:e.toNestErrors(s,o)}}return o.shouldUseNativeValidation&&e.validateFieldsNatively({},o),{values:n.raw?Object.assign({},a):l.value,errors:{}}},l=s["~standard"].validate(a),v=function(){if(l instanceof Promise)return Promise.resolve(l).then(function(e){l=e})}();return Promise.resolve(v&&v.then?v.then(u):u())}catch(e){return Promise.reject(e)}}};
//# sourceMappingURL=standard-schema.js.map
