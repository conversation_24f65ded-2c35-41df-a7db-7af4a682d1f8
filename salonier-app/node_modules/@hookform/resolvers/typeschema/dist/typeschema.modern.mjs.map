{"version": 3, "file": "typeschema.modern.mjs", "sources": ["../src/typeschema.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { StandardSchemaV1 } from '@standard-schema/spec';\nimport {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  Resolver,\n  appendErrors,\n} from 'react-hook-form';\n\nconst parseErrorSchema = (\n  typeschemaErrors: readonly StandardSchemaV1.Issue[],\n  validateAllFieldCriteria: boolean,\n): FieldErrors => {\n  const schemaErrors = Object.assign([], typeschemaErrors);\n  const errors: Record<string, FieldError> = {};\n\n  for (; schemaErrors.length; ) {\n    const error = typeschemaErrors[0];\n\n    if (!error.path) {\n      continue;\n    }\n    const _path = error.path.join('.');\n\n    if (!errors[_path]) {\n      errors[_path] = { message: error.message, type: '' };\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types[''];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        '',\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    schemaErrors.shift();\n  }\n\n  return errors;\n};\n\nexport function typeschemaResolver<Input extends FieldValues, Context, Output>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions?: never,\n  resolverOptions?: {\n    raw?: false;\n  },\n): Resolver<Input, Context, Output>;\n\nexport function typeschemaResolver<Input extends FieldValues, Context, Output>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions: never | undefined,\n  resolverOptions: {\n    raw: true;\n  },\n): Resolver<Input, Context, Input>;\n\n/**\n * Creates a resolver for react-hook-form using TypeSchema validation\n * @param {any} schema - The TypeSchema to validate against\n * @param {any} _ - Unused parameter\n * @param {Object} resolverOptions - Additional resolver configuration\n * @param {string} [resolverOptions.mode='async'] - Validation mode\n * @returns {Resolver} A resolver function compatible with react-hook-form\n * @example\n * const schema = z.object({\n *   name: z.string().required(),\n *   age: z.number().required(),\n * });\n *\n * useForm({\n *   resolver: typeschemaResolver(schema)\n * });\n */\nexport function typeschemaResolver<Input extends FieldValues, Context, Output>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions?: never,\n  resolverOptions: {\n    raw?: boolean;\n  } = {},\n): Resolver<Input, Context, Output | Input> {\n  return async (values, _, options) => {\n    let result = schema['~standard'].validate(values);\n    if (result instanceof Promise) {\n      result = await result;\n    }\n\n    if (result.issues) {\n      const errors = parseErrorSchema(\n        result.issues,\n        !options.shouldUseNativeValidation && options.criteriaMode === 'all',\n      );\n\n      return {\n        values: {},\n        errors: toNestErrors(errors, options),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return {\n      values: resolverOptions.raw ? Object.assign({}, values) : result.value,\n      errors: {},\n    };\n  };\n}\n"], "names": ["typeschemaResolver", "schema", "_schemaOptions", "resolverOptions", "async", "values", "_", "options", "result", "validate", "Promise", "issues", "errors", "parseErrorSchema", "typeschemaErrors", "validateAllFieldCriteria", "schemaErrors", "Object", "assign", "length", "error", "path", "_path", "join", "message", "type", "types", "messages", "appendErrors", "concat", "shift", "shouldUseNativeValidation", "criteriaMode", "toNestErrors", "validateFieldsNatively", "raw", "value"], "mappings": "8HAmFM,SAAUA,EACdC,EACAC,EACAC,EAEI,IAEJ,OAAOC,MAAOC,EAAQC,EAAGC,KACvB,IAAIC,EAASP,EAAO,aAAaQ,SAASJ,GAK1C,GAJIG,aAAkBE,UACpBF,QAAeA,GAGbA,EAAOG,OAAQ,CACjB,MAAMC,EAvFaC,EACvBC,EACAC,KAEA,MAAMC,EAAeC,OAAOC,OAAO,GAAIJ,GACjCF,EAAqC,CAAE,EAE7C,KAAOI,EAAaG,QAAU,CAC5B,MAAMC,EAAQN,EAAiB,GAE/B,IAAKM,EAAMC,KACT,SAEF,MAAMC,EAAQF,EAAMC,KAAKE,KAAK,KAM9B,GAJKX,EAAOU,KACVV,EAAOU,GAAS,CAAEE,QAASJ,EAAMI,QAASC,KAAM,KAG9CV,EAA0B,CAC5B,MAAMW,EAAQd,EAAOU,GAAOI,MACtBC,EAAWD,GAASA,EAAM,IAEhCd,EAAOU,GAASM,EACdN,EACAP,EACAH,EACA,GACAe,EACK,GAAgBE,OAAOF,EAAsBP,EAAMI,SACpDJ,EAAMI,QAEd,CAEAR,EAAac,OACf,CAEA,OAAOlB,GAkDYC,CACbL,EAAOG,QACNJ,EAAQwB,2BAAsD,QAAzBxB,EAAQyB,cAGhD,MAAO,CACL3B,OAAQ,CAAA,EACRO,OAAQqB,EAAarB,EAAQL,GAEjC,CAIA,OAFAA,EAAQwB,2BAA6BG,EAAuB,CAAA,EAAI3B,GAEzD,CACLF,OAAQF,EAAgBgC,IAAMlB,OAAOC,OAAO,CAAA,EAAIb,GAAUG,EAAO4B,MACjExB,OAAQ,CAAA,GAGd"}