"use strict";var st=Object.create;var ge=Object.defineProperty;var at=Object.getOwnPropertyDescriptor;var lt=Object.getOwnPropertyNames;var ct=Object.getPrototypeOf,ut=Object.prototype.hasOwnProperty;var p=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var dt=(e,t,r,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of lt(t))!ut.call(e,n)&&n!==r&&ge(e,n,{get:()=>t[n],enumerable:!(i=at(t,n))||i.enumerable});return e};var R=(e,t,r)=>(r=e!=null?st(ct(e)):{},dt(t||!e||!e.__esModule?ge(r,"default",{value:e,enumerable:!0}):r,e));var Oe=p(C=>{"use strict";Object.defineProperty(C,"__esModule",{value:!0});C.isGestureObjectEventCallbackMethod=C.isGestureHandlerEventCallback=C.gestureHandlerBuilderMethods=void 0;var I=require("@babel/types"),ft=new Set(["Tap","Pan","Pinch","Rotation","Fling","LongPress","ForceTouch","Native","Manual","Race","Simultaneous","Exclusive","Hover"]);C.gestureHandlerBuilderMethods=new Set(["onBegin","onStart","onEnd","onFinalize","onUpdate","onChange","onTouchesDown","onTouchesMove","onTouchesUp","onTouchesCancelled"]);function pt(e){return(0,I.isCallExpression)(e.parent)&&(0,I.isExpression)(e.parent.callee)&&_e(e.parent.callee)}C.isGestureHandlerEventCallback=pt;function _e(e){return(0,I.isMemberExpression)(e)&&(0,I.isIdentifier)(e.property)&&C.gestureHandlerBuilderMethods.has(e.property.name)&&ve(e.object)}C.isGestureObjectEventCallbackMethod=_e;function ve(e){return!!(mt(e)||(0,I.isCallExpression)(e)&&(0,I.isMemberExpression)(e.callee)&&ve(e.callee.object))}function mt(e){return(0,I.isCallExpression)(e)&&(0,I.isMemberExpression)(e.callee)&&(0,I.isIdentifier)(e.callee.object)&&e.callee.object.name==="Gesture"&&(0,I.isIdentifier)(e.callee.property)&&ft.has(e.callee.property.name)}});var Ie=p(X=>{"use strict";Object.defineProperty(X,"__esModule",{value:!0});X.isLayoutAnimationCallback=void 0;var S=require("@babel/types"),bt=new Set(["BounceIn","BounceInDown","BounceInLeft","BounceInRight","BounceInUp","BounceOut","BounceOutDown","BounceOutLeft","BounceOutRight","BounceOutUp","FadeIn","FadeInDown","FadeInLeft","FadeInRight","FadeInUp","FadeOut","FadeOutDown","FadeOutLeft","FadeOutRight","FadeOutUp","FlipInEasyX","FlipInEasyY","FlipInXDown","FlipInXUp","FlipInYLeft","FlipInYRight","FlipOutEasyX","FlipOutEasyY","FlipOutXDown","FlipOutXUp","FlipOutYLeft","FlipOutYRight","LightSpeedInLeft","LightSpeedInRight","LightSpeedOutLeft","LightSpeedOutRight","PinwheelIn","PinwheelOut","RollInLeft","RollInRight","RollOutLeft","RollOutRight","RotateInDownLeft","RotateInDownRight","RotateInUpLeft","RotateInUpRight","RotateOutDownLeft","RotateOutDownRight","RotateOutUpLeft","RotateOutUpRight","SlideInDown","SlideInLeft","SlideInRight","SlideInUp","SlideOutDown","SlideOutLeft","SlideOutRight","SlideOutUp","StretchInX","StretchInY","StretchOutX","StretchOutY","ZoomIn","ZoomInDown","ZoomInEasyDown","ZoomInEasyUp","ZoomInLeft","ZoomInRight","ZoomInRotate","ZoomInUp","ZoomOut","ZoomOutDown","ZoomOutEasyDown","ZoomOutEasyUp","ZoomOutLeft","ZoomOutRight","ZoomOutRotate","ZoomOutUp"]),yt=new Set(["Layout","LinearTransition","SequencedTransition","FadingTransition","JumpingTransition","CurvedTransition","EntryExitTransition"]),he=new Set([...bt,...yt]),kt=new Set(["build","duration","delay","getDuration","randomDelay","getDelay","getDelayFunction"]),gt=new Set(["easing","rotate","springify","damping","mass","stiffness","overshootClamping","restDisplacementThreshold","restSpeedThreshold","withInitialValues","getAnimationAndConfig"]),_t=new Set(["easingX","easingY","easingWidth","easingHeight","entering","exiting","reverse"]),vt=new Set([...kt,...gt,..._t]),Ot=new Set(["withCallback"]);function ht(e){return(0,S.isCallExpression)(e.parent)&&(0,S.isExpression)(e.parent.callee)&&Et(e.parent.callee)}X.isLayoutAnimationCallback=ht;function Et(e){return(0,S.isMemberExpression)(e)&&(0,S.isIdentifier)(e.property)&&Ot.has(e.property.name)&&Ee(e.object)}function Ee(e){return(0,S.isIdentifier)(e)&&he.has(e.name)?!0:!!((0,S.isNewExpression)(e)&&(0,S.isIdentifier)(e.callee)&&he.has(e.callee.name)||(0,S.isCallExpression)(e)&&(0,S.isMemberExpression)(e.callee)&&(0,S.isIdentifier)(e.callee.property)&&vt.has(e.callee.property.name)&&Ee(e.callee.object))}});var x=p(b=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});b.workletClassFactorySuffix=b.isWorkletizableObjectNode=b.isWorkletizableObjectPath=b.isWorkletizableFunctionNode=b.isWorkletizableFunctionPath=b.WorkletizableObject=b.WorkletizableFunction=void 0;var H=require("@babel/types");b.WorkletizableFunction="FunctionDeclaration|FunctionExpression|ArrowFunctionExpression|ObjectMethod";b.WorkletizableObject="ObjectExpression";function It(e){return e.isFunctionDeclaration()||e.isFunctionExpression()||e.isArrowFunctionExpression()||e.isObjectMethod()}b.isWorkletizableFunctionPath=It;function St(e){return(0,H.isFunctionDeclaration)(e)||(0,H.isFunctionExpression)(e)||(0,H.isArrowFunctionExpression)(e)||(0,H.isObjectMethod)(e)}b.isWorkletizableFunctionNode=St;function xt(e){return e.isObjectExpression()}b.isWorkletizableObjectPath=xt;function Wt(e){return(0,H.isObjectExpression)(e)}b.isWorkletizableObjectNode=Wt;b.workletClassFactorySuffix="__classFactory"});var q=p(A=>{"use strict";Object.defineProperty(A,"__esModule",{value:!0});A.replaceWithFactoryCall=A.isRelease=void 0;var Z=require("@babel/types");function Ct(){var e,t;let r=/(prod|release|stag[ei])/i;return!!(!((e=process.env.BABEL_ENV)===null||e===void 0)&&e.match(r)||!((t=process.env.NODE_ENV)===null||t===void 0)&&t.match(r))}A.isRelease=Ct;function wt(e,t,r){if(!t||!Ft(e))e.replaceWith(r);else{let i=(0,Z.variableDeclaration)("const",[(0,Z.variableDeclarator)((0,Z.identifier)(t),r)]);e.replaceWith(i)}}A.replaceWithFactoryCall=wt;function Ft(e){return(0,Z.isScopable)(e.parent)||(0,Z.isExportNamedDeclaration)(e.parent)}});var le=p(g=>{"use strict";Object.defineProperty(g,"__esModule",{value:!0});g.addCustomGlobals=g.initializeGlobals=g.globals=g.defaultGlobals=g.initializeState=void 0;var jt=["globalThis","Infinity","NaN","undefined","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape","Object","Function","Boolean","Symbol","Error","AggregateError","EvalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError","InternalError","Number","BigInt","Math","Date","String","RegExp","Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","BigInt64Array","BigUint64Array","Float32Array","Float64Array","Map","Set","WeakMap","WeakSet","ArrayBuffer","SharedArrayBuffer","DataView","Atomics","JSON","WeakRef","FinalizationRegistry","Iterator","AsyncIterator","Promise","GeneratorFunction","AsyncGeneratorFunction","Generator","AsyncGenerator","AsyncFunction","Reflect","Proxy","Intl","null","this","global","window","globalThis","console","performance","queueMicrotask","requestAnimationFrame","setImmediate","arguments","HermesInternal","_WORKLET","ReanimatedError","__reanimatedLoggerConfig"],Dt=["_IS_FABRIC","_log","_toString","_scheduleHostFunctionOnJS","_scheduleRemoteFunctionOnJS","_scheduleOnRuntime","_makeShareableClone","_updatePropsPaper","_updatePropsFabric","_measurePaper","_measureFabric","_scrollToPaper","_dispatchCommandPaper","_dispatchCommandFabric","_setGestureState","_notifyAboutProgress","_notifyAboutEnd","_runOnUIQueue","_getAnimationTimestamp"];function Pt(e){e.workletNumber=1,e.classesToWorkletize=[],Se(),xe(e)}g.initializeState=Pt;g.defaultGlobals=new Set(jt.concat(Dt));function Se(){g.globals=new Set(g.defaultGlobals)}g.initializeGlobals=Se;function xe(e){e.opts&&Array.isArray(e.opts.globals)&&e.opts.globals.forEach(t=>{g.globals.add(t)})}g.addCustomGlobals=xe});var $=p(z=>{"use strict";var Mt=z&&z.__rest||function(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(r[i]=e[i]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,i=Object.getOwnPropertySymbols(e);n<i.length;n++)t.indexOf(i[n])<0&&Object.prototype.propertyIsEnumerable.call(e,i[n])&&(r[i[n]]=e[i[n]]);return r};Object.defineProperty(z,"__esModule",{value:!0});z.workletTransformSync=void 0;var Rt=require("@babel/core");function At(e,t){let{extraPlugins:r=[],extraPresets:i=[]}=t,n=Mt(t,["extraPlugins","extraPresets"]);return(0,Rt.transformSync)(e,Object.assign(Object.assign({},n),{plugins:[...zt,...r],presets:[...qt,...i]}))}z.workletTransformSync=At;var qt=[require.resolve("@babel/preset-typescript")],zt=[]});var we=p(v=>{"use strict";var Lt=v&&v.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r);var n=Object.getOwnPropertyDescriptor(t,r);(!n||("get"in n?!t.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,i,n)}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),Tt=v&&v.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),We=v&&v.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)r!=="default"&&Object.prototype.hasOwnProperty.call(e,r)&&Lt(t,e,r);return Tt(t,e),t},Nt=v&&v.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(v,"__esModule",{value:!0});v.buildWorkletString=void 0;var Ce=require("@babel/core"),Bt=Nt(require("@babel/generator")),u=require("@babel/types"),V=require("assert"),Ut=We(require("convert-source-map")),Gt=We(require("fs")),Ht=$(),Zt=x(),Vt=q(),Jt="mock source map";function Xt(e,t,r,i,n){var s;$t(e,i);let l=e.program.body.find(_=>(0,u.isFunctionDeclaration)(_))||e.program.body.find(_=>(0,u.isExpressionStatement)(_))||void 0;(0,V.strict)(l,"[Reanimated] `draftExpression` is undefined.");let a=(0,u.isFunctionDeclaration)(l)?l:l.expression;(0,V.strict)("params"in a,"'params' property is undefined in 'expression'"),(0,V.strict)((0,u.isBlockStatement)(a.body),"[Reanimated] `expression.body` is not a `BlockStatement`");let y=new Set;(0,Ce.traverse)(e,{NewExpression(_){if(!(0,u.isIdentifier)(_.node.callee))return;let W=_.node.callee.name;if(!r.some(J=>J.name===W)||y.has(W))return;let M=r.findIndex(J=>J.name===W);r.splice(M,1);let oe=W+Zt.workletClassFactorySuffix;r.push((0,u.identifier)(oe)),(0,u.assertBlockStatement)(a.body),a.body.body.unshift((0,u.variableDeclaration)("const",[(0,u.variableDeclarator)((0,u.identifier)(W),(0,u.callExpression)((0,u.identifier)(oe),[]))])),y.add(W)}});let d=(0,u.functionExpression)((0,u.identifier)(i),a.params,a.body,a.generator,a.async),j=(0,Bt.default)(d).code;(0,V.strict)(n,"[Reanimated] `inputMap` is undefined.");let E=!((0,Vt.isRelease)()||t.opts.disableSourceMaps);if(E){n.sourcesContent=[];for(let _ of n.sources)n.sourcesContent.push(Gt.readFileSync(_).toString("utf-8"))}let P=(0,Ht.workletTransformSync)(j,{filename:t.file.opts.filename,extraPlugins:[er(r),...(s=t.opts.extraPlugins)!==null&&s!==void 0?s:[]],extraPresets:t.opts.extraPresets,compact:!0,sourceMaps:E,inputSourceMap:n,ast:!1,babelrc:!1,configFile:!1,comments:!1});(0,V.strict)(P,"[Reanimated] `transformed` is null.");let D;return E&&(Yt()?D=Jt:(D=Ut.fromObject(P.map).toObject(),delete D.sourcesContent)),[P.code,JSON.stringify(D)]}v.buildWorkletString=Xt;function $t(e,t){(0,Ce.traverse)(e,{FunctionExpression(r){if(!r.node.id){r.stop();return}let i=r.node.id.name;r.scope.rename(i,t)}})}function Yt(){return process.env.REANIMATED_JEST_SHOULD_MOCK_SOURCE_MAP==="1"}function Kt(e,t,r){t.length===0||!(0,u.isProgram)(e.parent)||(0,u.isExpression)(e.node.body)||e.node.body.body.unshift(r)}function Qt(e){var t;(0,u.isProgram)(e.parent)&&!(0,u.isArrowFunctionExpression)(e.node)&&!(0,u.isObjectMethod)(e.node)&&e.node.id&&e.scope.parent&&((t=e.scope.parent.bindings[e.node.id.name])===null||t===void 0?void 0:t.references)>0&&e.node.body.body.unshift((0,u.variableDeclaration)("const",[(0,u.variableDeclarator)((0,u.identifier)(e.node.id.name),(0,u.memberExpression)((0,u.thisExpression)(),(0,u.identifier)("_recur")))]))}function er(e){let t=(0,u.variableDeclaration)("const",[(0,u.variableDeclarator)((0,u.objectPattern)(e.map(r=>(0,u.objectProperty)((0,u.identifier)(r.name),(0,u.identifier)(r.name),!1,!0))),(0,u.memberExpression)((0,u.thisExpression)(),(0,u.identifier)("__closure")))]);return{visitor:{"FunctionDeclaration|FunctionExpression|ArrowFunctionExpression|ObjectMethod":r=>{Kt(r,e,t),Qt(r)}}}}});var De=p(L=>{"use strict";var tr=L&&L.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(L,"__esModule",{value:!0});L.makeWorkletFactory=void 0;var rr=require("@babel/core"),nr=tr(require("@babel/generator")),o=require("@babel/types"),w=require("assert"),je=require("path"),ir=le(),or=$(),Fe=x(),ce=q(),sr=we(),ar=require("../package.json").version,lr="x.y.z";function cr(e,t){var r;ur(e),(0,w.strict)(t.file.opts.filename,"[Reanimated] `state.file.opts.filename` is undefined.");let i=(0,nr.default)(e.node,{sourceMaps:!0,sourceFileName:t.file.opts.filename});i.code="("+(e.isObjectMethod()?"function ":"")+i.code+`
)`;let n=(0,or.workletTransformSync)(i.code,{extraPlugins:[...br,...(r=t.opts.extraPlugins)!==null&&r!==void 0?r:[]],extraPresets:t.opts.extraPresets,filename:t.file.opts.filename,ast:!0,babelrc:!1,configFile:!1,inputSourceMap:i.map});(0,w.strict)(n,"[Reanimated] `transformed` is undefined."),(0,w.strict)(n.ast,"[Reanimated] `transformed.ast` is undefined.");let s=mr(n.ast,e),l=(0,o.cloneNode)(e.node),a=(0,o.isBlockStatement)(l.body)?(0,o.functionExpression)(null,l.params,l.body,l.generator,l.async):l,{workletName:y,reactName:d}=pr(e,t),[j,E]=(0,sr.buildWorkletString)(n.ast,t,s,y,n.map);(0,w.strict)(j,"[Reanimated] `funString` is undefined.");let P=fr(j),D=1;s.length>0&&(D-=s.length+2);let _=e.parentPath.isProgram()?e:e.findParent(k=>{var se,ae;return(ae=(se=k.parentPath)===null||se===void 0?void 0:se.isProgram())!==null&&ae!==void 0?ae:!1});(0,w.strict)(_,"[Reanimated] `pathForStringDefinitions` is null."),(0,w.strict)(_.parentPath,"[Reanimated] `pathForStringDefinitions.parentPath` is null.");let W=_.parentPath.scope.generateUidIdentifier(`worklet_${P}_init_data`),M=(0,o.objectExpression)([(0,o.objectProperty)((0,o.identifier)("code"),(0,o.stringLiteral)(j))]);if(!(0,ce.isRelease)()){let k=t.file.opts.filename;t.opts.relativeSourceLocation&&(k=(0,je.relative)(t.cwd,k),E=E==null?void 0:E.replace(t.file.opts.filename,k)),M.properties.push((0,o.objectProperty)((0,o.identifier)("location"),(0,o.stringLiteral)(k)))}E&&M.properties.push((0,o.objectProperty)((0,o.identifier)("sourceMap"),(0,o.stringLiteral)(E))),!(0,ce.isRelease)()&&M.properties.push((0,o.objectProperty)((0,o.identifier)("version"),(0,o.stringLiteral)(dr()?lr:ar)));let ke=!t.opts.omitNativeOnlyData;ke&&_.insertBefore((0,o.variableDeclaration)("const",[(0,o.variableDeclarator)(W,M)])),(0,w.strict)(!(0,o.isFunctionDeclaration)(a),"[Reanimated] `funExpression` is a `FunctionDeclaration`."),(0,w.strict)(!(0,o.isObjectMethod)(a),"[Reanimated] `funExpression` is an `ObjectMethod`.");let G=[(0,o.variableDeclaration)("const",[(0,o.variableDeclarator)((0,o.identifier)(d),a)]),(0,o.expressionStatement)((0,o.assignmentExpression)("=",(0,o.memberExpression)((0,o.identifier)(d),(0,o.identifier)("__closure"),!1),(0,o.objectExpression)(s.map(k=>k.name.endsWith(Fe.workletClassFactorySuffix)?(0,o.objectProperty)((0,o.identifier)(k.name),(0,o.memberExpression)((0,o.identifier)(k.name.slice(0,k.name.length-Fe.workletClassFactorySuffix.length)),(0,o.identifier)(k.name))):(0,o.objectProperty)((0,o.identifier)(k.name),k,!1,!0))))),(0,o.expressionStatement)((0,o.assignmentExpression)("=",(0,o.memberExpression)((0,o.identifier)(d),(0,o.identifier)("__workletHash"),!1),(0,o.numericLiteral)(P)))];return ke&&G.push((0,o.expressionStatement)((0,o.assignmentExpression)("=",(0,o.memberExpression)((0,o.identifier)(d),(0,o.identifier)("__initData"),!1),W))),(0,ce.isRelease)()||(G.unshift((0,o.variableDeclaration)("const",[(0,o.variableDeclarator)((0,o.identifier)("_e"),(0,o.arrayExpression)([(0,o.newExpression)((0,o.memberExpression)((0,o.identifier)("global"),(0,o.identifier)("Error")),[]),(0,o.numericLiteral)(D),(0,o.numericLiteral)(-27)]))])),G.push((0,o.expressionStatement)((0,o.assignmentExpression)("=",(0,o.memberExpression)((0,o.identifier)(d),(0,o.identifier)("__stackDetails"),!1),(0,o.identifier)("_e"))))),G.push((0,o.returnStatement)((0,o.identifier)(d))),(0,o.functionExpression)(void 0,[],(0,o.blockStatement)(G))}L.makeWorkletFactory=cr;function ur(e){e.traverse({DirectiveLiteral(t){t.node.value==="worklet"&&t.getFunctionParent()===e&&t.parentPath.remove()}})}function dr(){return process.env.REANIMATED_JEST_SHOULD_MOCK_VERSION==="1"}function fr(e){let t=e.length,r=5381,i=52711;for(;t--;){let n=e.charCodeAt(t);r=r*33^n,i=i*33^n}return(r>>>0)*4096+(i>>>0)}function pr(e,t){let r="unknownFile";if(t.file.opts.filename){let l=t.file.opts.filename;r=(0,je.basename)(l);let a=l.split("/"),y=a.indexOf("node_modules");y!==-1&&(r=`${a[y+1]}_${r}`)}let i=`${r}${t.workletNumber++}`,n="";(0,o.isObjectMethod)(e.node)&&(0,o.isIdentifier)(e.node.key)?n=e.node.key.name:((0,o.isFunctionDeclaration)(e.node)||(0,o.isFunctionExpression)(e.node))&&(0,o.isIdentifier)(e.node.id)&&(n=e.node.id.name);let s=n?(0,o.toIdentifier)(`${n}_${i}`):(0,o.toIdentifier)(i);return n=n||(0,o.toIdentifier)(i),{workletName:s,reactName:n}}function mr(e,t){let r=new Map,i=new Map;return(0,rr.traverse)(e,{Identifier(n){if(!n.isReferencedIdentifier())return;let s=n.node.name;if(ir.globals.has(s)||"id"in t.node&&t.node.id&&t.node.id.name===s)return;let l=n.parent;if((0,o.isMemberExpression)(l)&&l.property===n.node&&!l.computed||(0,o.isObjectProperty)(l)&&(0,o.isObjectExpression)(n.parentPath.parent)&&n.node!==l.value)return;let a=n.scope;for(;a!=null;){if(a.bindings[s]!=null)return;a=a.parent}r.set(s,n.node),i.set(s,!1)}}),t.traverse({Identifier(n){if(!n.isReferencedIdentifier())return;let s=r.get(n.node.name);!s||i.get(n.node.name)||(s.loc=n.node.loc,i.set(n.node.name,!0))}}),Array.from(r.values())}var br=[require.resolve("@babel/plugin-transform-shorthand-properties"),require.resolve("@babel/plugin-transform-arrow-functions"),require.resolve("@babel/plugin-transform-optional-chaining"),require.resolve("@babel/plugin-transform-nullish-coalescing-operator"),[require.resolve("@babel/plugin-transform-template-literals"),{loose:!0}]]});var Pe=p(Y=>{"use strict";Object.defineProperty(Y,"__esModule",{value:!0});Y.makeWorkletFactoryCall=void 0;var yr=require("@babel/types"),kr=De();function gr(e,t){let r=(0,kr.makeWorkletFactory)(e,t),i=(0,yr.callExpression)(r,[]);return _r(e,i),i}Y.makeWorkletFactoryCall=gr;function _r(e,t){let r=e.node.loc;r&&(t.callee.loc={filename:r.filename,identifierName:r.identifierName,start:r.start,end:r.start})}});var K=p(F=>{"use strict";Object.defineProperty(F,"__esModule",{value:!0});F.substituteObjectMethodWithObjectProperty=F.processWorklet=F.processIfWithWorkletDirective=void 0;var ue=require("@babel/types"),vr=x(),Or=q(),hr=Pe();function Me(e,t){return!(0,ue.isBlockStatement)(e.node.body)||!Er(e.node.body.directives)?!1:(Re(e,t),!0)}F.processIfWithWorkletDirective=Me;function Re(e,t){t.opts.processNestedWorklets&&e.traverse({[vr.WorkletizableFunction](i,n){Me(i,n)}},t);let r=(0,hr.makeWorkletFactoryCall)(e,t);Ir(e,r)}F.processWorklet=Re;function Er(e){return e.some(t=>(0,ue.isDirectiveLiteral)(t.value)&&t.value.value==="worklet")}function Ir(e,t){var r;if(e.isObjectMethod())Ae(e,t);else{let i="id"in e.node?(r=e.node.id)===null||r===void 0?void 0:r.name:void 0;(0,Or.replaceWithFactoryCall)(e,i,t)}}function Ae(e,t){let r=(0,ue.objectProperty)(e.node.key,t);e.replaceWith(r)}F.substituteObjectMethodWithObjectProperty=Ae});var ze=p(Q=>{"use strict";Object.defineProperty(Q,"__esModule",{value:!0});Q.processWorkletizableObject=void 0;var Sr=x(),qe=K();function xr(e,t){let r=e.get("properties");for(let i of r)if(i.isObjectMethod())(0,qe.processWorklet)(i,t);else if(i.isObjectProperty()){let n=i.get("value");(0,Sr.isWorkletizableFunctionPath)(n)&&(0,qe.processWorklet)(n,t)}else throw new Error(`[Reanimated] '${i.type}' as to-be workletized argument is not supported for object hooks.`)}Q.processWorkletizableObject=xr});var Le=p(ee=>{"use strict";Object.defineProperty(ee,"__esModule",{value:!0});ee.findReferencedWorklet=void 0;var T=x();function de(e,t,r){let i=e.node.name,s=e.scope.getBinding(i);return s?t&&s.path.isFunctionDeclaration()?s.path:s.constant?Wr(s,t,r):Cr(s,t,r):void 0}ee.findReferencedWorklet=de;function Wr(e,t,r){let i=e.path;if(!i.isVariableDeclarator())return;let n=i.get("init");if(t&&(0,T.isWorkletizableFunctionPath)(n)||r&&(0,T.isWorkletizableObjectPath)(n))return n;if(n.isIdentifier()&&n.isReferencedIdentifier())return de(n,t,r)}function Cr(e,t,r){let i=e.constantViolations.reverse().find(s=>s.isAssignmentExpression()&&(t&&(0,T.isWorkletizableFunctionPath)(s.get("right"))||r&&(0,T.isWorkletizableObjectPath)(s.get("right"))));if(!i||!i.isAssignmentExpression())return;let n=i.get("right");if(t&&(0,T.isWorkletizableFunctionPath)(n)||r&&(0,T.isWorkletizableObjectPath)(n))return n;if(n.isIdentifier()&&n.isReferencedIdentifier())return de(n,t,r)}});var He=p(N=>{"use strict";Object.defineProperty(N,"__esModule",{value:!0});N.processCalleesAutoworkletizableCallbacks=N.processIfAutoworkletizableCallback=void 0;var Te=require("@babel/types"),fe=Oe(),wr=Ie(),Fr=ze(),jr=Le(),te=x(),Ge=K(),Ne=new Set(["useAnimatedGestureHandler","useAnimatedScrollHandler"]),Be=new Set(["useFrameCallback","useAnimatedStyle","useAnimatedProps","createAnimatedPropAdapter","useDerivedValue","useAnimatedScrollHandler","useAnimatedReaction","useWorkletCallback","withTiming","withSpring","withDecay","withRepeat","runOnUI","executeOnUIRuntimeSync"]),Dr=new Map([["useAnimatedGestureHandler",[0]],["useFrameCallback",[0]],["useAnimatedStyle",[0]],["useAnimatedProps",[0]],["createAnimatedPropAdapter",[0]],["useDerivedValue",[0]],["useAnimatedScrollHandler",[0]],["useAnimatedReaction",[0,1]],["useWorkletCallback",[0]],["withTiming",[2]],["withSpring",[2]],["withDecay",[1]],["withRepeat",[3]],["runOnUI",[0]],["executeOnUIRuntimeSync",[0]],...Array.from(fe.gestureHandlerBuilderMethods).map(e=>[e,[0]])]);function Pr(e,t){return(0,fe.isGestureHandlerEventCallback)(e)||(0,wr.isLayoutAnimationCallback)(e)?((0,Ge.processWorklet)(e,t),!0):!1}N.processIfAutoworkletizableCallback=Pr;function Mr(e,t){let r=(0,Te.isSequenceExpression)(e.node.callee)?e.node.callee.expressions[e.node.callee.expressions.length-1]:e.node.callee,i="name"in r?r.name:"property"in r&&"name"in r.property?r.property.name:void 0;if(i!==void 0){if(Be.has(i)||Ne.has(i)){let n=Be.has(i),s=Ne.has(i),l=Dr.get(i),a=e.get("arguments").filter((y,d)=>l.includes(d));Ue(a,t,n,s)}else if(!(0,Te.isV8IntrinsicIdentifier)(r)&&(0,fe.isGestureObjectEventCallbackMethod)(r)){let n=e.get("arguments");Ue(n,t,!0,!0)}}}N.processCalleesAutoworkletizableCallbacks=Mr;function Ue(e,t,r,i){e.forEach(n=>{let s=Rr(n,r,i);s&&((0,te.isWorkletizableFunctionPath)(s)?(0,Ge.processWorklet)(s,t):(0,te.isWorkletizableObjectPath)(s)&&(0,Fr.processWorkletizableObject)(s,t))})}function Rr(e,t,r){if(t&&(0,te.isWorkletizableFunctionPath)(e)||r&&(0,te.isWorkletizableObjectPath)(e))return e;if(e.isIdentifier()&&e.isReferencedIdentifier())return(0,jr.findReferencedWorklet)(e,t,r)}});var Xe=p(B=>{"use strict";var Ze=B&&B.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(B,"__esModule",{value:!0});B.processIfWorkletClass=void 0;var Ar=Ze(require("@babel/generator")),qr=Ze(require("@babel/traverse")),c=require("@babel/types"),re=require("assert"),zr=$(),Lr=x(),Tr=q(),Ve="__workletClass";function Nr(e,t){return Qr(e,t)?(Jr(e.node.body),Br(e,t),!0):!1}B.processIfWorkletClass=Nr;function Br(e,t){(0,re.strict)(e.node.id);let r=e.node.id.name,i=Ur(e.node,t);Xr(i),Gr(i.program.body),Hr(i.program.body,r),i.program.body.push((0,c.returnStatement)((0,c.identifier)(r)));let n=(0,c.functionExpression)(null,[],(0,c.blockStatement)([...i.program.body])),s=(0,c.callExpression)(n,[]);(0,Tr.replaceWithFactoryCall)(e,r,s)}function Ur(e,t){var r;let i=(0,Ar.default)(e).code,n=(0,zr.workletTransformSync)(i,{extraPlugins:["@babel/plugin-transform-class-properties","@babel/plugin-transform-classes","@babel/plugin-transform-unicode-regex",...(r=t.opts.extraPlugins)!==null&&r!==void 0?r:[]],extraPresets:t.opts.extraPresets,filename:t.file.opts.filename,ast:!0,babelrc:!1,configFile:!1});return(0,re.strict)(n&&n.ast),n.ast}function Gr(e){e.forEach(t=>{if((0,c.isFunctionDeclaration)(t)){let r=(0,c.directive)((0,c.directiveLiteral)("worklet"));t.body.directives.push(r)}})}function Hr(e,t){let r=t+Lr.workletClassFactorySuffix,i=Zr(e,t),s=e[i].declarations[0].init,l=(0,c.functionDeclaration)((0,c.identifier)(r),[],(0,c.blockStatement)([(0,c.variableDeclaration)("const",[(0,c.variableDeclarator)((0,c.identifier)(t),s)]),(0,c.expressionStatement)((0,c.assignmentExpression)("=",(0,c.memberExpression)((0,c.identifier)(t),(0,c.identifier)(r)),(0,c.identifier)(r))),(0,c.returnStatement)((0,c.identifier)(t))],[(0,c.directive)((0,c.directiveLiteral)("worklet"))])),a=(0,c.variableDeclaration)("const",[(0,c.variableDeclarator)((0,c.identifier)(t),(0,c.callExpression)((0,c.identifier)(r),[]))]);e.splice(i,1,l,a)}function Zr(e,t){let r=e.findIndex(i=>(0,c.isVariableDeclaration)(i)&&i.declarations.some(n=>(0,c.isIdentifier)(n.id)&&n.id.name===t));return(0,re.strict)(r>=0),r}function Vr(e){return e.body.some(t=>(0,c.isClassProperty)(t)&&(0,c.isIdentifier)(t.key)&&t.key.name===Ve)}function Jr(e){e.body=e.body.filter(t=>!(0,c.isClassProperty)(t)||!(0,c.isIdentifier)(t.key)||t.key.name!==Ve)}function Xr(e){let t=$r(e),r=Yr(t),i=t.map(a=>a.index),n=r.map(a=>a.index),s=e.program.body,l=[...s];for(let a=0;a<t.length;a++){let y=n[a],d=i[a],j=l[y];s[d]=j}}function $r(e){let t=[];return(0,qr.default)(e,{Program:{enter:r=>{r.get("body").forEach((n,s)=>{var l;let a=n.getBindingIdentifiers();if(!n.isFunctionDeclaration()||!(!((l=n.node.id)===null||l===void 0)&&l.name))return;let y={name:n.node.id.name,index:s,dependencies:new Set};t.push(y),n.traverse({Identifier(d){Kr(d,a,n)&&y.dependencies.add(d.node.name)}})})}}}),t}function Yr(e){let t=[],r=new Set;for(let i of e)Je(i,e,t,r);return t}function Je(e,t,r,i){if(i.has(e.name))throw new Error("Cycle detected. This should never happen.");if(!r.find(n=>n.name===e.name)){i.add(e.name);for(let n of e.dependencies)if(!r.find(s=>s.name===n)){let s=t.find(l=>l.name===n);(0,re.strict)(s),Je(s,t,r,i)}r.push(e),i.delete(e.name)}}function Kr(e,t,r){return e.isReferencedIdentifier()&&!(e.node.name in t)&&!r.scope.hasOwnBinding(e.node.name)&&r.scope.hasReference(e.node.name)}function Qr(e,t){var r;let i=(r=e.node.id)===null||r===void 0?void 0:r.name,n=e.node;if(!i)return!1;let s=Vr(n.body),l=t.classesToWorkletize.some(d=>d.node===n),a=e.parentPath.isProgram()&&t.classesToWorkletize.some(d=>d.name===i);return t.classesToWorkletize=t.classesToWorkletize.filter(d=>d.node!==n&&d.name!==i),s||l||a}});var pe=p(h=>{"use strict";Object.defineProperty(h,"__esModule",{value:!0});h.isContextObject=h.processIfWorkletContextObject=h.contextObjectMarker=void 0;var O=require("@babel/types");h.contextObjectMarker="__workletContextObject";function en(e,t){return $e(e.node)?(rn(e.node),tn(e.node),!0):!1}h.processIfWorkletContextObject=en;function $e(e){return e.properties.some(t=>(0,O.isObjectProperty)(t)&&(0,O.isIdentifier)(t.key)&&t.key.name===h.contextObjectMarker)}h.isContextObject=$e;function tn(e){let t=(0,O.functionExpression)(null,[],(0,O.blockStatement)([(0,O.returnStatement)((0,O.cloneNode)(e))],[(0,O.directive)((0,O.directiveLiteral)("worklet"))]));e.properties.push((0,O.objectProperty)((0,O.identifier)(`${h.contextObjectMarker}Factory`),t))}function rn(e){e.properties=e.properties.filter(t=>!((0,O.isObjectProperty)(t)&&(0,O.isIdentifier)(t.key)&&t.key.name===h.contextObjectMarker))}});var tt=p(U=>{"use strict";Object.defineProperty(U,"__esModule",{value:!0});U.isImplicitContextObject=U.processIfWorkletFile=void 0;var m=require("@babel/types"),Ye=pe(),Ke=x();function nn(e,t){return e.node.directives.some(r=>r.value.value==="worklet")?(e.node.directives=e.node.directives.filter(r=>r.value.value!=="worklet"),on(e,t),!0):!1}U.processIfWorkletFile=nn;function on(e,t){let r=e.get("body");pn(e.node),r.forEach(i=>{let n=sn(i);me(n,t)})}function sn(e){return e.isExportNamedDeclaration()||e.isExportDefaultDeclaration()?e.get("declaration"):e}function me(e,t){var r;(0,Ke.isWorkletizableFunctionPath)(e)?(e.isArrowFunctionExpression()&&cn(e.node),Qe(e.node.body)):(0,Ke.isWorkletizableObjectPath)(e)?et(e)?un(e.node):ln(e,t):e.isVariableDeclaration()?an(e,t):e.isClassDeclaration()&&(fn(e.node.body),!((r=e.node.id)===null||r===void 0)&&r.name&&t.classesToWorkletize.push({node:e.node,name:e.node.id.name}))}function an(e,t){e.get("declarations").forEach(i=>{let n=i.get("init");n.isExpression()&&me(n,t)})}function ln(e,t){e.get("properties").forEach(i=>{if(i.isObjectMethod())Qe(i.node.body);else if(i.isObjectProperty()){let n=i.get("value");me(n,t)}})}function cn(e){(0,m.isBlockStatement)(e.body)||(e.body=(0,m.blockStatement)([(0,m.returnStatement)(e.body)]))}function Qe(e){e.directives.some(t=>t.value.value==="worklet")||e.directives.push((0,m.directive)((0,m.directiveLiteral)("worklet")))}function un(e){e.properties.some(t=>(0,m.isObjectProperty)(t)&&(0,m.isIdentifier)(t.key)&&t.key.name===Ye.contextObjectMarker)||e.properties.push((0,m.objectProperty)((0,m.identifier)(`${Ye.contextObjectMarker}`),(0,m.booleanLiteral)(!0)))}function et(e){return e.get("properties").some(r=>r.isObjectMethod()?dn(r):!1)}U.isImplicitContextObject=et;function dn(e){let t=!1;return e.traverse({ThisExpression(r){t=!0,r.stop()}}),t}function fn(e){e.body.push((0,m.classProperty)((0,m.identifier)("__workletClass"),(0,m.booleanLiteral)(!0)))}function pn(e){let t=e.body,r=t.length,i=0;for(;i<r;){let n=t[i];if(!mn(n)){i++;continue}let s=t.splice(i,1);t.push(...s),r--}}function mn(e){return(0,m.isExpressionStatement)(e)&&(0,m.isAssignmentExpression)(e.expression)&&(0,m.isMemberExpression)(e.expression.left)&&(0,m.isIdentifier)(e.expression.left.object)&&e.expression.left.object.name==="exports"}});var rt=p(ne=>{"use strict";Object.defineProperty(ne,"__esModule",{value:!0});ne.processInlineStylesWarning=void 0;var f=require("@babel/types"),be=require("assert"),bn=q();function yn(e){return(0,f.callExpression)((0,f.arrowFunctionExpression)([],(0,f.blockStatement)([(0,f.expressionStatement)((0,f.callExpression)((0,f.memberExpression)((0,f.identifier)("console"),(0,f.identifier)("warn")),[(0,f.callExpression)((0,f.memberExpression)((0,f.callExpression)((0,f.identifier)("require"),[(0,f.stringLiteral)("react-native-reanimated")]),(0,f.identifier)("getUseOfValueInStyleWarning")),[])])),(0,f.returnStatement)(e.node)])),[])}function kn(e){e.isMemberExpression()&&(0,f.isIdentifier)(e.node.property)&&e.node.property.name==="value"&&e.replaceWith(yn(e))}function gn(e){if((0,f.isArrayExpression)(e.node)){let t=e.get("elements");(0,be.strict)(Array.isArray(t),"[Reanimated] `elements` should be an array.");for(let r of t)r.isObjectExpression()&&ye(r)}}function ye(e){let t=e.get("properties");for(let r of t)if(r.isObjectProperty()){let i=r.get("value");(0,f.isIdentifier)(r.node.key)&&r.node.key.name==="transform"?gn(i):kn(i)}}function _n(e,t){if((0,bn.isRelease)()||t.opts.disableInlineStylesWarning||e.node.name.name!=="style"||!(0,f.isJSXExpressionContainer)(e.node.value))return;let r=e.get("value").get("expression");if((0,be.strict)(!Array.isArray(r),"[Reanimated] `expression` should not be an array."),r.isArrayExpression()){let i=r.get("elements");(0,be.strict)(Array.isArray(i),"[Reanimated] `elements` should be an array.");for(let n of i)n.isObjectExpression()&&ye(n)}else r.isObjectExpression()&&ye(r)}ne.processInlineStylesWarning=_n});var it=p(ie=>{"use strict";Object.defineProperty(ie,"__esModule",{value:!0});ie.substituteWebCallExpression=void 0;var nt=require("@babel/types");function vn(e){let t=e.node.callee;if((0,nt.isIdentifier)(t)){let r=t.name;(r==="isWeb"||r==="shouldBeUseWeb")&&e.replaceWith((0,nt.booleanLiteral)(!0))}}ie.substituteWebCallExpression=vn});Object.defineProperty(exports,"__esModule",{value:!0});var ot=He(),On=Xe(),hn=pe(),En=tt(),In=le(),Sn=rt(),xn=x(),Wn=it(),Cn=K();module.exports=function(){function e(t){try{t()}catch(r){throw new Error(`[Reanimated] Babel plugin exception: ${r}`)}}return{name:"reanimated",pre(){e(()=>{(0,In.initializeState)(this)})},visitor:{CallExpression:{enter(t,r){e(()=>{(0,ot.processCalleesAutoworkletizableCallbacks)(t,r),r.opts.substituteWebPlatformChecks&&(0,Wn.substituteWebCallExpression)(t)})}},[xn.WorkletizableFunction]:{enter(t,r){e(()=>{(0,Cn.processIfWithWorkletDirective)(t,r)||(0,ot.processIfAutoworkletizableCallback)(t,r)})}},ObjectExpression:{enter(t,r){e(()=>{(0,hn.processIfWorkletContextObject)(t,r)})}},ClassDeclaration:{enter(t,r){e(()=>{(0,On.processIfWorkletClass)(t,r)})}},Program:{enter(t,r){e(()=>{(0,En.processIfWorkletFile)(t,r)})}},JSXAttribute:{enter(t,r){e(()=>(0,Sn.processInlineStylesWarning)(t,r))}}}}};
//# sourceMappingURL=index.js.map
