import type { ILayoutAnimationBuilder, LayoutAnimationFunction, StyleProps } from './commonTypes';
import type { NestedArray } from './createAnimatedComponent/commonTypes';
export declare function maybeBuild(layoutAnimationOrBuilder: ILayoutAnimationBuilder | LayoutAnimationFunction | Keyframe, style: NestedArray<StyleProps> | undefined, displayName: string): LayoutAnimationFunction | Keyframe;
//# sourceMappingURL=animationBuilder.d.ts.map