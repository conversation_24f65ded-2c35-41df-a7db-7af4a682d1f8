{"version": 3, "names": ["ReanimatedError", "logger", "PropsAllowlists", "createReactDOMStyle", "createTextShadowValue", "createTransformValue", "createJSReanimatedModule", "global", "_makeShareableClone", "_scheduleHostFunctionOnJS", "_scheduleOnRuntime", "_updatePropsJS", "updates", "viewRef", "isAnimatedProps", "component", "getAnimatableRef", "rawStyles", "Object", "keys", "reduce", "acc", "key", "value", "index", "setNativeProps", "undefined", "style", "updatePropsDOM", "props", "length", "for<PERSON>ach", "<PERSON><PERSON><PERSON>", "replace", "m", "toLowerCase", "_touchableNode", "setAttribute", "componentName", "className", "warn", "newProps", "uiProps", "isNativeProp", "previousStyle", "currentStyle", "domStyle", "Array", "isArray", "transform", "textShadowColor", "textShadowRadius", "textShadowOffset", "textShadow", "nodeName", "propName", "NATIVE_THREAD_PROPS_WHITELIST"], "sourceRoot": "../../../../src", "sources": ["ReanimatedModule/js-reanimated/index.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,eAAe,QAAQ,iBAAc;AAC9C,SAASC,MAAM,QAAQ,uBAAc;AACrC,SAASC,eAAe,QAAQ,0BAAuB;AACvD,SACEC,mBAAmB,EACnBC,qBAAqB,EACrBC,oBAAoB,QACf,YAAY;AAEnB,SAASC,wBAAwB,QAAQ,mBAAgB;;AAEzD;AACAC,MAAM,CAACC,mBAAmB,GAAG,MAAM;EACjC,MAAM,IAAIR,eAAe,CACvB,kEACF,CAAC;AACH,CAAC;AAEDO,MAAM,CAACE,yBAAyB,GAAG,MAAM;EACvC,MAAM,IAAIT,eAAe,CACvB,4DACF,CAAC;AACH,CAAC;AAEDO,MAAM,CAACG,kBAAkB,GAAG,MAAM;EAChC,MAAM,IAAIV,eAAe,CACvB,iEACF,CAAC;AACH,CAAC;AAuBD;AACA,OAAO,MAAMW,cAAc,GAAGA,CAE5BC,OAAwC,EACxCC,OAEC,EACDC,eAAyB,KAChB;EACT,IAAID,OAAO,EAAE;IACX,MAAME,SAAS,GAAGF,OAAO,CAACG,gBAAgB,GACtCH,OAAO,CAACG,gBAAgB,CAAC,CAAC,GAC1BH,OAAO;IACX,MAAM,CAACI,SAAS,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACP,OAAO,CAAC,CAACQ,MAAM,CAC7C,CAACC,GAAqC,EAAEC,GAAG,KAAK;MAC9C,MAAMC,KAAK,GAAGX,OAAO,CAACU,GAAG,CAAC;MAC1B,MAAME,KAAK,GAAG,OAAOD,KAAK,KAAK,UAAU,GAAG,CAAC,GAAG,CAAC;MACjDF,GAAG,CAACG,KAAK,CAAC,CAACF,GAAG,CAAC,GAAGC,KAAK;MACvB,OAAOF,GAAG;IACZ,CAAC,EACD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CACT,CAAC;IAED,IAAI,OAAON,SAAS,CAACU,cAAc,KAAK,UAAU,EAAE;MAClD;MACA;MACA;MACAA,cAAc,CAACV,SAAS,EAAEE,SAAS,EAAEH,eAAe,CAAC;IACvD,CAAC,MAAM,IACLX,mBAAmB,KAAKuB,SAAS,IACjCX,SAAS,CAACY,KAAK,KAAKD,SAAS,EAC7B;MACA;MACA;MACAE,cAAc,CAACb,SAAS,EAAEE,SAAS,EAAEH,eAAe,CAAC;IACvD,CAAC,MAAM,IAAII,MAAM,CAACC,IAAI,CAACJ,SAAS,CAACc,KAAK,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MAClDZ,MAAM,CAACC,IAAI,CAACJ,SAAS,CAACc,KAAK,CAAC,CAACE,OAAO,CAAET,GAAG,IAAK;QAC5C,IAAI,CAACL,SAAS,CAACK,GAAG,CAAC,EAAE;UACnB;QACF;QACA,MAAMU,SAAS,GAAGV,GAAG,CAACW,OAAO,CAAC,QAAQ,EAAGC,CAAC,IAAK,GAAG,GAAGA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;QACrEpB,SAAS,CAACqB,cAAc,CAACC,YAAY,CAACL,SAAS,EAAEf,SAAS,CAACK,GAAG,CAAC,CAAC;MAClE,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAMgB,aAAa,GACjB,WAAW,IAAIvB,SAAS,GAAGA,SAAS,EAAEwB,SAAS,GAAG,EAAE;MACtDtC,MAAM,CAACuC,IAAI,CACT,iDAAiDF,aAAa,EAChE,CAAC;IACH;EACF;AACF,CAAC;AAED,MAAMb,cAAc,GAAGA,CACrBV,SAAwD,EACxD0B,QAAoB,EACpB3B,eAAyB,KAChB;EACT,IAAIA,eAAe,EAAE;IACnB,MAAM4B,OAAgC,GAAG,CAAC,CAAC;IAC3C,KAAK,MAAMpB,GAAG,IAAImB,QAAQ,EAAE;MAC1B,IAAIE,YAAY,CAACrB,GAAG,CAAC,EAAE;QACrBoB,OAAO,CAACpB,GAAG,CAAC,GAAGmB,QAAQ,CAACnB,GAAG,CAAC;MAC9B;IACF;IACA;IACA;IACAP,SAAS,CAACU,cAAc,GAAGiB,OAAO,CAAC;EACrC;EAEA,MAAME,aAAa,GAAG7B,SAAS,CAAC6B,aAAa,GAAG7B,SAAS,CAAC6B,aAAa,GAAG,CAAC,CAAC;EAC5E,MAAMC,YAAY,GAAG;IAAE,GAAGD,aAAa;IAAE,GAAGH;EAAS,CAAC;EACtD1B,SAAS,CAAC6B,aAAa,GAAGC,YAAY;EAEtC9B,SAAS,CAACU,cAAc,GAAG;IAAEE,KAAK,EAAEkB;EAAa,CAAC,CAAC;AACrD,CAAC;AAED,MAAMjB,cAAc,GAAGA,CACrBb,SAA8C,EAC9CY,KAAiB,EACjBb,eAAyB,KAChB;EACT,MAAM8B,aAAa,GAAI7B,SAAS,CAA2B6B,aAAa,GACnE7B,SAAS,CAA2B6B,aAAa,GAClD,CAAC,CAAC;EACN,MAAMC,YAAY,GAAG;IAAE,GAAGD,aAAa;IAAE,GAAGjB;EAAM,CAAC;EAClDZ,SAAS,CAA2B6B,aAAa,GAAGC,YAAY;EAEjE,MAAMC,QAAQ,GAAG3C,mBAAmB,CAAC0C,YAAY,CAAC;EAClD,IAAIE,KAAK,CAACC,OAAO,CAACF,QAAQ,CAACG,SAAS,CAAC,IAAI5C,oBAAoB,KAAKqB,SAAS,EAAE;IAC3EoB,QAAQ,CAACG,SAAS,GAAG5C,oBAAoB,CAACyC,QAAQ,CAACG,SAAS,CAAC;EAC/D;EAEA,IACE7C,qBAAqB,KAAKsB,SAAS,KAClCoB,QAAQ,CAACI,eAAe,IACvBJ,QAAQ,CAACK,gBAAgB,IACzBL,QAAQ,CAACM,gBAAgB,CAAC,EAC5B;IACAN,QAAQ,CAACO,UAAU,GAAGjD,qBAAqB,CAAC;MAC1C8C,eAAe,EAAEJ,QAAQ,CAACI,eAAe;MACzCE,gBAAgB,EAAEN,QAAQ,CAACM,gBAAgB;MAC3CD,gBAAgB,EAAEL,QAAQ,CAACK;IAC7B,CAAC,CAAC;EACJ;EAEA,KAAK,MAAM7B,GAAG,IAAIwB,QAAQ,EAAE;IAC1B,IAAIhC,eAAe,EAAE;MACnB;MACA;MACA;MACA,IAAKC,SAAS,CAAiBuC,QAAQ,KAAK,OAAO,IAAIhC,GAAG,KAAK,MAAM,EAAE;QACpEP,SAAS,CAAsBQ,KAAK,GAAGuB,QAAQ,CAACxB,GAAG,CAAW;MACjE,CAAC,MAAM;QACJP,SAAS,CAAiBsB,YAAY,CAACf,GAAG,EAAEwB,QAAQ,CAACxB,GAAG,CAAC,CAAC;MAC7D;IACF,CAAC,MAAM;MACJP,SAAS,CAACY,KAAK,CAAgBL,GAAG,CAAC,GAAGwB,QAAQ,CAACxB,GAAG,CAAC;IACtD;EACF;AACF,CAAC;AAED,SAASqB,YAAYA,CAACY,QAAgB,EAAW;EAC/C,OAAO,CAAC,CAACrD,eAAe,CAACsD,6BAA6B,CAACD,QAAQ,CAAC;AAClE", "ignoreList": []}