{"version": 3, "names": ["SLOPE_FACTOR", "VELOCITY_EPS", "DERIVATIVE_EPS", "rubberBandDecay", "animation", "now", "config", "lastTimestamp", "startTimestamp", "current", "velocity", "deltaTime", "Math", "min", "clampIndex", "abs", "clamp", "derivative", "v", "exp", "deceleration", "rubberBandFactor", "springActive", "velocityFactor"], "sourceRoot": "../../../../src", "sources": ["animation/decay/rubberBandDecay.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,YAAY,EAAEC,YAAY,QAAQ,YAAS;AAEpD,MAAMC,cAAc,GAAG,GAAG;AAE1B,OAAO,SAASC,eAAeA,CAC7BC,SAA8B,EAC9BC,GAAW,EACXC,MAA6B,EACpB;EACT,SAAS;;EACT,MAAM;IAAEC,aAAa;IAAEC,cAAc;IAAEC,OAAO;IAAEC;EAAS,CAAC,GAAGN,SAAS;EAEtE,MAAMO,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACR,GAAG,GAAGE,aAAa,EAAE,EAAE,CAAC;EACnD,MAAMO,UAAU,GACdF,IAAI,CAACG,GAAG,CAACN,OAAO,GAAGH,MAAM,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGJ,IAAI,CAACG,GAAG,CAACN,OAAO,GAAGH,MAAM,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,GACrE,CAAC,GACD,CAAC;EAEP,IAAIC,UAAU,GAAG,CAAC;EAClB,IAAIR,OAAO,GAAGH,MAAM,CAACU,KAAK,CAAC,CAAC,CAAC,IAAIP,OAAO,GAAGH,MAAM,CAACU,KAAK,CAAC,CAAC,CAAC,EAAE;IAC1DC,UAAU,GAAGR,OAAO,GAAGH,MAAM,CAACU,KAAK,CAACF,UAAU,CAAC;EACjD;EAEA,MAAMI,CAAC,GACLR,QAAQ,GACNE,IAAI,CAACO,GAAG,CACN,EAAE,CAAC,GAAGb,MAAM,CAACc,YAAY,CAAC,IAAIf,GAAG,GAAGG,cAAc,CAAC,GAAGR,YACxD,CAAC,GACHiB,UAAU,GAAGX,MAAM,CAACe,gBAAgB;EAEtC,IAAIT,IAAI,CAACG,GAAG,CAACE,UAAU,CAAC,GAAGf,cAAc,EAAE;IACzCE,SAAS,CAACkB,YAAY,GAAG,IAAI;EAC/B,CAAC,MAAM,IAAIlB,SAAS,CAACkB,YAAY,EAAE;IACjClB,SAAS,CAACK,OAAO,GAAGH,MAAM,CAACU,KAAK,CAACF,UAAU,CAAC;IAC5C,OAAO,IAAI;EACb,CAAC,MAAM,IAAIF,IAAI,CAACG,GAAG,CAACG,CAAC,CAAC,GAAGjB,YAAY,EAAE;IACrC,OAAO,IAAI;EACb;EAEAG,SAAS,CAACK,OAAO,GAAGA,OAAO,GAAIS,CAAC,GAAGZ,MAAM,CAACiB,cAAc,GAAGZ,SAAS,GAAI,IAAI;EAC5EP,SAAS,CAACM,QAAQ,GAAGQ,CAAC;EACtBd,SAAS,CAACG,aAAa,GAAGF,GAAG;EAC7B,OAAO,KAAK;AACd", "ignoreList": []}