{"version": 3, "names": ["withClamp", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "withRepeat", "withSequence", "with<PERSON><PERSON><PERSON>", "withStyleAnimation", "withTiming", "cancelAnimation", "defineAnimation", "initialUpdaterRun"], "sourceRoot": "../../../src", "sources": ["animation/index.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,QAAQ,YAAS;AAUnC,SAASC,SAAS,QAAQ,kBAAS;AACnC,SAASC,SAAS,QAAQ,YAAS;AACnC,SAASC,UAAU,QAAQ,aAAU;AACrC,SAASC,YAAY,QAAQ,eAAY;AACzC,SAASC,UAAU,QAAQ,aAAU;AAErC,SAASC,kBAAkB,QAAQ,qBAAkB;AAErD,SAASC,UAAU,QAAQ,aAAU;AACrC,SAASC,eAAe,EAAEC,eAAe,EAAEC,iBAAiB,QAAQ,WAAQ", "ignoreList": []}