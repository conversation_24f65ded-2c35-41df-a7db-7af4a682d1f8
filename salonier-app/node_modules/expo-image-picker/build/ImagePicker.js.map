{"version": 3, "file": "ImagePicker.js", "sourceRoot": "", "sources": ["../src/ImagePicker.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,UAAU,EACV,oBAAoB,EAIpB,gBAAgB,EAChB,mBAAmB,GACpB,MAAM,mBAAmB,CAAC;AAE3B,OAAO,mBAAmB,MAAM,uBAAuB,CAAC;AAQxD,OAAO,EAAE,oBAAoB,EAAE,MAAM,SAAS,CAAC;AAE/C,SAAS,eAAe,CAAC,OAA2B;IAClD,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;IAEtD,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;QACnB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC;QAEtB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACrB,MAAM,IAAI,UAAU,CAClB,sBAAsB,EACtB,+BAA+B,CAAC,IAAI,CAAC,6BAA6B,CACnE,CAAC;QACJ,CAAC;IACH,CAAC;IAED,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC;QAC5C,MAAM,IAAI,UAAU,CAClB,sBAAsB,EACtB,2BAA2B,OAAO,oCAAoC,CACvE,CAAC;IACJ,CAAC;IAED,IAAI,gBAAgB,IAAI,gBAAgB,GAAG,CAAC,EAAE,CAAC;QAC7C,MAAM,IAAI,UAAU,CAClB,sBAAsB,EACtB,oCAAoC,gBAAgB,kCAAkC,CACvF,CAAC;IACJ,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,cAAc;AACd;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,yBAAyB;IAC7C,OAAO,mBAAmB,CAAC,yBAAyB,EAAE,CAAC;AACzD,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,+BAA+B,CACnD,YAAqB,KAAK;IAE1B,OAAO,mBAAmB,CAAC,+BAA+B,CAAC,SAAS,CAAC,CAAC;AACxE,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,6BAA6B;IACjD,OAAO,mBAAmB,CAAC,6BAA6B,EAAE,CAAC;AAC7D,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,mCAAmC,CACvD,YAAqB,KAAK;IAE1B,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,mCAAmC,CAAC;IAClF,OAAO,iBAAiB,CAAC,SAAS,CAAC,CAAC;AACtC,CAAC;AAED,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,0BAA0B,GAAG,oBAAoB,CAG5D;IACA,4FAA4F;IAC5F,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,+BAA+B,CAAC,OAAO,EAAE,SAAS,CAAC;IAC3E,aAAa,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,mCAAmC,CAAC,OAAO,EAAE,SAAS,CAAC;CACpF,CAAC,CAAC;AAEH,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,oBAAoB,CAAC;IACvD,SAAS,EAAE,yBAAyB;IACpC,aAAa,EAAE,6BAA6B;CAC7C,CAAC,CAAC;AAEH,cAAc;AACd;;;;;;;;;;GAUG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB;IAGzC,IAAI,mBAAmB,CAAC,qBAAqB,EAAE,CAAC;QAC9C,OAAO,mBAAmB,CAAC,qBAAqB,EAAE,CAAC;IACrD,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;;GAcG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,UAA8B,EAAE;IAEhC,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,CAAC;QAC3C,MAAM,IAAI,mBAAmB,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;IACpE,CAAC;IACD,MAAM,aAAa,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;IACpD,OAAO,MAAM,mBAAmB,CAAC,iBAAiB,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,CAAC;AACrF,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB,CAC3C,UAA8B,EAAE;IAEhC,MAAM,aAAa,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;IAEpD,IAAI,CAAC,mBAAmB,CAAC,uBAAuB,EAAE,CAAC;QACjD,MAAM,IAAI,mBAAmB,CAAC,aAAa,EAAE,yBAAyB,CAAC,CAAC;IAC1E,CAAC;IACD,IAAI,aAAa,EAAE,aAAa,IAAI,aAAa,CAAC,uBAAuB,EAAE,CAAC;QAC1E,OAAO,CAAC,IAAI,CACV,qHAAqH;YACnH,2FAA2F;YAC3F,sBAAsB,CACzB,CAAC;IACJ,CAAC;IACD,OAAO,MAAM,mBAAmB,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;AAC1E,CAAC;AAED,cAAc,qBAAqB,CAAC;AAGpC,OAAO,EAAE,gBAAgB,EAAE,CAAC", "sourcesContent": ["import {\n  CodedError,\n  createPer<PERSON>Hook,\n  PermissionExpiration,\n  PermissionHookOptions,\n  PermissionResponse,\n  PermissionStatus,\n  UnavailabilityError,\n} from 'expo-modules-core';\n\nimport ExponentImagePicker from './ExponentImagePicker';\nimport {\n  CameraPermissionResponse,\n  ImagePickerErrorResult,\n  ImagePickerOptions,\n  ImagePickerResult,\n  MediaLibraryPermissionResponse,\n} from './ImagePicker.types';\nimport { mapDeprecatedOptions } from './utils';\n\nfunction validateOptions(options: ImagePickerOptions) {\n  const { aspect, quality, videoMaxDuration } = options;\n\n  if (aspect != null) {\n    const [x, y] = aspect;\n\n    if (x <= 0 || y <= 0) {\n      throw new CodedError(\n        'ERR_INVALID_ARGUMENT',\n        `Invalid aspect ratio values ${x}:${y}. Provide positive numbers.`\n      );\n    }\n  }\n\n  if (quality && (quality < 0 || quality > 1)) {\n    throw new CodedError(\n      'ERR_INVALID_ARGUMENT',\n      `Invalid 'quality' value ${quality}. Provide a value between 0 and 1.`\n    );\n  }\n\n  if (videoMaxDuration && videoMaxDuration < 0) {\n    throw new CodedError(\n      'ERR_INVALID_ARGUMENT',\n      `Invalid 'videoMaxDuration' value ${videoMaxDuration}. Provide a non-negative number.`\n    );\n  }\n\n  return options;\n}\n\n// @needsAudit\n/**\n * Checks user's permissions for accessing camera.\n * @return A promise that fulfills with an object of type [CameraPermissionResponse](#camerapermissionresponse).\n */\nexport async function getCameraPermissionsAsync(): Promise<CameraPermissionResponse> {\n  return ExponentImagePicker.getCameraPermissionsAsync();\n}\n\n// @needsAudit\n/**\n * Checks user's permissions for accessing photos.\n * @param writeOnly Whether to request write or read and write permissions. Defaults to `false`\n * @return A promise that fulfills with an object of type [MediaLibraryPermissionResponse](#medialibrarypermissionresponse).\n */\nexport async function getMediaLibraryPermissionsAsync(\n  writeOnly: boolean = false\n): Promise<MediaLibraryPermissionResponse> {\n  return ExponentImagePicker.getMediaLibraryPermissionsAsync(writeOnly);\n}\n\n// @needsAudit\n/**\n * Asks the user to grant permissions for accessing camera. This does nothing on web because the\n * browser camera is not used.\n * @return A promise that fulfills with an object of type [CameraPermissionResponse](#camerarollpermissionresponse).\n */\nexport async function requestCameraPermissionsAsync(): Promise<CameraPermissionResponse> {\n  return ExponentImagePicker.requestCameraPermissionsAsync();\n}\n\n// @needsAudit\n/**\n * Asks the user to grant permissions for accessing user's photo. This method does nothing on web.\n * @param writeOnly Whether to request write or read and write permissions. Defaults to `false`\n * @return A promise that fulfills with an object of type [MediaLibraryPermissionResponse](#medialibrarypermissionresponse).\n */\nexport async function requestMediaLibraryPermissionsAsync(\n  writeOnly: boolean = false\n): Promise<MediaLibraryPermissionResponse> {\n  const imagePickerMethod = ExponentImagePicker.requestMediaLibraryPermissionsAsync;\n  return imagePickerMethod(writeOnly);\n}\n\n// @needsAudit\n/**\n * Check or request permissions to access the media library.\n * This uses both `requestMediaLibraryPermissionsAsync` and `getMediaLibraryPermissionsAsync` to interact with the permissions.\n *\n * @example\n * ```ts\n * const [status, requestPermission] = ImagePicker.useMediaLibraryPermissions();\n * ```\n */\nexport const useMediaLibraryPermissions = createPermissionHook<\n  MediaLibraryPermissionResponse,\n  { writeOnly?: boolean }\n>({\n  // TODO(cedric): permission requesters should have an options param or a different requester\n  getMethod: (options) => getMediaLibraryPermissionsAsync(options?.writeOnly),\n  requestMethod: (options) => requestMediaLibraryPermissionsAsync(options?.writeOnly),\n});\n\n// @needsAudit\n/**\n * Check or request permissions to access the camera.\n * This uses both `requestCameraPermissionsAsync` and `getCameraPermissionsAsync` to interact with the permissions.\n *\n * @example\n * ```ts\n * const [status, requestPermission] = ImagePicker.useCameraPermissions();\n * ```\n */\nexport const useCameraPermissions = createPermissionHook({\n  getMethod: getCameraPermissionsAsync,\n  requestMethod: requestCameraPermissionsAsync,\n});\n\n// @needsAudit\n/**\n * Android system sometimes kills the `MainActivity` after the `ImagePicker` finishes. When this\n * happens, we lose the data selected using the `ImagePicker`. However, you can retrieve the lost\n * data by calling `getPendingResultAsync`. You can test this functionality by turning on\n * `Don't keep activities` in the developer options.\n * @return\n * - **On Android:** a promise that resolves to an object of exactly same type as in\n * `ImagePicker.launchImageLibraryAsync` or `ImagePicker.launchCameraAsync` if the `ImagePicker`\n * finished successfully. Otherwise, an object of type [`ImagePickerErrorResult`](#imagepickerimagepickererrorresult).\n * - **On other platforms:** `null`\n */\nexport async function getPendingResultAsync(): Promise<\n  ImagePickerResult | ImagePickerErrorResult | null\n> {\n  if (ExponentImagePicker.getPendingResultAsync) {\n    return ExponentImagePicker.getPendingResultAsync();\n  }\n  return null;\n}\n\n// @needsAudit\n/**\n * Display the system UI for taking a photo with the camera. Requires `Permissions.CAMERA`.\n * On Android and iOS 10 `Permissions.CAMERA_ROLL` is also required. On mobile web, this must be\n * called immediately in a user interaction like a button press, otherwise the browser will block\n * the request without a warning.\n * > **Note:** Make sure that you handle `MainActivity` destruction on **Android**. See [ImagePicker.getPendingResultAsync](#imagepickergetpendingresultasync).\n * > **Notes for Web:** The system UI can only be shown after user activation (e.g. a `Button` press).\n * Therefore, calling `launchCameraAsync` in `componentDidMount`, for example, will **not** work as\n * intended. The `cancelled` event will not be returned in the browser due to platform restrictions\n * and inconsistencies across browsers.\n * @param options An `ImagePickerOptions` object.\n * @return A promise that resolves to an object with `canceled` and `assets` fields.\n * When the user canceled the action the `assets` is always `null`, otherwise it's an array of\n * the selected media assets which have a form of [`ImagePickerAsset`](#imagepickerasset).\n */\nexport async function launchCameraAsync(\n  options: ImagePickerOptions = {}\n): Promise<ImagePickerResult> {\n  if (!ExponentImagePicker.launchCameraAsync) {\n    throw new UnavailabilityError('ImagePicker', 'launchCameraAsync');\n  }\n  const mappedOptions = mapDeprecatedOptions(options);\n  return await ExponentImagePicker.launchCameraAsync(validateOptions(mappedOptions));\n}\n\n// @needsAudit\n/**\n * Display the system UI for choosing an image or a video from the phone's library.\n * Requires `Permissions.MEDIA_LIBRARY` on iOS 10 only. On mobile web, this must be     called\n * immediately in a user interaction like a button press, otherwise the browser will block the\n * request without a warning.\n *\n * **Animated GIFs support:** On Android, if the selected image is an animated GIF, the result image will be an\n * animated GIF too if and only if `quality` is explicitly set to `1.0` and `allowsEditing` is set to `false`.\n * Otherwise compression and/or cropper will pick the first frame of the GIF and return it as the\n * result (on Android the result will be a PNG). On iOS, both quality and cropping are supported.\n *\n * > **Notes for Web:** The system UI can only be shown after user activation (e.g. a `Button` press).\n * Therefore, calling `launchImageLibraryAsync` in `componentDidMount`, for example, will **not**\n * work as intended. The `cancelled` event will not be returned in the browser due to platform\n * restrictions and inconsistencies across browsers.\n * @param options An object extended by [`ImagePickerOptions`](#imagepickeroptions).\n * @return A promise that resolves to an object with `canceled` and `assets` fields.\n * When the user canceled the action the `assets` is always `null`, otherwise it's an array of\n * the selected media assets which have a form of [`ImagePickerAsset`](#imagepickerasset).\n */\nexport async function launchImageLibraryAsync(\n  options: ImagePickerOptions = {}\n): Promise<ImagePickerResult> {\n  const mappedOptions = mapDeprecatedOptions(options);\n\n  if (!ExponentImagePicker.launchImageLibraryAsync) {\n    throw new UnavailabilityError('ImagePicker', 'launchImageLibraryAsync');\n  }\n  if (mappedOptions?.allowsEditing && mappedOptions.allowsMultipleSelection) {\n    console.warn(\n      '[expo-image-picker] `allowsEditing` is not supported when `allowsMultipleSelection` is enabled and will be ignored.' +\n        \"Disable either 'allowsEditing' or 'allowsMultipleSelection' in 'launchImageLibraryAsync' \" +\n        'to fix this warning.'\n    );\n  }\n  return await ExponentImagePicker.launchImageLibraryAsync(mappedOptions);\n}\n\nexport * from './ImagePicker.types';\n\nexport type { PermissionExpiration, PermissionHookOptions, PermissionResponse };\nexport { PermissionStatus };\n"]}