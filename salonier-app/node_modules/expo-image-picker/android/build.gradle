plugins {
  id 'com.android.library'
  id 'expo-module-gradle-plugin'
}

group = 'host.exp.exponent'
version = '16.1.4'

android {
  namespace "expo.modules.imagepicker"
  defaultConfig {
    versionCode 22
    versionName "16.1.4"
    testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
  }
  testOptions {
    unitTests.includeAndroidResources = true
  }
}

dependencies {
  implementation "androidx.activity:activity-ktx:1.10.0"
  implementation "androidx.appcompat:appcompat:1.7.0"
  implementation "androidx.exifinterface:exifinterface:1.3.7"
  implementation "com.vanniktech:android-image-cropper:4.6.0"
  implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.3"
  implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.3"

  if (project.findProject(':expo-modules-test-core')) {
    androidTestImplementation project(':expo-modules-test-core')
  }
}
