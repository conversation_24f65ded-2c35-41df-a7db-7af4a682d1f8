{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "e", "__esModule", "default", "_default", "exports", "React", "createContext"], "sourceRoot": "../../src", "sources": ["GestureHandlerRootViewContext.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA0B,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,IAAAG,QAAA,GAAAC,OAAA,CAAAF,OAAA,gBAEXG,cAAK,CAACC,aAAa,CAAC,KAAK,CAAC", "ignoreList": []}