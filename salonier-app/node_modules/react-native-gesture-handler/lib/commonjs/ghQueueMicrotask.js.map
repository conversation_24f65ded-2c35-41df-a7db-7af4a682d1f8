{"version": 3, "names": ["ghQueueMicrotask", "exports", "setImmediate", "bind", "requestAnimationFrame", "queueMicrotask"], "sourceRoot": "../../src", "sources": ["ghQueueMicrotask.ts"], "mappings": ";;;;;;AAAA;AACA;AACO,MAAMA,gBAAgB,GAAAC,OAAA,CAAAD,gBAAA,GAC3B,OAAOE,YAAY,KAAK,UAAU,GAC9BA,YAAY,CAACC,IAAI,CAAC,IAAI,CAAC,GACvB,OAAOC,qBAAqB,KAAK,UAAU,GACzCA,qBAAqB,CAACD,IAAI,CAAC,IAAI,CAAC,GAChCE,cAAc,CAACF,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}