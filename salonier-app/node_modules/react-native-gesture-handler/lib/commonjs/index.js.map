{"version": 3, "names": ["_init", "require", "_Directions", "_State", "_PointerType", "_gestureHandlerRootHOC", "_interopRequireDefault", "_GestureHandlerRootView", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_TapGestureHandler", "_ForceTouchGestureHandler", "_LongPressGestureHandler", "_PanGestureHandler", "_PinchGestureHandler", "_RotationGestureHandler", "_FlingGestureHandler", "_createNativeWrapper", "_GestureDetector", "_gestureObjects", "_NativeViewGestureHandler", "_GestureButtons", "_touchables", "_GestureComponents", "_Text", "_hoverGesture", "_Swipeable", "_Pressable", "_DrawerLayout", "_EnableNewWebImplementation", "e", "__esModule", "default", "initialize"], "sourceRoot": "../../src", "sources": ["index.ts"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;AACA,IAAAI,sBAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,uBAAA,GAAAD,sBAAA,CAAAL,OAAA;AAeA,IAAAO,qBAAA,GAAAP,OAAA;AAsBA,IAAAQ,kBAAA,GAAAR,OAAA;AACA,IAAAS,yBAAA,GAAAT,OAAA;AACA,IAAAU,wBAAA,GAAAV,OAAA;AACA,IAAAW,kBAAA,GAAAX,OAAA;AACA,IAAAY,oBAAA,GAAAZ,OAAA;AACA,IAAAa,uBAAA,GAAAb,OAAA;AACA,IAAAc,oBAAA,GAAAd,OAAA;AACA,IAAAe,oBAAA,GAAAV,sBAAA,CAAAL,OAAA;AAEA,IAAAgB,gBAAA,GAAAhB,OAAA;AACA,IAAAiB,eAAA,GAAAjB,OAAA;AAkBA,IAAAkB,yBAAA,GAAAlB,OAAA;AAOA,IAAAmB,eAAA,GAAAnB,OAAA;AAYA,IAAAoB,WAAA,GAAApB,OAAA;AAMA,IAAAqB,kBAAA,GAAArB,OAAA;AAQA,IAAAsB,KAAA,GAAAtB,OAAA;AACA,IAAAuB,aAAA,GAAAvB,OAAA;AAyCA,IAAAwB,UAAA,GAAAnB,sBAAA,CAAAL,OAAA;AAKA,IAAAyB,UAAA,GAAApB,sBAAA,CAAAL,OAAA;AAUA,IAAA0B,aAAA,GAAArB,sBAAA,CAAAL,OAAA;AAEA,IAAA2B,2BAAA,GAAA3B,OAAA;AAGsC,SAAAK,uBAAAuB,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEtC,IAAAG,gBAAU,EAAC,CAAC", "ignoreList": []}