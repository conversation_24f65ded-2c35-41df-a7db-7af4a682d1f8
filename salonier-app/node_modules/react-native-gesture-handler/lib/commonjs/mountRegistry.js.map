{"version": 3, "names": ["MountRegistry", "mountListeners", "Set", "unmountListeners", "addMountListener", "listener", "add", "delete", "addUnmountListener", "gestureHandlerWillMount", "handler", "for<PERSON>ach", "gestureHandlerWillUnmount", "gestureWillMount", "gesture", "gestureWillUnmount", "exports"], "sourceRoot": "../../src", "sources": ["mountRegistry.ts"], "mappings": ";;;;;;AAUA;AACO,MAAMA,aAAa,CAAC;EACzB,OAAeC,cAAc,GAAG,IAAIC,GAAG,CAAuB,CAAC;EAC/D,OAAeC,gBAAgB,GAAG,IAAID,GAAG,CAAuB,CAAC;EAEjE,OAAOE,gBAAgBA,CAACC,QAA8B,EAAc;IAClE,IAAI,CAACJ,cAAc,CAACK,GAAG,CAACD,QAAQ,CAAC;IAEjC,OAAO,MAAM;MACX,IAAI,CAACJ,cAAc,CAACM,MAAM,CAACF,QAAQ,CAAC;IACtC,CAAC;EACH;EAEA,OAAOG,kBAAkBA,CAACH,QAA8B,EAAc;IACpE,IAAI,CAACF,gBAAgB,CAACG,GAAG,CAACD,QAAQ,CAAC;IAEnC,OAAO,MAAM;MACX,IAAI,CAACF,gBAAgB,CAACI,MAAM,CAACF,QAAQ,CAAC;IACxC,CAAC;EACH;EAEA,OAAOI,uBAAuBA,CAACC,OAAwB,EAAE;IACvD,IAAI,CAACT,cAAc,CAACU,OAAO,CAAEN,QAAQ,IACnCA,QAAQ,CAACK,OAAuC,CAClD,CAAC;EACH;EAEA,OAAOE,yBAAyBA,CAACF,OAAwB,EAAE;IACzD,IAAI,CAACP,gBAAgB,CAACQ,OAAO,CAAEN,QAAQ,IACrCA,QAAQ,CAACK,OAAuC,CAClD,CAAC;EACH;EAEA,OAAOG,gBAAgBA,CAACC,OAAoB,EAAE;IAC5C,IAAI,CAACb,cAAc,CAACU,OAAO,CAAEN,QAAQ,IAAKA,QAAQ,CAACS,OAAO,CAAC,CAAC;EAC9D;EAEA,OAAOC,kBAAkBA,CAACD,OAAoB,EAAE;IAC9C,IAAI,CAACX,gBAAgB,CAACQ,OAAO,CAAEN,QAAQ,IAAKA,QAAQ,CAACS,OAAO,CAAC,CAAC;EAChE;AACF;AAACE,OAAA,CAAAhB,aAAA,GAAAA,aAAA", "ignoreList": []}