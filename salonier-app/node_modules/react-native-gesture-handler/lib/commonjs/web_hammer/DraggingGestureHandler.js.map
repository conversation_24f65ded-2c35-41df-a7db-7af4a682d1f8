{"version": 3, "names": ["_Gesture<PERSON><PERSON>ler", "_interopRequireDefault", "require", "_reactNative", "e", "__esModule", "default", "DraggingGestureHandler", "Gesture<PERSON>andler", "shouldEnableGestureOnSetup", "transformNativeEvent", "deltaX", "deltaY", "velocityX", "velocityY", "center", "x", "y", "rect", "view", "getBoundingClientRect", "ratio", "PixelRatio", "get", "translationX", "__initialX", "translationY", "__initialY", "absoluteX", "absoluteY", "left", "top", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["web_hammer/DraggingGestureHandler.ts"], "mappings": ";;;;;;AAEA,IAAAA,eAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAA0C,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAH1C;AACA;;AAIA,MAAeG,sBAAsB,SAASC,uBAAc,CAAC;EAC3D,IAAIC,0BAA0BA,CAAA,EAAG;IAC/B,OAAO,IAAI;EACb;EAEAC,oBAAoBA,CAAC;IACnBC,MAAM;IACNC,MAAM;IACNC,SAAS;IACTC,SAAS;IACTC,MAAM,EAAE;MAAEC,CAAC;MAAEC;IAAE;EACD,CAAC,EAAE;IACjB;IACA,MAAMC,IAAI,GAAG,IAAI,CAACC,IAAI,CAAEC,qBAAqB,CAAC,CAAC;IAC/C,MAAMC,KAAK,GAAGC,uBAAU,CAACC,GAAG,CAAC,CAAC;IAC9B,OAAO;MACLC,YAAY,EAAEb,MAAM,IAAI,IAAI,CAACc,UAAU,IAAI,CAAC,CAAC;MAC7CC,YAAY,EAAEd,MAAM,IAAI,IAAI,CAACe,UAAU,IAAI,CAAC,CAAC;MAC7CC,SAAS,EAAEZ,CAAC;MACZa,SAAS,EAAEZ,CAAC;MACZJ,SAAS,EAAEA,SAAS,GAAGQ,KAAK;MAC5BP,SAAS,EAAEA,SAAS,GAAGO,KAAK;MAC5BL,CAAC,EAAEA,CAAC,GAAGE,IAAI,CAACY,IAAI;MAChBb,CAAC,EAAEA,CAAC,GAAGC,IAAI,CAACa;IACd,CAAC;EACH;AACF;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAA3B,OAAA,GAEcC,sBAAsB", "ignoreList": []}