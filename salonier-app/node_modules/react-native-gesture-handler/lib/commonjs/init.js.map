{"version": 3, "names": ["_eventReceiver", "require", "_RNGestureHandlerModule", "_interopRequireDefault", "_utils", "e", "__esModule", "default", "fabricInitialized", "initialize", "startListening", "maybeInitializeFabric", "isF<PERSON><PERSON>", "RNGestureHandlerModule", "install"], "sourceRoot": "../../src", "sources": ["init.ts"], "mappings": ";;;;;;;AAAA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAAmC,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEnC,IAAIG,iBAAiB,GAAG,KAAK;AAEtB,SAASC,UAAUA,CAAA,EAAG;EAC3B,IAAAC,6BAAc,EAAC,CAAC;AAClB;;AAEA;AACA;AACO,SAASC,qBAAqBA,CAAA,EAAG;EACtC,IAAI,IAAAC,eAAQ,EAAC,CAAC,IAAI,CAACJ,iBAAiB,EAAE;IACpCK,+BAAsB,CAACC,OAAO,CAAC,CAAC;IAChCN,iBAAiB,GAAG,IAAI;EAC1B;AACF", "ignoreList": []}