{"version": 3, "names": ["findHostInstance_DEPRECATED", "getInternalInstanceHandleFromPublicInstance", "getShadowNodeFromRef", "ref", "undefined", "ReactFabric", "require", "default", "e", "_ref", "_internalInstanceHandle", "stateNode", "node"], "sourceRoot": "../../src", "sources": ["getShadowNodeFromRef.ts"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA,IAAIA,2BAAmD;AACvD,IAAIC,2CAEH;AAEM,SAASC,oBAAoBA,CAACC,GAAY,EAAE;EACjD;EACA,IAAIH,2BAA2B,KAAKI,SAAS,EAAE;IAC7C,IAAI;MACF;MACA,MAAMC,WAAW,GAAGC,OAAO,CAAC,mDAAmD,CAAC;MAChF;MACA;MACA;MACAN,2BAA2B;MACzB;MACAK,WAAW,EAAEE,OAAO,EAAEP,2BAA2B;MACjD;MACAK,WAAW,EAAEL,2BAA2B;IAC5C,CAAC,CAAC,OAAOQ,CAAC,EAAE;MACVR,2BAA2B,GAAIS,IAAa,IAAK,IAAI;IACvD;EACF;;EAEA;EACA,IAAIR,2CAA2C,KAAKG,SAAS,EAAE;IAC7D,IAAI;MACF;MACAH,2CAA2C;MACzC;MACAK,OAAO,CAAC,wFAAwF,CAAC,CAC9FL,2CAA2C;MAC9C;MACEE,GAAQ,IAAKA,GAAG,CAACO,uBAAuB,CAAC;IAC/C,CAAC,CAAC,OAAOF,CAAC,EAAE;MACVP,2CAA2C,GAAIE,GAAQ;MACrD;MACAA,GAAG,CAACO,uBAAuB;IAC/B;EACF;;EAEA;EACA,OAAOT,2CAA2C,CAChDD,2BAA2B,CAACG,GAAG,CACjC,CAAC,CAACQ,SAAS,CAACC,IAAI;AAClB", "ignoreList": []}