{"version": 3, "names": ["_State", "require", "_Directions", "_Gesture<PERSON><PERSON>ler", "_interopRequireDefault", "_Vector", "_utils", "e", "__esModule", "default", "DEFAULT_MAX_DURATION_MS", "DEFAULT_MIN_VELOCITY", "DEFAULT_ALIGNMENT_CONE", "DEFAULT_DIRECTION", "Directions", "RIGHT", "DEFAULT_NUMBER_OF_TOUCHES_REQUIRED", "AXIAL_DEVIATION_COSINE", "coneToDeviation", "DIAGONAL_DEVIATION_COSINE", "FlingGestureHandler", "Gesture<PERSON>andler", "numberOfPointersRequired", "direction", "maxDurationMs", "minVelocity", "maxNumberOfPointersSimultaneously", "keyPointer", "NaN", "updateGestureConfig", "enabled", "props", "config", "numberOfPointers", "startFling", "begin", "delayTimeout", "setTimeout", "fail", "tryEndFling", "velocityVector", "Vector", "fromVelocity", "tracker", "getAlignment", "minimalAlignmentCosine", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fromDirection", "axialDirectionsList", "Object", "values", "diagonalDirectionsList", "DiagonalDirections", "axialAlignmentList", "map", "diagonalAlignmentList", "isAligned", "some", "Boolean", "isFast", "magnitude", "clearTimeout", "activate", "endFling", "onPointerDown", "event", "isButtonInConfig", "button", "addToTracker", "pointerId", "newPointerAction", "tryToSendTouchEvent", "onPointerAdd", "state", "State", "UNDETERMINED", "BEGAN", "trackedPointersCount", "pointerMoveAction", "track", "onPointerMove", "onPointerOutOfBounds", "onPointerUp", "onUp", "onPointerRemove", "removeFromTracker", "force", "end", "resetConfig", "exports"], "sourceRoot": "../../../../src", "sources": ["web/handlers/FlingGestureHandler.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AAGA,IAAAE,eAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,OAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAA2C,SAAAG,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE3C,MAAMG,uBAAuB,GAAG,GAAG;AACnC,MAAMC,oBAAoB,GAAG,GAAG;AAChC,MAAMC,sBAAsB,GAAG,EAAE;AACjC,MAAMC,iBAAiB,GAAGC,sBAAU,CAACC,KAAK;AAC1C,MAAMC,kCAAkC,GAAG,CAAC;AAE5C,MAAMC,sBAAsB,GAAG,IAAAC,sBAAe,EAACN,sBAAsB,CAAC;AACtE,MAAMO,yBAAyB,GAAG,IAAAD,sBAAe,EAAC,EAAE,GAAGN,sBAAsB,CAAC;AAE/D,MAAMQ,mBAAmB,SAASC,uBAAc,CAAC;EACtDC,wBAAwB,GAAGN,kCAAkC;EAC7DO,SAAS,GAAeV,iBAAiB;EAEzCW,aAAa,GAAGd,uBAAuB;EACvCe,WAAW,GAAGd,oBAAoB;EAGlCe,iCAAiC,GAAG,CAAC;EACrCC,UAAU,GAAGC,GAAG;EAEjBC,mBAAmBA,CAAC;IAAEC,OAAO,GAAG,IAAI;IAAE,GAAGC;EAAc,CAAC,EAAQ;IACrE,KAAK,CAACF,mBAAmB,CAAC;MAAEC,OAAO,EAAEA,OAAO;MAAE,GAAGC;IAAM,CAAC,CAAC;IAEzD,IAAI,IAAI,CAACC,MAAM,CAACT,SAAS,EAAE;MACzB,IAAI,CAACA,SAAS,GAAG,IAAI,CAACS,MAAM,CAACT,SAAS;IACxC;IAEA,IAAI,IAAI,CAACS,MAAM,CAACC,gBAAgB,EAAE;MAChC,IAAI,CAACX,wBAAwB,GAAG,IAAI,CAACU,MAAM,CAACC,gBAAgB;IAC9D;EACF;EAEQC,UAAUA,CAAA,EAAS;IACzB,IAAI,CAACC,KAAK,CAAC,CAAC;IAEZ,IAAI,CAACT,iCAAiC,GAAG,CAAC;IAE1C,IAAI,CAACU,YAAY,GAAGC,UAAU,CAAC,MAAM,IAAI,CAACC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACd,aAAa,CAAC;EACvE;EAEQe,WAAWA,CAAA,EAAY;IAC7B,MAAMC,cAAc,GAAGC,eAAM,CAACC,YAAY,CAAC,IAAI,CAACC,OAAO,EAAE,IAAI,CAAChB,UAAU,CAAC;IAEzE,MAAMiB,YAAY,GAAGA,CACnBrB,SAA0C,EAC1CsB,sBAA8B,KAC3B;MACH,OACE,CAACtB,SAAS,GAAG,IAAI,CAACA,SAAS,MAAMA,SAAS,IAC1CiB,cAAc,CAACM,SAAS,CACtBL,eAAM,CAACM,aAAa,CAACxB,SAAS,CAAC,EAC/BsB,sBACF,CAAC;IAEL,CAAC;IAED,MAAMG,mBAAmB,GAAGC,MAAM,CAACC,MAAM,CAACpC,sBAAU,CAAC;IACrD,MAAMqC,sBAAsB,GAAGF,MAAM,CAACC,MAAM,CAACE,8BAAkB,CAAC;;IAEhE;IACA,MAAMC,kBAAkB,GAAGL,mBAAmB,CAACM,GAAG,CAAE/B,SAAS,IAC3DqB,YAAY,CAACrB,SAAS,EAAEN,sBAAsB,CAChD,CAAC;IAED,MAAMsC,qBAAqB,GAAGJ,sBAAsB,CAACG,GAAG,CAAE/B,SAAS,IACjEqB,YAAY,CAACrB,SAAS,EAAEJ,yBAAyB,CACnD,CAAC;IAED,MAAMqC,SAAS,GACbH,kBAAkB,CAACI,IAAI,CAACC,OAAO,CAAC,IAAIH,qBAAqB,CAACE,IAAI,CAACC,OAAO,CAAC;IAEzE,MAAMC,MAAM,GAAGnB,cAAc,CAACoB,SAAS,GAAG,IAAI,CAACnC,WAAW;IAE1D,IACE,IAAI,CAACC,iCAAiC,KACpC,IAAI,CAACJ,wBAAwB,IAC/BkC,SAAS,IACTG,MAAM,EACN;MACAE,YAAY,CAAC,IAAI,CAACzB,YAAY,CAAC;MAC/B,IAAI,CAAC0B,QAAQ,CAAC,CAAC;MAEf,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEQC,QAAQA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACxB,WAAW,CAAC,CAAC,EAAE;MACvB,IAAI,CAACD,IAAI,CAAC,CAAC;IACb;EACF;EAEU0B,aAAaA,CAACC,KAAmB,EAAQ;IACjD,IAAI,CAAC,IAAI,CAACC,gBAAgB,CAACD,KAAK,CAACE,MAAM,CAAC,EAAE;MACxC;IACF;IAEA,IAAI,CAACxB,OAAO,CAACyB,YAAY,CAACH,KAAK,CAAC;IAChC,IAAI,CAACtC,UAAU,GAAGsC,KAAK,CAACI,SAAS;IAEjC,KAAK,CAACL,aAAa,CAACC,KAAK,CAAC;IAC1B,IAAI,CAACK,gBAAgB,CAAC,CAAC;IAEvB,IAAI,CAACC,mBAAmB,CAACN,KAAK,CAAC;EACjC;EAEUO,YAAYA,CAACP,KAAmB,EAAQ;IAChD,IAAI,CAACtB,OAAO,CAACyB,YAAY,CAACH,KAAK,CAAC;IAChC,KAAK,CAACO,YAAY,CAACP,KAAK,CAAC;IACzB,IAAI,CAACK,gBAAgB,CAAC,CAAC;EACzB;EAEQA,gBAAgBA,CAAA,EAAS;IAC/B,IAAI,IAAI,CAACG,KAAK,KAAKC,YAAK,CAACC,YAAY,EAAE;MACrC,IAAI,CAACzC,UAAU,CAAC,CAAC;IACnB;IAEA,IAAI,IAAI,CAACuC,KAAK,KAAKC,YAAK,CAACE,KAAK,EAAE;MAC9B;IACF;IAEA,IAAI,CAACrC,WAAW,CAAC,CAAC;IAElB,IACE,IAAI,CAACI,OAAO,CAACkC,oBAAoB,GAAG,IAAI,CAACnD,iCAAiC,EAC1E;MACA,IAAI,CAACA,iCAAiC,GACpC,IAAI,CAACiB,OAAO,CAACkC,oBAAoB;IACrC;EACF;EAEQC,iBAAiBA,CAACb,KAAmB,EAAQ;IACnD,IAAI,CAACtB,OAAO,CAACoC,KAAK,CAACd,KAAK,CAAC;IAEzB,IAAI,IAAI,CAACQ,KAAK,KAAKC,YAAK,CAACE,KAAK,EAAE;MAC9B;IACF;IAEA,IAAI,CAACrC,WAAW,CAAC,CAAC;EACpB;EAEUyC,aAAaA,CAACf,KAAmB,EAAQ;IACjD,IAAI,CAACa,iBAAiB,CAACb,KAAK,CAAC;IAC7B,KAAK,CAACe,aAAa,CAACf,KAAK,CAAC;EAC5B;EAEUgB,oBAAoBA,CAAChB,KAAmB,EAAQ;IACxD,IAAI,CAACa,iBAAiB,CAACb,KAAK,CAAC;IAC7B,KAAK,CAACgB,oBAAoB,CAAChB,KAAK,CAAC;EACnC;EAEUiB,WAAWA,CAACjB,KAAmB,EAAQ;IAC/C,KAAK,CAACiB,WAAW,CAACjB,KAAK,CAAC;IACxB,IAAI,CAACkB,IAAI,CAAClB,KAAK,CAAC;IAEhB,IAAI,CAACtC,UAAU,GAAGC,GAAG;EACvB;EAEUwD,eAAeA,CAACnB,KAAmB,EAAQ;IACnD,KAAK,CAACmB,eAAe,CAACnB,KAAK,CAAC;IAC5B,IAAI,CAACkB,IAAI,CAAClB,KAAK,CAAC;EAClB;EAEQkB,IAAIA,CAAClB,KAAmB,EAAQ;IACtC,IAAI,IAAI,CAACQ,KAAK,KAAKC,YAAK,CAACE,KAAK,EAAE;MAC9B,IAAI,CAACb,QAAQ,CAAC,CAAC;IACjB;IAEA,IAAI,CAACpB,OAAO,CAAC0C,iBAAiB,CAACpB,KAAK,CAACI,SAAS,CAAC;EACjD;EAEOP,QAAQA,CAACwB,KAAe,EAAQ;IACrC,KAAK,CAACxB,QAAQ,CAACwB,KAAK,CAAC;IACrB,IAAI,CAACC,GAAG,CAAC,CAAC;EACZ;EAEUC,WAAWA,CAAA,EAAS;IAC5B,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,CAAClE,wBAAwB,GAAGN,kCAAkC;IAClE,IAAI,CAACO,SAAS,GAAGV,iBAAiB;EACpC;AACF;AAAC4E,OAAA,CAAAhF,OAAA,GAAAW,mBAAA", "ignoreList": []}