{"version": 3, "names": ["_State", "require", "_GestureHandlerOrchestrator", "_interopRequireDefault", "_Gesture<PERSON><PERSON>ler", "e", "__esModule", "default", "HoverGestureHandler", "Gesture<PERSON>andler", "transformNativeEvent", "stylusData", "onPointerMoveOver", "event", "GestureHandlerOrchestrator", "instance", "recordHandlerIfNotPresent", "tracker", "addToTracker", "state", "State", "UNDETERMINED", "begin", "activate", "onPointerMoveOut", "removeFromTracker", "pointerId", "end", "onPointerMove", "track", "onPointerCancel", "reset", "exports"], "sourceRoot": "../../../../src", "sources": ["web/handlers/HoverGestureHandler.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEA,IAAAC,2BAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,eAAA,GAAAD,sBAAA,CAAAF,OAAA;AAA8C,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE/B,MAAMG,mBAAmB,SAASC,uBAAc,CAAC;EAGpDC,oBAAoBA,CAAA,EAA4B;IACxD,OAAO;MACL,GAAG,KAAK,CAACA,oBAAoB,CAAC,CAAC;MAC/BC,UAAU,EAAE,IAAI,CAACA;IACnB,CAAC;EACH;EAEUC,iBAAiBA,CAACC,KAAmB,EAAQ;IACrDC,mCAA0B,CAACC,QAAQ,CAACC,yBAAyB,CAAC,IAAI,CAAC;IAEnE,IAAI,CAACC,OAAO,CAACC,YAAY,CAACL,KAAK,CAAC;IAChC,IAAI,CAACF,UAAU,GAAGE,KAAK,CAACF,UAAU;IAClC,KAAK,CAACC,iBAAiB,CAACC,KAAK,CAAC;IAE9B,IAAI,IAAI,CAACM,KAAK,KAAKC,YAAK,CAACC,YAAY,EAAE;MACrC,IAAI,CAACC,KAAK,CAAC,CAAC;MACZ,IAAI,CAACC,QAAQ,CAAC,CAAC;IACjB;EACF;EAEUC,gBAAgBA,CAACX,KAAmB,EAAQ;IACpD,IAAI,CAACI,OAAO,CAACQ,iBAAiB,CAACZ,KAAK,CAACa,SAAS,CAAC;IAC/C,IAAI,CAACf,UAAU,GAAGE,KAAK,CAACF,UAAU;IAElC,KAAK,CAACa,gBAAgB,CAACX,KAAK,CAAC;IAE7B,IAAI,CAACc,GAAG,CAAC,CAAC;EACZ;EAEUC,aAAaA,CAACf,KAAmB,EAAQ;IACjD,IAAI,CAACI,OAAO,CAACY,KAAK,CAAChB,KAAK,CAAC;IACzB,IAAI,CAACF,UAAU,GAAGE,KAAK,CAACF,UAAU;IAElC,KAAK,CAACiB,aAAa,CAACf,KAAK,CAAC;EAC5B;EAEUiB,eAAeA,CAACjB,KAAmB,EAAQ;IACnD,KAAK,CAACiB,eAAe,CAACjB,KAAK,CAAC;IAC5B,IAAI,CAACkB,KAAK,CAAC,CAAC;EACd;AACF;AAACC,OAAA,CAAAzB,OAAA,GAAAC,mBAAA", "ignoreList": []}