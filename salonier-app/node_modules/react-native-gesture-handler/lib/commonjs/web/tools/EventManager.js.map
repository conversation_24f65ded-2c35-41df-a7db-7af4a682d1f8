{"version": 3, "names": ["EventManager", "pointersInBounds", "constructor", "view", "activePointersCounter", "onPointerDown", "_event", "onPointerAdd", "onPointerUp", "onPointerRemove", "onPointerMove", "onPointerLeave", "onPointerEnter", "onPointerCancel", "onPointerOutOfBounds", "onPointerMoveOver", "onPointerMoveOut", "onWheel", "setOnPointerDown", "callback", "setOnPointerAdd", "setOnPointerUp", "setOnPointerRemove", "setOnPointerMove", "setOnPointerLeave", "setOnPointerEnter", "setOnPointerCancel", "setOnPointerOutOfBounds", "setOnPointerMoveOver", "setOnPointerMoveOut", "setOnWheel", "markAsInBounds", "pointerId", "indexOf", "push", "markAsOutOfBounds", "index", "splice", "resetManager", "exports", "default"], "sourceRoot": "../../../../src", "sources": ["web/tools/EventManager.ts"], "mappings": ";;;;;;AAAA;;AAKe,MAAeA,YAAY,CAAI;EAElCC,gBAAgB,GAAa,EAAE;EAGzCC,WAAWA,CAACC,IAAO,EAAE;IACnB,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,qBAAqB,GAAG,CAAC;EAChC;EAUUC,aAAaA,CAACC,MAAoB,EAAQ,CAAC;EAC3CC,YAAYA,CAACD,MAAoB,EAAQ,CAAC;EAC1CE,WAAWA,CAACF,MAAoB,EAAQ,CAAC;EACzCG,eAAeA,CAACH,MAAoB,EAAQ,CAAC;EAC7CI,aAAaA,CAACJ,MAAoB,EAAQ,CAAC;EAC3CK,cAAcA,CAACL,MAAoB,EAAQ,CAAC,CAAC,CAAC;EAC9CM,cAAcA,CAACN,MAAoB,EAAQ,CAAC,CAAC,CAAC;EAC9CO,eAAeA,CAACP,MAAoB,EAAQ;IACpD;IACA;IACA;IACA;EAAA;EAEQQ,oBAAoBA,CAACR,MAAoB,EAAQ,CAAC;EAClDS,iBAAiBA,CAACT,MAAoB,EAAQ,CAAC;EAC/CU,gBAAgBA,CAACV,MAAoB,EAAQ,CAAC;EAC9CW,OAAOA,CAACX,MAAoB,EAAQ,CAAC;EAExCY,gBAAgBA,CAACC,QAA8B,EAAQ;IAC5D,IAAI,CAACd,aAAa,GAAGc,QAAQ;EAC/B;EACOC,eAAeA,CAACD,QAA8B,EAAQ;IAC3D,IAAI,CAACZ,YAAY,GAAGY,QAAQ;EAC9B;EACOE,cAAcA,CAACF,QAA8B,EAAQ;IAC1D,IAAI,CAACX,WAAW,GAAGW,QAAQ;EAC7B;EACOG,kBAAkBA,CAACH,QAA8B,EAAQ;IAC9D,IAAI,CAACV,eAAe,GAAGU,QAAQ;EACjC;EACOI,gBAAgBA,CAACJ,QAA8B,EAAQ;IAC5D,IAAI,CAACT,aAAa,GAAGS,QAAQ;EAC/B;EACOK,iBAAiBA,CAACL,QAA8B,EAAQ;IAC7D,IAAI,CAACR,cAAc,GAAGQ,QAAQ;EAChC;EACOM,iBAAiBA,CAACN,QAA8B,EAAQ;IAC7D,IAAI,CAACP,cAAc,GAAGO,QAAQ;EAChC;EACOO,kBAAkBA,CAACP,QAA8B,EAAQ;IAC9D,IAAI,CAACN,eAAe,GAAGM,QAAQ;EACjC;EACOQ,uBAAuBA,CAACR,QAA8B,EAAQ;IACnE,IAAI,CAACL,oBAAoB,GAAGK,QAAQ;EACtC;EACOS,oBAAoBA,CAACT,QAA8B,EAAQ;IAChE,IAAI,CAACJ,iBAAiB,GAAGI,QAAQ;EACnC;EACOU,mBAAmBA,CAACV,QAA8B,EAAQ;IAC/D,IAAI,CAACH,gBAAgB,GAAGG,QAAQ;EAClC;EACOW,UAAUA,CAACX,QAA8B,EAAQ;IACtD,IAAI,CAACF,OAAO,GAAGE,QAAQ;EACzB;EAEUY,cAAcA,CAACC,SAAiB,EAAQ;IAChD,IAAI,IAAI,CAAC/B,gBAAgB,CAACgC,OAAO,CAACD,SAAS,CAAC,IAAI,CAAC,EAAE;MACjD;IACF;IAEA,IAAI,CAAC/B,gBAAgB,CAACiC,IAAI,CAACF,SAAS,CAAC;EACvC;EAEUG,iBAAiBA,CAACH,SAAiB,EAAQ;IACnD,MAAMI,KAAa,GAAG,IAAI,CAACnC,gBAAgB,CAACgC,OAAO,CAACD,SAAS,CAAC;IAE9D,IAAII,KAAK,GAAG,CAAC,EAAE;MACb;IACF;IAEA,IAAI,CAACnC,gBAAgB,CAACoC,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;EACxC;EAEOE,YAAYA,CAAA,EAAS;IAC1B;IACA;IACA;IACA;IACA;IACA;;IAEA,IAAI,CAAClC,qBAAqB,GAAG,CAAC;IAC9B,IAAI,CAACH,gBAAgB,GAAG,EAAE;EAC5B;AACF;AAACsC,OAAA,CAAAC,OAAA,GAAAxC,YAAA", "ignoreList": []}