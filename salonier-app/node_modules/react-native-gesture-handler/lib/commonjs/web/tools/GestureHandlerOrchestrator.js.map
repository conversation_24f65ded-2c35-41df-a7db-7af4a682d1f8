{"version": 3, "names": ["_PointerType", "require", "_State", "_PointerTracker", "_interopRequireDefault", "e", "__esModule", "default", "GestureHandlerOrchestrator", "gestureHandlers", "awaitingHandlers", "awaitingHandlersTags", "Set", "handlingChangeSemaphore", "activationIndex", "constructor", "scheduleFinishedHandlersCleanup", "cleanupFinishedHandlers", "<PERSON><PERSON><PERSON><PERSON>", "handler", "reset", "active", "awaiting", "Number", "MAX_VALUE", "removeHandlerFromOrchestrator", "indexInGestureHandlers", "indexOf", "indexInAwaitingHandlers", "splice", "delete", "handlerTag", "handlersToRemove", "i", "length", "isFinished", "state", "add", "filter", "has", "hasOtherHandlerToWaitFor", "hasToWaitFor", "<PERSON><PERSON><PERSON><PERSON>", "shouldHandlerWaitForOther", "some", "shouldBeCancelledByFinishedHandler", "shouldBeCancelled", "State", "END", "tryActivate", "cancel", "add<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handlerState", "CANCELLED", "FAILED", "shouldActivate", "makeActive", "ACTIVE", "fail", "BEGAN", "shouldBeCancelledBy", "shouldHandlerBeCancelledBy", "cleanupAwaitingHandlers", "should<PERSON>ait", "onHandlerStateChange", "newState", "oldState", "sendIfDisabled", "enabled", "sendEvent", "UNDETERMINED", "includes", "currentState", "shouldResetProgress", "push", "recordHandlerIfNotPresent", "MAX_SAFE_INTEGER", "shouldWaitForHandlerFailure", "shouldRequireToWaitForFailure", "canRunSimultaneously", "gh1", "gh2", "shouldRecognizeSimultaneously", "shouldBeCancelledByOther", "handlerPointers", "getTrackedPointersID", "otherPointers", "PointerTracker", "shareCommonPointers", "delegate", "view", "checkOverlap", "isPointerWithinBothBounds", "pointer", "point", "tracker", "getLastAbsoluteCoords", "isPointerInBounds", "cancelMouseAndPenGestures", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "pointerType", "PointerType", "MOUSE", "STYLUS", "resetTracker", "instance", "_instance", "exports"], "sourceRoot": "../../../../src", "sources": ["web/tools/GestureHandlerOrchestrator.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAGA,IAAAE,eAAA,GAAAC,sBAAA,CAAAH,OAAA;AAA8C,SAAAG,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE/B,MAAMG,0BAA0B,CAAC;EAGtCC,eAAe,GAAsB,EAAE;EACvCC,gBAAgB,GAAsB,EAAE;EACxCC,oBAAoB,GAAgB,IAAIC,GAAG,CAAC,CAAC;EAE7CC,uBAAuB,GAAG,CAAC;EAC3BC,eAAe,GAAG,CAAC;;EAE3B;EACA;EACQC,WAAWA,CAAA,EAAG,CAAC;EAEfC,+BAA+BA,CAAA,EAAS;IAC9C,IAAI,IAAI,CAACH,uBAAuB,KAAK,CAAC,EAAE;MACtC,IAAI,CAACI,uBAAuB,CAAC,CAAC;IAChC;EACF;EAEQC,YAAYA,CAACC,OAAwB,EAAQ;IACnDA,OAAO,CAACC,KAAK,CAAC,CAAC;IACfD,OAAO,CAACE,MAAM,GAAG,KAAK;IACtBF,OAAO,CAACG,QAAQ,GAAG,KAAK;IACxBH,OAAO,CAACL,eAAe,GAAGS,MAAM,CAACC,SAAS;EAC5C;EAEOC,6BAA6BA,CAACN,OAAwB,EAAQ;IACnE,MAAMO,sBAAsB,GAAG,IAAI,CAACjB,eAAe,CAACkB,OAAO,CAACR,OAAO,CAAC;IACpE,MAAMS,uBAAuB,GAAG,IAAI,CAAClB,gBAAgB,CAACiB,OAAO,CAACR,OAAO,CAAC;IAEtE,IAAIO,sBAAsB,IAAI,CAAC,EAAE;MAC/B,IAAI,CAACjB,eAAe,CAACoB,MAAM,CAACH,sBAAsB,EAAE,CAAC,CAAC;IACxD;IAEA,IAAIE,uBAAuB,IAAI,CAAC,EAAE;MAChC,IAAI,CAAClB,gBAAgB,CAACmB,MAAM,CAACD,uBAAuB,EAAE,CAAC,CAAC;MACxD,IAAI,CAACjB,oBAAoB,CAACmB,MAAM,CAACX,OAAO,CAACY,UAAU,CAAC;IACtD;EACF;EAEQd,uBAAuBA,CAAA,EAAS;IACtC,MAAMe,gBAAgB,GAAG,IAAIpB,GAAG,CAAkB,CAAC;IAEnD,KAAK,IAAIqB,CAAC,GAAG,IAAI,CAACxB,eAAe,CAACyB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MACzD,MAAMd,OAAO,GAAG,IAAI,CAACV,eAAe,CAACwB,CAAC,CAAC;MAEvC,IAAI,IAAI,CAACE,UAAU,CAAChB,OAAO,CAACiB,KAAK,CAAC,IAAI,CAACjB,OAAO,CAACG,QAAQ,EAAE;QACvD,IAAI,CAACJ,YAAY,CAACC,OAAO,CAAC;QAC1Ba,gBAAgB,CAACK,GAAG,CAAClB,OAAO,CAAC;MAC/B;IACF;IAEA,IAAI,CAACV,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC6B,MAAM,CAC/CnB,OAAO,IAAK,CAACa,gBAAgB,CAACO,GAAG,CAACpB,OAAO,CAC5C,CAAC;EACH;EAEQqB,wBAAwBA,CAACrB,OAAwB,EAAW;IAClE,MAAMsB,YAAY,GAAIC,YAA6B,IAAK;MACtD,OACE,CAAC,IAAI,CAACP,UAAU,CAACO,YAAY,CAACN,KAAK,CAAC,IACpC,IAAI,CAACO,yBAAyB,CAACxB,OAAO,EAAEuB,YAAY,CAAC;IAEzD,CAAC;IAED,OAAO,IAAI,CAACjC,eAAe,CAACmC,IAAI,CAACH,YAAY,CAAC;EAChD;EAEQI,kCAAkCA,CACxC1B,OAAwB,EACf;IACT,MAAM2B,iBAAiB,GAAIJ,YAA6B,IAAK;MAC3D,OACE,IAAI,CAACC,yBAAyB,CAACxB,OAAO,EAAEuB,YAAY,CAAC,IACrDA,YAAY,CAACN,KAAK,KAAKW,YAAK,CAACC,GAAG;IAEpC,CAAC;IAED,OAAO,IAAI,CAACvC,eAAe,CAACmC,IAAI,CAACE,iBAAiB,CAAC;EACrD;EAEQG,WAAWA,CAAC9B,OAAwB,EAAQ;IAClD,IAAI,IAAI,CAAC0B,kCAAkC,CAAC1B,OAAO,CAAC,EAAE;MACpDA,OAAO,CAAC+B,MAAM,CAAC,CAAC;MAChB;IACF;IAEA,IAAI,IAAI,CAACV,wBAAwB,CAACrB,OAAO,CAAC,EAAE;MAC1C,IAAI,CAACgC,kBAAkB,CAAChC,OAAO,CAAC;MAChC;IACF;IAEA,MAAMiC,YAAY,GAAGjC,OAAO,CAACiB,KAAK;IAElC,IAAIgB,YAAY,KAAKL,YAAK,CAACM,SAAS,IAAID,YAAY,KAAKL,YAAK,CAACO,MAAM,EAAE;MACrE;IACF;IAEA,IAAI,IAAI,CAACC,cAAc,CAACpC,OAAO,CAAC,EAAE;MAChC,IAAI,CAACqC,UAAU,CAACrC,OAAO,CAAC;MACxB;IACF;IAEA,IAAIiC,YAAY,KAAKL,YAAK,CAACU,MAAM,EAAE;MACjCtC,OAAO,CAACuC,IAAI,CAAC,CAAC;MACd;IACF;IAEA,IAAIN,YAAY,KAAKL,YAAK,CAACY,KAAK,EAAE;MAChCxC,OAAO,CAAC+B,MAAM,CAAC,CAAC;IAClB;EACF;EAEQK,cAAcA,CAACpC,OAAwB,EAAW;IACxD,MAAMyC,mBAAmB,GAAIlB,YAA6B,IAAK;MAC7D,OAAO,IAAI,CAACmB,0BAA0B,CAAC1C,OAAO,EAAEuB,YAAY,CAAC;IAC/D,CAAC;IAED,OAAO,CAAC,IAAI,CAACjC,eAAe,CAACmC,IAAI,CAACgB,mBAAmB,CAAC;EACxD;EAEQE,uBAAuBA,CAAC3C,OAAwB,EAAQ;IAC9D,MAAM4C,UAAU,GAAIrB,YAA6B,IAAK;MACpD,OACE,CAACA,YAAY,CAACpB,QAAQ,IACtB,IAAI,CAACqB,yBAAyB,CAACD,YAAY,EAAEvB,OAAO,CAAC;IAEzD,CAAC;IAED,KAAK,MAAMuB,YAAY,IAAI,IAAI,CAAChC,gBAAgB,EAAE;MAChD,IAAIqD,UAAU,CAACrB,YAAY,CAAC,EAAE;QAC5B,IAAI,CAACxB,YAAY,CAACwB,YAAY,CAAC;QAC/B,IAAI,CAAC/B,oBAAoB,CAACmB,MAAM,CAACY,YAAY,CAACX,UAAU,CAAC;MAC3D;IACF;IAEA,IAAI,CAACrB,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAAC4B,MAAM,CAAEI,YAAY,IAChE,IAAI,CAAC/B,oBAAoB,CAAC4B,GAAG,CAACG,YAAY,CAACX,UAAU,CACvD,CAAC;EACH;EAEOiC,oBAAoBA,CACzB7C,OAAwB,EACxB8C,QAAe,EACfC,QAAe,EACfC,cAAwB,EAClB;IACN,IAAI,CAAChD,OAAO,CAACiD,OAAO,IAAI,CAACD,cAAc,EAAE;MACvC;IACF;IAEA,IAAI,CAACtD,uBAAuB,IAAI,CAAC;IAEjC,IAAI,IAAI,CAACsB,UAAU,CAAC8B,QAAQ,CAAC,EAAE;MAC7B,KAAK,MAAMvB,YAAY,IAAI,IAAI,CAAChC,gBAAgB,EAAE;QAChD,IACE,CAAC,IAAI,CAACiC,yBAAyB,CAACD,YAAY,EAAEvB,OAAO,CAAC,IACtD,CAAC,IAAI,CAACR,oBAAoB,CAAC4B,GAAG,CAACG,YAAY,CAACX,UAAU,CAAC,EACvD;UACA;QACF;QAEA,IAAIkC,QAAQ,KAAKlB,YAAK,CAACC,GAAG,EAAE;UAC1B,IAAI,CAACC,WAAW,CAACP,YAAY,CAAC;UAC9B;QACF;QAEAA,YAAY,CAACQ,MAAM,CAAC,CAAC;QAErB,IAAIR,YAAY,CAACN,KAAK,KAAKW,YAAK,CAACC,GAAG,EAAE;UACpC;UACA;UACA;UACA;UACAN,YAAY,CAAC2B,SAAS,CAACtB,YAAK,CAACM,SAAS,EAAEN,YAAK,CAACY,KAAK,CAAC;QACtD;QAEAjB,YAAY,CAACpB,QAAQ,GAAG,KAAK;MAC/B;IACF;IAEA,IAAI2C,QAAQ,KAAKlB,YAAK,CAACU,MAAM,EAAE;MAC7B,IAAI,CAACR,WAAW,CAAC9B,OAAO,CAAC;IAC3B,CAAC,MAAM,IAAI+C,QAAQ,KAAKnB,YAAK,CAACU,MAAM,IAAIS,QAAQ,KAAKnB,YAAK,CAACC,GAAG,EAAE;MAC9D,IAAI7B,OAAO,CAACE,MAAM,EAAE;QAClBF,OAAO,CAACkD,SAAS,CAACJ,QAAQ,EAAEC,QAAQ,CAAC;MACvC,CAAC,MAAM,IACLA,QAAQ,KAAKnB,YAAK,CAACU,MAAM,KACxBQ,QAAQ,KAAKlB,YAAK,CAACM,SAAS,IAAIY,QAAQ,KAAKlB,YAAK,CAACO,MAAM,CAAC,EAC3D;QACAnC,OAAO,CAACkD,SAAS,CAACJ,QAAQ,EAAElB,YAAK,CAACY,KAAK,CAAC;MAC1C;IACF,CAAC,MAAM,IACLO,QAAQ,KAAKnB,YAAK,CAACuB,YAAY,IAC/BL,QAAQ,KAAKlB,YAAK,CAACM,SAAS,EAC5B;MACAlC,OAAO,CAACkD,SAAS,CAACJ,QAAQ,EAAEC,QAAQ,CAAC;IACvC;IAEA,IAAI,CAACrD,uBAAuB,IAAI,CAAC;IAEjC,IAAI,CAACG,+BAA+B,CAAC,CAAC;IAEtC,IAAI,CAAC,IAAI,CAACN,gBAAgB,CAAC6D,QAAQ,CAACpD,OAAO,CAAC,EAAE;MAC5C,IAAI,CAAC2C,uBAAuB,CAAC3C,OAAO,CAAC;IACvC;EACF;EAEQqC,UAAUA,CAACrC,OAAwB,EAAQ;IACjD,MAAMqD,YAAY,GAAGrD,OAAO,CAACiB,KAAK;IAElCjB,OAAO,CAACE,MAAM,GAAG,IAAI;IACrBF,OAAO,CAACsD,mBAAmB,GAAG,IAAI;IAClCtD,OAAO,CAACL,eAAe,GAAG,IAAI,CAACA,eAAe,EAAE;IAEhD,KAAK,IAAImB,CAAC,GAAG,IAAI,CAACxB,eAAe,CAACyB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MACzD,IAAI,IAAI,CAAC4B,0BAA0B,CAAC,IAAI,CAACpD,eAAe,CAACwB,CAAC,CAAC,EAAEd,OAAO,CAAC,EAAE;QACrE,IAAI,CAACV,eAAe,CAACwB,CAAC,CAAC,CAACiB,MAAM,CAAC,CAAC;MAClC;IACF;IAEA,KAAK,MAAMR,YAAY,IAAI,IAAI,CAAChC,gBAAgB,EAAE;MAChD,IAAI,IAAI,CAACmD,0BAA0B,CAACnB,YAAY,EAAEvB,OAAO,CAAC,EAAE;QAC1DuB,YAAY,CAACpB,QAAQ,GAAG,KAAK;MAC/B;IACF;IAEAH,OAAO,CAACkD,SAAS,CAACtB,YAAK,CAACU,MAAM,EAAEV,YAAK,CAACY,KAAK,CAAC;IAE5C,IAAIa,YAAY,KAAKzB,YAAK,CAACU,MAAM,EAAE;MACjCtC,OAAO,CAACkD,SAAS,CAACtB,YAAK,CAACC,GAAG,EAAED,YAAK,CAACU,MAAM,CAAC;MAC1C,IAAIe,YAAY,KAAKzB,YAAK,CAACC,GAAG,EAAE;QAC9B7B,OAAO,CAACkD,SAAS,CAACtB,YAAK,CAACuB,YAAY,EAAEvB,YAAK,CAACC,GAAG,CAAC;MAClD;IACF;IAEA,IAAI,CAAC7B,OAAO,CAACG,QAAQ,EAAE;MACrB;IACF;IAEAH,OAAO,CAACG,QAAQ,GAAG,KAAK;IAExB,IAAI,CAACZ,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAAC4B,MAAM,CACjDI,YAAY,IAAKA,YAAY,KAAKvB,OACrC,CAAC;EACH;EAEQgC,kBAAkBA,CAAChC,OAAwB,EAAQ;IACzD,IAAI,IAAI,CAACT,gBAAgB,CAAC6D,QAAQ,CAACpD,OAAO,CAAC,EAAE;MAC3C;IACF;IAEA,IAAI,CAACT,gBAAgB,CAACgE,IAAI,CAACvD,OAAO,CAAC;IACnC,IAAI,CAACR,oBAAoB,CAAC0B,GAAG,CAAClB,OAAO,CAACY,UAAU,CAAC;IAEjDZ,OAAO,CAACG,QAAQ,GAAG,IAAI;IACvBH,OAAO,CAACL,eAAe,GAAG,IAAI,CAACA,eAAe,EAAE;EAClD;EAEO6D,yBAAyBA,CAACxD,OAAwB,EAAQ;IAC/D,IAAI,IAAI,CAACV,eAAe,CAAC8D,QAAQ,CAACpD,OAAO,CAAC,EAAE;MAC1C;IACF;IAEA,IAAI,CAACV,eAAe,CAACiE,IAAI,CAACvD,OAAO,CAAC;IAElCA,OAAO,CAACE,MAAM,GAAG,KAAK;IACtBF,OAAO,CAACG,QAAQ,GAAG,KAAK;IACxBH,OAAO,CAACL,eAAe,GAAGS,MAAM,CAACqD,gBAAgB;EACnD;EAEQjC,yBAAyBA,CAC/BxB,OAAwB,EACxBuB,YAA6B,EACpB;IACT,OACEvB,OAAO,KAAKuB,YAAY,KACvBvB,OAAO,CAAC0D,2BAA2B,CAACnC,YAAY,CAAC,IAChDA,YAAY,CAACoC,6BAA6B,CAAC3D,OAAO,CAAC,CAAC;EAE1D;EAEQ4D,oBAAoBA,CAC1BC,GAAoB,EACpBC,GAAoB,EACX;IACT,OACED,GAAG,KAAKC,GAAG,IACXD,GAAG,CAACE,6BAA6B,CAACD,GAAG,CAAC,IACtCA,GAAG,CAACC,6BAA6B,CAACF,GAAG,CAAC;EAE1C;EAEQnB,0BAA0BA,CAChC1C,OAAwB,EACxBuB,YAA6B,EACpB;IACT,IAAI,IAAI,CAACqC,oBAAoB,CAAC5D,OAAO,EAAEuB,YAAY,CAAC,EAAE;MACpD,OAAO,KAAK;IACd;IAEA,IAAIvB,OAAO,CAACG,QAAQ,IAAIH,OAAO,CAACiB,KAAK,KAAKW,YAAK,CAACU,MAAM,EAAE;MACtD,OAAOtC,OAAO,CAACgE,wBAAwB,CAACzC,YAAY,CAAC;IACvD;IAEA,MAAM0C,eAAyB,GAAGjE,OAAO,CAACkE,oBAAoB,CAAC,CAAC;IAChE,MAAMC,aAAuB,GAAG5C,YAAY,CAAC2C,oBAAoB,CAAC,CAAC;IAEnE,IACE,CAACE,uBAAc,CAACC,mBAAmB,CAACJ,eAAe,EAAEE,aAAa,CAAC,IACnEnE,OAAO,CAACsE,QAAQ,CAACC,IAAI,KAAKhD,YAAY,CAAC+C,QAAQ,CAACC,IAAI,EACpD;MACA,OAAO,IAAI,CAACC,YAAY,CAACxE,OAAO,EAAEuB,YAAY,CAAC;IACjD;IAEA,OAAO,IAAI;EACb;EAEQiD,YAAYA,CAClBxE,OAAwB,EACxBuB,YAA6B,EACpB;IACT;IACA;IACA;;IAEA;;IAEA,MAAMkD,yBAAyB,GAAIC,OAAe,IAAK;MACrD,MAAMC,KAAK,GAAG3E,OAAO,CAAC4E,OAAO,CAACC,qBAAqB,CAACH,OAAO,CAAC;MAE5D,OACE1E,OAAO,CAACsE,QAAQ,CAACQ,iBAAiB,CAACH,KAAK,CAAC,IACzCpD,YAAY,CAAC+C,QAAQ,CAACQ,iBAAiB,CAACH,KAAK,CAAC;IAElD,CAAC;IAED,OAAO3E,OAAO,CAACkE,oBAAoB,CAAC,CAAC,CAACzC,IAAI,CAACgD,yBAAyB,CAAC;EACvE;EAEQzD,UAAUA,CAACC,KAAY,EAAW;IACxC,OACEA,KAAK,KAAKW,YAAK,CAACC,GAAG,IAAIZ,KAAK,KAAKW,YAAK,CAACO,MAAM,IAAIlB,KAAK,KAAKW,YAAK,CAACM,SAAS;EAE9E;;EAEA;EACA;EACA;EACA;EACA;EACA;EACO6C,yBAAyBA,CAACC,cAA+B,EAAQ;IACtE,IAAI,CAAC1F,eAAe,CAAC2F,OAAO,CAAEjF,OAAwB,IAAK;MACzD,IACEA,OAAO,CAACkF,WAAW,KAAKC,wBAAW,CAACC,KAAK,IACzCpF,OAAO,CAACkF,WAAW,KAAKC,wBAAW,CAACE,MAAM,EAC1C;QACA;MACF;MAEA,IAAIrF,OAAO,KAAKgF,cAAc,EAAE;QAC9BhF,OAAO,CAAC+B,MAAM,CAAC,CAAC;MAClB,CAAC,MAAM;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA/B,OAAO,CAAC4E,OAAO,CAACU,YAAY,CAAC,CAAC;MAChC;IACF,CAAC,CAAC;EACJ;EAEA,WAAkBC,QAAQA,CAAA,EAA+B;IACvD,IAAI,CAAClG,0BAA0B,CAACmG,SAAS,EAAE;MACzCnG,0BAA0B,CAACmG,SAAS,GAAG,IAAInG,0BAA0B,CAAC,CAAC;IACzE;IAEA,OAAOA,0BAA0B,CAACmG,SAAS;EAC7C;AACF;AAACC,OAAA,CAAArG,OAAA,GAAAC,0BAAA", "ignoreList": []}