"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TouchEventType = void 0;
const TouchEventType = exports.TouchEventType = {
  UNDETERMINED: 0,
  TOUCHES_DOWN: 1,
  TOUCHES_MOVE: 2,
  TOUCHES_UP: 3,
  TOUCHES_CANCELLED: 4
};

// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; it can be used as a type and as a value
//# sourceMappingURL=TouchEventType.js.map