{"version": 3, "names": ["_NativeRNGestureHandlerModule", "_interopRequireDefault", "require", "e", "__esModule", "default", "_default", "exports", "<PERSON><PERSON><PERSON>"], "sourceRoot": "../../src", "sources": ["RNGestureHandlerModule.ts"], "mappings": ";;;;;;AAGA,IAAAA,6BAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA0D,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAH1D;AACA;AAAA,IAAAG,QAAA,GAAAC,OAAA,CAAAF,OAAA,GAGeG,qCAAM", "ignoreList": []}