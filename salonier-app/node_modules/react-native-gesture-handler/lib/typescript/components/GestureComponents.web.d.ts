import * as React from 'react';
import { FlatListProps } from 'react-native';
export declare const ScrollView: React.ForwardRefExoticComponent<import("react-native").ScrollViewProps & import("..").NativeViewGestureHandlerProps & React.RefAttributes<React.ComponentType<any>>>;
export declare const Switch: React.ForwardRefExoticComponent<import("react-native").SwitchProps & import("..").NativeViewGestureHandlerProps & React.RefAttributes<React.ComponentType<any>>>;
export declare const TextInput: React.ForwardRefExoticComponent<import("react-native").TextInputProps & import("..").NativeViewGestureHandlerProps & React.RefAttributes<React.ComponentType<any>>>;
export declare const DrawerLayoutAndroid: () => React.JSX.Element;
export declare const RefreshControl: React.ForwardRefExoticComponent<import("react-native").ViewProps & import("..").NativeViewGestureHandlerProps & React.RefAttributes<React.ComponentType<any>>>;
export declare const FlatList: React.ForwardRefExoticComponent<FlatListProps<unknown> & React.RefAttributes<unknown>>;
//# sourceMappingURL=GestureComponents.web.d.ts.map