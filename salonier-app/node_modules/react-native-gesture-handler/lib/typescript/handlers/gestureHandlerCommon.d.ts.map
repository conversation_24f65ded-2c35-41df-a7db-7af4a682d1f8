{"version": 3, "file": "gestureHandlerCommon.d.ts", "sourceRoot": "", "sources": ["../../../src/handlers/gestureHandlerCommon.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACnD,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAqB7C,eAAO,MAAM,uBAAuB,0UAU1B,CAAC;AAEX,eAAO,MAAM,mCAAmC,UAI/C,CAAC;AAEF,MAAM,WAAW,mBAAmB;IAClC,UAAU,EAAE,MAAM,CAAC;IACnB,gBAAgB,EAAE,MAAM,CAAC;IACzB,KAAK,EAAE,OAAO,CAAC,OAAO,KAAK,CAAC,CAAC;IAC7B,WAAW,EAAE,WAAW,CAAC;CAC1B;AACD,MAAM,WAAW,8BAA+B,SAAQ,mBAAmB;IACzE,QAAQ,EAAE,OAAO,CAAC,OAAO,KAAK,CAAC,CAAC;CACjC;AAED,MAAM,MAAM,OAAO,GACf,MAAM,GACN,IAAI,GACJ,SAAS,GACT,OAAO,CACL,MAAM,CACJ,MAAM,GAAG,OAAO,GAAG,KAAK,GAAG,QAAQ,GAAG,UAAU,GAAG,YAAY,EAC/D,MAAM,CACP,CACF,GACD,MAAM,CAAC,OAAO,GAAG,MAAM,EAAE,MAAM,CAAC,GAChC,MAAM,CAAC,OAAO,GAAG,OAAO,EAAE,MAAM,CAAC,GACjC,MAAM,CAAC,QAAQ,GAAG,KAAK,EAAE,MAAM,CAAC,GAChC,MAAM,CAAC,QAAQ,GAAG,QAAQ,EAAE,MAAM,CAAC,CAAC;AAExC,MAAM,MAAM,UAAU,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AAClD,MAAM,MAAM,YAAY,GACpB,MAAM,GACN,SAAS,GACT,MAAM,GACN,cAAc,GACd,MAAM,GACN,SAAS,GACT,UAAU,GACV,MAAM,GACN,MAAM,GACN,WAAW,GACX,MAAM,GACN,eAAe,GACf,OAAO,GACP,MAAM,GACN,MAAM,GACN,SAAS,GACT,aAAa,GACb,MAAM,GACN,UAAU,GACV,UAAU,GACV,UAAU,GACV,WAAW,GACX,WAAW,GACX,UAAU,GACV,WAAW,GACX,WAAW,GACX,UAAU,GACV,WAAW,GACX,WAAW,GACX,aAAa,GACb,aAAa,GACb,YAAY,GACZ,YAAY,GACZ,YAAY,GACZ,SAAS,GACT,UAAU,CAAC;AAEf,oBAAY,WAAW;IACrB,IAAI,IAAI;IACR,KAAK,IAAI;IACT,MAAM,IAAI;IACV,QAAQ,IAAI;IACZ,QAAQ,KAAK;IACb,GAAG,KAAK;CACT;AAED,MAAM,MAAM,WAAW,GACnB,MAAM,GACN,MAAM,GACN,OAAO,GACP,UAAU,GACV,WAAW,GACX,OAAO,GACP,QAAQ,GACR,UAAU,GACV,YAAY,GACZ,cAAc,GACd,SAAS,GACT,SAAS,GACT,QAAQ,GACR,cAAc,GACd,OAAO,CAAC;AAIZ,MAAM,WAAW,YAAY,CAAC,kBAAkB,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;IACxE,WAAW,EAAE,QAAQ,CAAC,mBAAmB,GAAG,kBAAkB,CAAC,CAAC;CACjE;AACD,MAAM,WAAW,uBAAuB,CACtC,kBAAkB,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;IAE5C,WAAW,EAAE,QAAQ,CAAC,8BAA8B,GAAG,kBAAkB,CAAC,CAAC;CAC5E;AAED,MAAM,MAAM,SAAS,GAAG;IACtB,EAAE,EAAE,MAAM,CAAC;IACX,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF,MAAM,MAAM,iBAAiB,GAAG;IAC9B,UAAU,EAAE,MAAM,CAAC;IACnB,eAAe,EAAE,MAAM,CAAC;IACxB,KAAK,EAAE,OAAO,CAAC,OAAO,KAAK,CAAC,CAAC;IAC7B,SAAS,EAAE,cAAc,CAAC;IAC1B,UAAU,EAAE,SAAS,EAAE,CAAC;IACxB,cAAc,EAAE,SAAS,EAAE,CAAC;IAC5B,WAAW,EAAE,WAAW,CAAC;CAC1B,CAAC;AAEF,MAAM,MAAM,kBAAkB,CAAC,oBAAoB,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IAC3E,mBAAmB,GAAG,oBAAoB,CAAC;AAE7C,MAAM,MAAM,uBAAuB,CACjC,+BAA+B,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IACvD,8BAA8B,GAAG,+BAA+B,CAAC;AAErE,MAAM,MAAM,mBAAmB,GAAG;IAChC,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,uBAAuB,CAAC,EAAE,OAAO,CAAC;IAClC,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,UAAU,CAAC,EAAE,UAAU,CAAC;IACxB,YAAY,CAAC,EAAE,YAAY,CAAC;IAC5B,WAAW,CAAC,EAAE,WAAW,CAAC;IAC1B,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B,WAAW,CAAC,EAAE,WAAW,CAAC;CAC3B,CAAC;AAIF,MAAM,MAAM,uBAAuB,CACjC,kBAAkB,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IAC1E,mBAAmB,GAAG;IACxB,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ,OAAO,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;IACpD,oBAAoB,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;IACjE,cAAc,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;IAC3D,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAE/B,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,uBAAuB,KAAK,IAAI,CAAC;IACnD,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,uBAAuB,KAAK,IAAI,CAAC;IACpD,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE,uBAAuB,KAAK,IAAI,CAAC;IACvD,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE,uBAAuB,KAAK,IAAI,CAAC;IACvD,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,uBAAuB,KAAK,IAAI,CAAC;IAGnD,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,YAAY,CAAC,kBAAkB,CAAC,KAAK,IAAI,CAAC;IACnE,oBAAoB,CAAC,EAAE,CACrB,KAAK,EAAE,uBAAuB,CAAC,kBAAkB,CAAC,KAC/C,IAAI,CAAC;IAEV,QAAQ,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC;CAC5B,CAAC"}