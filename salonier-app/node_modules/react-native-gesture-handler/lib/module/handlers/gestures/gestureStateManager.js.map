{"version": 3, "names": ["Reanimated", "State", "tagMessage", "warningMessage", "REANIMATED_AVAILABLE", "useSharedValue", "undefined", "setGestureState", "create", "handlerTag", "begin", "BEGAN", "console", "warn", "activate", "ACTIVE", "fail", "FAILED", "end", "END", "GestureStateManager"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/gestureStateManager.ts"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,qBAAqB;AAChD,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,UAAU,QAAQ,aAAa;AASxC,MAAMC,cAAc,GAAGD,UAAU,CAC/B,kFACF,CAAC;;AAED;AACA;AACA,MAAME,oBAAoB,GAAGJ,UAAU,EAAEK,cAAc,KAAKC,SAAS;AACrE,MAAMC,eAAe,GAAGP,UAAU,EAAEO,eAAe;AAEnD,SAASC,MAAMA,CAACC,UAAkB,EAA2B;EAC3D,SAAS;;EACT,OAAO;IACLC,KAAK,EAAEA,CAAA,KAAM;MACX,SAAS;;MACT,IAAIN,oBAAoB,EAAE;QACxB;QACA;QACAG,eAAe,CAAEE,UAAU,EAAER,KAAK,CAACU,KAAK,CAAC;MAC3C,CAAC,MAAM;QACLC,OAAO,CAACC,IAAI,CAACV,cAAc,CAAC;MAC9B;IACF,CAAC;IAEDW,QAAQ,EAAEA,CAAA,KAAM;MACd,SAAS;;MACT,IAAIV,oBAAoB,EAAE;QACxB;QACA;QACAG,eAAe,CAAEE,UAAU,EAAER,KAAK,CAACc,MAAM,CAAC;MAC5C,CAAC,MAAM;QACLH,OAAO,CAACC,IAAI,CAACV,cAAc,CAAC;MAC9B;IACF,CAAC;IAEDa,IAAI,EAAEA,CAAA,KAAM;MACV,SAAS;;MACT,IAAIZ,oBAAoB,EAAE;QACxB;QACA;QACAG,eAAe,CAAEE,UAAU,EAAER,KAAK,CAACgB,MAAM,CAAC;MAC5C,CAAC,MAAM;QACLL,OAAO,CAACC,IAAI,CAACV,cAAc,CAAC;MAC9B;IACF,CAAC;IAEDe,GAAG,EAAEA,CAAA,KAAM;MACT,SAAS;;MACT,IAAId,oBAAoB,EAAE;QACxB;QACA;QACAG,eAAe,CAAEE,UAAU,EAAER,KAAK,CAACkB,GAAG,CAAC;MACzC,CAAC,MAAM;QACLP,OAAO,CAACC,IAAI,CAACV,cAAc,CAAC;MAC9B;IACF;EACF,CAAC;AACH;AAEA,OAAO,MAAMiB,mBAAmB,GAAG;EACjCZ;AACF,CAAC", "ignoreList": []}