{"version": 3, "names": ["useCallback", "attachHandlers", "updateHandlers", "needsToReattach", "dropHandlers", "useForceRender", "validateDetectorChildren", "findNodeHandle", "useDetectorUpdater", "state", "preparedGesture", "gestures<PERSON>oAtta<PERSON>", "gestureConfig", "webEventHandlersRef", "forceRender", "updateAttachedGestures", "skipConfigUpdate", "viewTag", "viewRef", "didUnderlyingViewChange", "previousViewTag", "forceRebuildReanimatedEvent"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/useDetectorUpdater.ts"], "mappings": ";;AAAA,SAAgBA,WAAW,QAAQ,OAAO;AAS1C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,EAAEC,wBAAwB,QAAQ,SAAS;AAClE,OAAOC,cAAc,MAAM,yBAAyB;;AAEpD;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAChCC,KAA2B,EAC3BC,eAAqC,EACrCC,gBAA+B,EAC/BC,aAA4C,EAC5CC,mBAAqD,EACrD;EACA,MAAMC,WAAW,GAAGT,cAAc,CAAC,CAAC;EACpC,MAAMU,sBAAsB,GAAGf,WAAW;EACxC;EACCgB,gBAA0B,IAAK;IAC9B;IACA,MAAMC,OAAO,GAAGV,cAAc,CAACE,KAAK,CAACS,OAAO,CAAW;IACvD,MAAMC,uBAAuB,GAAGF,OAAO,KAAKR,KAAK,CAACW,eAAe;IAEjE,IACED,uBAAuB,IACvBhB,eAAe,CAACO,eAAe,EAAEC,gBAAgB,CAAC,EAClD;MACAL,wBAAwB,CAACG,KAAK,CAACS,OAAO,CAAC;MACvCd,YAAY,CAACM,eAAe,CAAC;MAC7BT,cAAc,CAAC;QACbS,eAAe;QACfE,aAAa;QACbD,gBAAgB;QAChBE,mBAAmB;QACnBI;MACF,CAAC,CAAC;MAEF,IAAIE,uBAAuB,EAAE;QAC3BV,KAAK,CAACW,eAAe,GAAGH,OAAO;QAC/BR,KAAK,CAACY,2BAA2B,GAAG,IAAI;QACxCP,WAAW,CAAC,CAAC;MACf;IACF,CAAC,MAAM,IAAI,CAACE,gBAAgB,EAAE;MAC5Bd,cAAc,CAACQ,eAAe,EAAEE,aAAa,EAAED,gBAAgB,CAAC;IAClE;EACF,CAAC,EACD,CACEG,WAAW,EACXF,aAAa,EACbD,gBAAgB,EAChBD,eAAe,EACfD,KAAK,EACLI,mBAAmB,CAEvB,CAAC;EAED,OAAOE,sBAAsB;AAC/B", "ignoreList": []}