{"version": 3, "names": ["Platform", "isTestEnv", "tagMessage", "BaseGesture", "flingGestureHandlerProps", "forceTouchGestureHandlerProps", "longPressGestureHandlerProps", "panGestureHandlerProps", "panGestureHandlerCustomNativeProps", "tapGestureHandlerProps", "hoverGestureHandlerProps", "nativeViewGestureHandlerProps", "baseGestureHandlerWithDetectorProps", "isNewWebImplementationEnabled", "<PERSON><PERSON><PERSON><PERSON>", "useCallback", "useRef", "useState", "Reanimated", "onGestureHandlerEvent", "ALLOWED_PROPS", "convertToHandlerTag", "ref", "handlerTag", "current", "extractValidHandlerTags", "interactionGroup", "map", "filter", "tag", "extractGestureRelations", "gesture", "requireToFail", "config", "simultaneousWith", "blocksHandlers", "waitFor", "simultaneousHandlers", "checkGestureCallbacksForWorklets", "__DEV__", "runOnJS", "areSomeNotWorklets", "handlers", "isWorklet", "includes", "areSomeWorklets", "console", "error", "undefined", "areAllNotWorklets", "warn", "validateDetectorChildren", "OS", "wrapType", "_reactInternals", "elementType", "instance", "findHostInstance_DEPRECATED", "_internalFiberInstanceHandleDEV", "sibling", "Error", "return", "useForceRender", "renderState", "setRenderState", "forceRender", "useWebEventHandlers", "e", "nativeEvent", "onGestureHandlerStateChange"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/utils.ts"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,cAAc;AAEvC,SAASC,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AACtD,SAAqBC,WAAW,QAAqB,YAAY;AAEjE,SAASC,wBAAwB,QAAQ,2BAA2B;AACpE,SAASC,6BAA6B,QAAQ,gCAAgC;AAC9E,SAASC,4BAA4B,QAAQ,+BAA+B;AAC5E,SACEC,sBAAsB,EACtBC,kCAAkC,QAC7B,yBAAyB;AAChC,SAASC,sBAAsB,QAAQ,yBAAyB;AAChE,SAASC,wBAAwB,QAAQ,iBAAiB;AAC1D,SAASC,6BAA6B,QAAQ,gCAAgC;AAC9E,SAEEC,mCAAmC,QAC9B,4BAA4B;AACnC,SAASC,6BAA6B,QAAQ,qCAAqC;AACnF,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,WAAW,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACrD,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,qBAAqB,QAAQ,kBAAkB;AAGxD,OAAO,MAAMC,aAAa,GAAG,CAC3B,GAAGR,mCAAmC,EACtC,GAAGH,sBAAsB,EACzB,GAAGF,sBAAsB,EACzB,GAAGC,kCAAkC,EACrC,GAAGF,4BAA4B,EAC/B,GAAGD,6BAA6B,EAChC,GAAGD,wBAAwB,EAC3B,GAAGM,wBAAwB,EAC3B,GAAGC,6BAA6B,CACjC;AAED,SAASU,mBAAmBA,CAACC,GAAe,EAAU;EACpD,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3B,OAAOA,GAAG;EACZ,CAAC,MAAM,IAAIA,GAAG,YAAYnB,WAAW,EAAE;IACrC,OAAOmB,GAAG,CAACC,UAAU;EACvB,CAAC,MAAM;IACL;IACA;IACA,OAAOD,GAAG,CAACE,OAAO,EAAED,UAAU,IAAI,CAAC,CAAC;EACtC;AACF;AAEA,SAASE,uBAAuBA,CAACC,gBAA0C,EAAE;EAC3E,OACEA,gBAAgB,EAAEC,GAAG,CAACN,mBAAmB,CAAC,EAAEO,MAAM,CAAEC,GAAG,IAAKA,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE;AAE9E;AAEA,OAAO,SAASC,uBAAuBA,CAACC,OAAoB,EAAE;EAC5D,MAAMC,aAAa,GAAGP,uBAAuB,CAACM,OAAO,CAACE,MAAM,CAACD,aAAa,CAAC;EAC3E,MAAME,gBAAgB,GAAGT,uBAAuB,CAC9CM,OAAO,CAACE,MAAM,CAACC,gBACjB,CAAC;EACD,MAAMC,cAAc,GAAGV,uBAAuB,CAACM,OAAO,CAACE,MAAM,CAACE,cAAc,CAAC;EAE7E,OAAO;IACLC,OAAO,EAAEJ,aAAa;IACtBK,oBAAoB,EAAEH,gBAAgB;IACtCC,cAAc,EAAEA;EAClB,CAAC;AACH;AAEA,OAAO,SAASG,gCAAgCA,CAACP,OAAoB,EAAE;EACrE,IAAI,CAACQ,OAAO,EAAE;IACZ;EACF;EACA;EACA;EACA,IAAIR,OAAO,CAACE,MAAM,CAACO,OAAO,EAAE;IAC1B;EACF;EAEA,MAAMC,kBAAkB,GAAGV,OAAO,CAACW,QAAQ,CAACC,SAAS,CAACC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAMC,eAAe,GAAGd,OAAO,CAACW,QAAQ,CAACC,SAAS,CAACC,QAAQ,CAAC,IAAI,CAAC;;EAEjE;EACA;EACA,IAAIH,kBAAkB,IAAII,eAAe,EAAE;IACzCC,OAAO,CAACC,KAAK,CACX7C,UAAU,CACR,2QACF,CACF,CAAC;EACH;EAEA,IAAIgB,UAAU,KAAK8B,SAAS,EAAE;IAC5B;IACA;EACF;EAEA,MAAMC,iBAAiB,GAAG,CAACJ,eAAe,IAAIJ,kBAAkB;EAChE;EACA;EACA,IAAIQ,iBAAiB,IAAI,CAAChD,SAAS,CAAC,CAAC,EAAE;IACrC6C,OAAO,CAACI,IAAI,CACVhD,UAAU,CACR,0OACF,CACF,CAAC;EACH;AACF;;AAEA;AACA,OAAO,SAASiD,wBAAwBA,CAAC7B,GAAQ,EAAE;EACjD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIiB,OAAO,IAAIvC,QAAQ,CAACoD,EAAE,KAAK,KAAK,EAAE;IACpC;IACA,MAAMC,QAAQ;IACZ;IACA/B,GAAG,CAACgC,eAAe,CAACC,WAAW;;IAEjC;IACA,IAAIC,QAAQ,GACV1C,UAAU,CAAC2C,2BAA2B,CACpCnC,GACF,CAAC,CAACoC,+BAA+B;;IAEnC;IACA,OAAOF,QAAQ,IAAIA,QAAQ,CAACD,WAAW,KAAKF,QAAQ,EAAE;MACpD;MACA,IAAIG,QAAQ,CAACG,OAAO,EAAE;QACpB,MAAM,IAAIC,KAAK,CACb,mPACF,CAAC;MACH;;MAEA;MACAJ,QAAQ,GAAGA,QAAQ,CAACK,MAAM;IAC5B;EACF;AACF;AAEA,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC/B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMgD,WAAW,GAAGlD,WAAW,CAAC,MAAM;IACpCiD,cAAc,CAAC,CAACD,WAAW,CAAC;EAC9B,CAAC,EAAE,CAACA,WAAW,EAAEC,cAAc,CAAC,CAAC;EAEjC,OAAOC,WAAW;AACpB;AAEA,OAAO,SAASC,mBAAmBA,CAAA,EAAG;EACpC,OAAOlD,MAAM,CAAkB;IAC7BG,qBAAqB,EAAGgD,CAAmC,IAAK;MAC9DhD,qBAAqB,CAACgD,CAAC,CAACC,WAAW,CAAC;IACtC,CAAC;IACDC,2BAA2B,EAAExD,6BAA6B,CAAC,CAAC,GACvDsD,CAAmC,IAAK;MACvChD,qBAAqB,CAACgD,CAAC,CAACC,WAAW,CAAC;IACtC,CAAC,GACDpB;EACN,CAAC,CAAC;AACJ", "ignoreList": []}