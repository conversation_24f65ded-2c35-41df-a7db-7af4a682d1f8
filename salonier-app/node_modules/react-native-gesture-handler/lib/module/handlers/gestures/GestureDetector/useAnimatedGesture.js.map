{"version": 3, "names": ["CALLBACK_TYPE", "Reanimated", "GestureStateManager", "State", "TouchEventType", "tagMessage", "<PERSON><PERSON><PERSON><PERSON>", "type", "gesture", "BEGAN", "onBegin", "START", "onStart", "UPDATE", "onUpdate", "CHANGE", "onChange", "END", "onEnd", "FINALIZE", "onFinalize", "TOUCHES_DOWN", "onTouchesDown", "TOUCHES_MOVE", "onTouchesMove", "TOUCHES_UP", "onTouchesUp", "TOUCHES_CANCELLED", "onTouchesCancelled", "touchEventTypeToCallbackType", "eventType", "UNDEFINED", "runWorklet", "event", "args", "handler", "isWorklet", "console", "warn", "isStateChangeEvent", "oldState", "isTouchEvent", "useAnimatedGesture", "preparedGesture", "needsRebuild", "sharedHandlersCallbacks", "useSharedValue", "lastUpdateEvent", "stateControllers", "callback", "currentCallback", "value", "i", "length", "handlerTag", "UNDETERMINED", "state", "ACTIVE", "undefined", "FAILED", "CANCELLED", "create", "changeEventCalculator", "useEvent", "animatedEventHandler", "animatedHandlers"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/useAnimatedGesture.ts"], "mappings": ";;AAAA,SAA2BA,aAAa,QAAQ,YAAY;AAC5D,SAASC,UAAU,QAAQ,sBAAsB;AAMjD,SACEC,mBAAmB,QAEd,wBAAwB;AAC/B,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,UAAU,QAAQ,gBAAgB;AAG3C,SAASC,UAAUA,CACjBC,IAAmB,EACnBC,OAAkD,EAClD;EACA,SAAS;;EACT,QAAQD,IAAI;IACV,KAAKP,aAAa,CAACS,KAAK;MACtB,OAAOD,OAAO,CAACE,OAAO;IACxB,KAAKV,aAAa,CAACW,KAAK;MACtB,OAAOH,OAAO,CAACI,OAAO;IACxB,KAAKZ,aAAa,CAACa,MAAM;MACvB,OAAOL,OAAO,CAACM,QAAQ;IACzB,KAAKd,aAAa,CAACe,MAAM;MACvB,OAAOP,OAAO,CAACQ,QAAQ;IACzB,KAAKhB,aAAa,CAACiB,GAAG;MACpB,OAAOT,OAAO,CAACU,KAAK;IACtB,KAAKlB,aAAa,CAACmB,QAAQ;MACzB,OAAOX,OAAO,CAACY,UAAU;IAC3B,KAAKpB,aAAa,CAACqB,YAAY;MAC7B,OAAOb,OAAO,CAACc,aAAa;IAC9B,KAAKtB,aAAa,CAACuB,YAAY;MAC7B,OAAOf,OAAO,CAACgB,aAAa;IAC9B,KAAKxB,aAAa,CAACyB,UAAU;MAC3B,OAAOjB,OAAO,CAACkB,WAAW;IAC5B,KAAK1B,aAAa,CAAC2B,iBAAiB;MAClC,OAAOnB,OAAO,CAACoB,kBAAkB;EACrC;AACF;AAEA,SAASC,4BAA4BA,CACnCC,SAAyB,EACV;EACf,SAAS;;EACT,QAAQA,SAAS;IACf,KAAK1B,cAAc,CAACiB,YAAY;MAC9B,OAAOrB,aAAa,CAACqB,YAAY;IACnC,KAAKjB,cAAc,CAACmB,YAAY;MAC9B,OAAOvB,aAAa,CAACuB,YAAY;IACnC,KAAKnB,cAAc,CAACqB,UAAU;MAC5B,OAAOzB,aAAa,CAACyB,UAAU;IACjC,KAAKrB,cAAc,CAACuB,iBAAiB;MACnC,OAAO3B,aAAa,CAAC2B,iBAAiB;EAC1C;EACA,OAAO3B,aAAa,CAAC+B,SAAS;AAChC;AAEA,SAASC,UAAUA,CACjBzB,IAAmB,EACnBC,OAAkD,EAClDyB,KAAuE,EACvE,GAAGC,IAAe,EAClB;EACA,SAAS;;EACT,MAAMC,OAAO,GAAG7B,UAAU,CAACC,IAAI,EAAEC,OAAO,CAAC;EACzC,IAAIA,OAAO,CAAC4B,SAAS,CAAC7B,IAAI,CAAC,EAAE;IAC3B;IACA;IACA4B,OAAO,GAAGF,KAAK,EAAE,GAAGC,IAAI,CAAC;EAC3B,CAAC,MAAM,IAAIC,OAAO,EAAE;IAClBE,OAAO,CAACC,IAAI,CAACjC,UAAU,CAAC,6CAA6C,CAAC,CAAC;EACzE;AACF;AAEA,SAASkC,kBAAkBA,CACzBN,KAAuE,EACrC;EAClC,SAAS;;EACT;EACA,OAAOA,KAAK,CAACO,QAAQ,IAAI,IAAI;AAC/B;AAEA,SAASC,YAAYA,CACnBR,KAAuE,EAC3C;EAC5B,SAAS;;EACT,OAAOA,KAAK,CAACH,SAAS,IAAI,IAAI;AAChC;AAEA,OAAO,SAASY,kBAAkBA,CAChCC,eAAqC,EACrCC,YAAqB,EACrB;EACA,IAAI,CAAC3C,UAAU,EAAE;IACf;EACF;;EAEA;EACA;EACA;EACA,MAAM4C,uBAAuB,GAAG5C,UAAU,CAAC6C,cAAc,CAEvD,IAAI,CAAC;;EAEP;EACA,MAAMC,eAAe,GAAG9C,UAAU,CAAC6C,cAAc,CAE/C,EAAE,CAAC;;EAEL;EACA,MAAME,gBAA2C,GAAG,EAAE;EAEtD,MAAMC,QAAQ,GACZhB,KAAuE,IACpE;IACH,SAAS;;IAET,MAAMiB,eAAe,GAAGL,uBAAuB,CAACM,KAAK;IACrD,IAAI,CAACD,eAAe,EAAE;MACpB;IACF;IAEA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,eAAe,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/C,MAAM5C,OAAO,GAAG0C,eAAe,CAACE,CAAC,CAAC;MAElC,IAAInB,KAAK,CAACqB,UAAU,KAAK9C,OAAO,CAAC8C,UAAU,EAAE;QAC3C;MACF;MAEA,IAAIf,kBAAkB,CAACN,KAAK,CAAC,EAAE;QAC7B,IACEA,KAAK,CAACO,QAAQ,KAAKrC,KAAK,CAACoD,YAAY,IACrCtB,KAAK,CAACuB,KAAK,KAAKrD,KAAK,CAACM,KAAK,EAC3B;UACAuB,UAAU,CAAChC,aAAa,CAACS,KAAK,EAAED,OAAO,EAAEyB,KAAK,CAAC;QACjD,CAAC,MAAM,IACL,CAACA,KAAK,CAACO,QAAQ,KAAKrC,KAAK,CAACM,KAAK,IAC7BwB,KAAK,CAACO,QAAQ,KAAKrC,KAAK,CAACoD,YAAY,KACvCtB,KAAK,CAACuB,KAAK,KAAKrD,KAAK,CAACsD,MAAM,EAC5B;UACAzB,UAAU,CAAChC,aAAa,CAACW,KAAK,EAAEH,OAAO,EAAEyB,KAAK,CAAC;UAC/Cc,eAAe,CAACI,KAAK,CAAC3C,OAAO,CAAC8C,UAAU,CAAC,GAAGI,SAAS;QACvD,CAAC,MAAM,IACLzB,KAAK,CAACO,QAAQ,KAAKP,KAAK,CAACuB,KAAK,IAC9BvB,KAAK,CAACuB,KAAK,KAAKrD,KAAK,CAACc,GAAG,EACzB;UACA,IAAIgB,KAAK,CAACO,QAAQ,KAAKrC,KAAK,CAACsD,MAAM,EAAE;YACnCzB,UAAU,CAAChC,aAAa,CAACiB,GAAG,EAAET,OAAO,EAAEyB,KAAK,EAAE,IAAI,CAAC;UACrD;UACAD,UAAU,CAAChC,aAAa,CAACmB,QAAQ,EAAEX,OAAO,EAAEyB,KAAK,EAAE,IAAI,CAAC;QAC1D,CAAC,MAAM,IACL,CAACA,KAAK,CAACuB,KAAK,KAAKrD,KAAK,CAACwD,MAAM,IAAI1B,KAAK,CAACuB,KAAK,KAAKrD,KAAK,CAACyD,SAAS,KAChE3B,KAAK,CAACuB,KAAK,KAAKvB,KAAK,CAACO,QAAQ,EAC9B;UACA,IAAIP,KAAK,CAACO,QAAQ,KAAKrC,KAAK,CAACsD,MAAM,EAAE;YACnCzB,UAAU,CAAChC,aAAa,CAACiB,GAAG,EAAET,OAAO,EAAEyB,KAAK,EAAE,KAAK,CAAC;UACtD;UACAD,UAAU,CAAChC,aAAa,CAACmB,QAAQ,EAAEX,OAAO,EAAEyB,KAAK,EAAE,KAAK,CAAC;QAC3D;MACF,CAAC,MAAM,IAAIQ,YAAY,CAACR,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACe,gBAAgB,CAACI,CAAC,CAAC,EAAE;UACxBJ,gBAAgB,CAACI,CAAC,CAAC,GAAGlD,mBAAmB,CAAC2D,MAAM,CAAC5B,KAAK,CAACqB,UAAU,CAAC;QACpE;QAEA,IAAIrB,KAAK,CAACH,SAAS,KAAK1B,cAAc,CAACmD,YAAY,EAAE;UACnDvB,UAAU,CACRH,4BAA4B,CAACI,KAAK,CAACH,SAAS,CAAC,EAC7CtB,OAAO,EACPyB,KAAK,EACLe,gBAAgB,CAACI,CAAC,CACpB,CAAC;QACH;MACF,CAAC,MAAM;QACLpB,UAAU,CAAChC,aAAa,CAACa,MAAM,EAAEL,OAAO,EAAEyB,KAAK,CAAC;QAEhD,IAAIzB,OAAO,CAACQ,QAAQ,IAAIR,OAAO,CAACsD,qBAAqB,EAAE;UACrD9B,UAAU,CACRhC,aAAa,CAACe,MAAM,EACpBP,OAAO,EACPA,OAAO,CAACsD,qBAAqB,GAC3B7B,KAAK,EACLc,eAAe,CAACI,KAAK,CAAC3C,OAAO,CAAC8C,UAAU,CAC1C,CACF,CAAC;UAEDP,eAAe,CAACI,KAAK,CAAC3C,OAAO,CAAC8C,UAAU,CAAC,GAAGrB,KAAK;QACnD;MACF;IACF;EACF,CAAC;;EAED;EACA,MAAMA,KAAK,GAAGhC,UAAU,CAAC8D,QAAQ,CAC/Bd,QAAQ,EACR,CAAC,6BAA6B,EAAE,uBAAuB,CAAC,EACxDL,YACF,CAAC;EAEDD,eAAe,CAACqB,oBAAoB,GAAG/B,KAAK;EAC5CU,eAAe,CAACsB,gBAAgB,GAAGpB,uBAAuB;AAC5D", "ignoreList": []}