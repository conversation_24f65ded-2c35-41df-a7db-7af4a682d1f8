{"version": 3, "names": ["BaseGesture", "Gesture", "extendRelation", "currentRelation", "extendWith", "undefined", "ComposedGesture", "gestures", "simultaneousGestures", "requireGesturesToFail", "constructor", "prepareSingleGesture", "gesture", "newConfig", "config", "simultaneousWith", "requireToFail", "prepare", "initialize", "toGestureArray", "flatMap", "SimultaneousGesture", "simultaneousArrays", "map", "filter", "x", "i", "length", "ExclusiveGesture", "gestureArrays", "concat"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/gestureComposition.ts"], "mappings": ";;AAAA,SAASA,WAAW,EAAEC,OAAO,QAAiC,WAAW;AAEzE,SAASC,cAAcA,CACrBC,eAAyC,EACzCC,UAAyB,EACzB;EACA,IAAID,eAAe,KAAKE,SAAS,EAAE;IACjC,OAAO,CAAC,GAAGD,UAAU,CAAC;EACxB,CAAC,MAAM;IACL,OAAO,CAAC,GAAGD,eAAe,EAAE,GAAGC,UAAU,CAAC;EAC5C;AACF;AAEA,OAAO,MAAME,eAAe,SAASL,OAAO,CAAC;EACjCM,QAAQ,GAAc,EAAE;EACxBC,oBAAoB,GAAkB,EAAE;EACxCC,qBAAqB,GAAkB,EAAE;EAEnDC,WAAWA,CAAC,GAAGH,QAAmB,EAAE;IAClC,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC1B;EAEUI,oBAAoBA,CAC5BC,OAAgB,EAChBJ,oBAAmC,EACnCC,qBAAoC,EACpC;IACA,IAAIG,OAAO,YAAYZ,WAAW,EAAE;MAClC,MAAMa,SAAS,GAAG;QAAE,GAAGD,OAAO,CAACE;MAAO,CAAC;;MAEvC;MACA;MACAD,SAAS,CAACE,gBAAgB,GAAGb,cAAc,CACzCW,SAAS,CAACE,gBAAgB,EAC1BP,oBACF,CAAC;MACDK,SAAS,CAACG,aAAa,GAAGd,cAAc,CACtCW,SAAS,CAACG,aAAa,EACvBP,qBACF,CAAC;MAEDG,OAAO,CAACE,MAAM,GAAGD,SAAS;IAC5B,CAAC,MAAM,IAAID,OAAO,YAAYN,eAAe,EAAE;MAC7CM,OAAO,CAACJ,oBAAoB,GAAGA,oBAAoB;MACnDI,OAAO,CAACH,qBAAqB,GAAGA,qBAAqB;MACrDG,OAAO,CAACK,OAAO,CAAC,CAAC;IACnB;EACF;EAEAA,OAAOA,CAAA,EAAG;IACR,KAAK,MAAML,OAAO,IAAI,IAAI,CAACL,QAAQ,EAAE;MACnC,IAAI,CAACI,oBAAoB,CACvBC,OAAO,EACP,IAAI,CAACJ,oBAAoB,EACzB,IAAI,CAACC,qBACP,CAAC;IACH;EACF;EAEAS,UAAUA,CAAA,EAAG;IACX,KAAK,MAAMN,OAAO,IAAI,IAAI,CAACL,QAAQ,EAAE;MACnCK,OAAO,CAACM,UAAU,CAAC,CAAC;IACtB;EACF;EAEAC,cAAcA,CAAA,EAAkB;IAC9B,OAAO,IAAI,CAACZ,QAAQ,CAACa,OAAO,CAAER,OAAO,IAAKA,OAAO,CAACO,cAAc,CAAC,CAAC,CAAC;EACrE;AACF;AAEA,OAAO,MAAME,mBAAmB,SAASf,eAAe,CAAC;EACvDW,OAAOA,CAAA,EAAG;IACR;IACA;IACA,MAAMK,kBAAkB,GAAG,IAAI,CAACf,QAAQ,CAACgB,GAAG,CAAEX,OAAO;IACnD;IACA,IAAI,CAACL;IACH;IAAA,CACCiB,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAKb,OAAO;IAC5B;IACA;IACA;IACA;IAAA,CACCQ,OAAO,CAAEK,CAAC,IAAKA,CAAC,CAACN,cAAc,CAAC,CAAC,CACtC,CAAC;IAED,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACnB,QAAQ,CAACoB,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,IAAI,CAACf,oBAAoB,CACvB,IAAI,CAACJ,QAAQ,CAACmB,CAAC,CAAC,EAChBJ,kBAAkB,CAACI,CAAC,CAAC,EACrB,IAAI,CAACjB,qBACP,CAAC;IACH;EACF;AACF;AAEA,OAAO,MAAMmB,gBAAgB,SAAStB,eAAe,CAAC;EACpDW,OAAOA,CAAA,EAAG;IACR;IACA;IACA,MAAMY,aAAa,GAAG,IAAI,CAACtB,QAAQ,CAACgB,GAAG,CAAEX,OAAO,IAC9CA,OAAO,CAACO,cAAc,CAAC,CACzB,CAAC;IAED,IAAIH,aAA4B,GAAG,EAAE;IAErC,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACnB,QAAQ,CAACoB,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,IAAI,CAACf,oBAAoB,CACvB,IAAI,CAACJ,QAAQ,CAACmB,CAAC,CAAC,EAChB,IAAI,CAAClB,oBAAoB,EACzB,IAAI,CAACC,qBAAqB,CAACqB,MAAM,CAACd,aAAa,CACjD,CAAC;;MAED;MACAA,aAAa,GAAGA,aAAa,CAACc,MAAM,CAACD,aAAa,CAACH,CAAC,CAAC,CAAC;IACxD;EACF;AACF", "ignoreList": []}