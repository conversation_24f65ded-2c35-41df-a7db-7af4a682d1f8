{"version": 3, "names": ["BaseGesture", "LongPressGesture", "config", "constructor", "handler<PERSON>ame", "shouldCancelWhenOutside", "minDuration", "duration", "minDurationMs", "maxDistance", "distance", "maxDist", "numberOfPointers", "pointers"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/longPressGesture.ts"], "mappings": ";;AAAA,SAASA,WAAW,QAA2B,WAAW;AAI1D,OAAO,MAAMC,gBAAgB,SAASD,WAAW,CAAsC;EAC9EE,MAAM,GAA+C,CAAC,CAAC;EAE9DC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,WAAW,GAAG,yBAAyB;IAC5C,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAAC;EACpC;;EAEA;AACF;AACA;AACA;AACA;EACEC,WAAWA,CAACC,QAAgB,EAAE;IAC5B,IAAI,CAACL,MAAM,CAACM,aAAa,GAAGD,QAAQ;IACpC,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEE,WAAWA,CAACC,QAAgB,EAAE;IAC5B,IAAI,CAACR,MAAM,CAACS,OAAO,GAAGD,QAAQ;IAC9B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACEE,gBAAgBA,CAACC,QAAgB,EAAE;IACjC,IAAI,CAACX,MAAM,CAACU,gBAAgB,GAAGC,QAAQ;IACvC,OAAO,IAAI;EACb;AACF", "ignoreList": []}