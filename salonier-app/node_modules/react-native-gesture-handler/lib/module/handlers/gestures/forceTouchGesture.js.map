{"version": 3, "names": ["ContinousBaseGesture", "changeEventCalculator", "current", "previous", "changePayload", "undefined", "forceChange", "force", "ForceTouchGesture", "config", "constructor", "handler<PERSON>ame", "minForce", "max<PERSON><PERSON>ce", "feedbackOnActivation", "value", "onChange", "callback", "handlers"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/forceTouchGesture.ts"], "mappings": ";;AAAA,SAA4BA,oBAAoB,QAAQ,WAAW;AASnE,SAASC,qBAAqBA,CAC5BC,OAAiE,EACjEC,QAAmE,EACnE;EACA,SAAS;;EACT,IAAIC,aAAkD;EACtD,IAAID,QAAQ,KAAKE,SAAS,EAAE;IAC1BD,aAAa,GAAG;MACdE,WAAW,EAAEJ,OAAO,CAACK;IACvB,CAAC;EACH,CAAC,MAAM;IACLH,aAAa,GAAG;MACdE,WAAW,EAAEJ,OAAO,CAACK,KAAK,GAAGJ,QAAQ,CAACI;IACxC,CAAC;EACH;EAEA,OAAO;IAAE,GAAGL,OAAO;IAAE,GAAGE;EAAc,CAAC;AACzC;AAEA,OAAO,MAAMI,iBAAiB,SAASR,oBAAoB,CAGzD;EACOS,MAAM,GAAgD,CAAC,CAAC;EAE/DC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,WAAW,GAAG,0BAA0B;EAC/C;;EAEA;AACF;AACA;AACA;AACA;EACEC,QAAQA,CAACL,KAAa,EAAE;IACtB,IAAI,CAACE,MAAM,CAACG,QAAQ,GAAGL,KAAK;IAC5B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEM,QAAQA,CAACN,KAAa,EAAE;IACtB,IAAI,CAACE,MAAM,CAACI,QAAQ,GAAGN,KAAK;IAC5B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACEO,oBAAoBA,CAACC,KAAc,EAAE;IACnC,IAAI,CAACN,MAAM,CAACK,oBAAoB,GAAGC,KAAK;IACxC,OAAO,IAAI;EACb;EAEAC,QAAQA,CACNC,QAOS,EACT;IACA;IACA,IAAI,CAACC,QAAQ,CAACjB,qBAAqB,GAAGA,qBAAqB;IAC3D,OAAO,KAAK,CAACe,QAAQ,CAACC,QAAQ,CAAC;EACjC;AACF", "ignoreList": []}