{"version": 3, "names": ["tagMessage", "Reanimated", "require", "e", "undefined", "useSharedValue", "setGestureState", "console", "warn"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/reanimatedWrapper.ts"], "mappings": ";;AAKA,SAASA,UAAU,QAAQ,aAAa;AAMxC,IAAIC,UAiBS;AAEb,IAAI;EACFA,UAAU,GAAGC,OAAO,CAAC,yBAAyB,CAAC;AACjD,CAAC,CAAC,OAAOC,CAAC,EAAE;EACV;EACA;EACAF,UAAU,GAAGG,SAAS;AACxB;AAEA,IAAI,CAACH,UAAU,EAAEI,cAAc,EAAE;EAC/B;EACA;EACAJ,UAAU,GAAGG,SAAS;AACxB;AAEA,IAAIH,UAAU,KAAKG,SAAS,IAAI,CAACH,UAAU,CAACK,eAAe,EAAE;EAC3D;EACAL,UAAU,CAACK,eAAe,GAAG,MAAM;IACjC,SAAS;;IACTC,OAAO,CAACC,IAAI,CACVR,UAAU,CACR,gGACF,CACF,CAAC;EACH,CAAC;AACH;AAEA,SAASC,UAAU", "ignoreList": []}