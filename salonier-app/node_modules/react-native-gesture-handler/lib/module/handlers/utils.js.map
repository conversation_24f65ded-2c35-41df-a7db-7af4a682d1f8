{"version": 3, "names": ["Platform", "findNodeHandle", "findNodeHandleRN", "handlerIDToTag", "toArray", "RNGestureHandlerModule", "ghQueueMicrotask", "isConfigParam", "param", "name", "undefined", "Object", "filterConfig", "props", "validProps", "defaults", "filteredConfig", "key", "value", "transformIntoHandlerTags", "top", "left", "bottom", "right", "handlerIDs", "OS", "map", "current", "filter", "handle", "handlerID", "handlerTag", "node", "flushOperationsScheduled", "scheduleFlushOperations", "flushOperations"], "sourceRoot": "../../../src", "sources": ["handlers/utils.ts"], "mappings": ";;AACA,SAASA,QAAQ,EAAEC,cAAc,IAAIC,gBAAgB,QAAQ,cAAc;AAC3E,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,OAAO,QAAQ,UAAU;AAClC,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,SAASC,gBAAgB,QAAQ,qBAAqB;AAEtD,SAASC,aAAaA,CAACC,KAAc,EAAEC,IAAY,EAAE;EACnD;EACA;EACA,OACED,KAAK,KAAKE,SAAS,KAClBF,KAAK,KAAKG,MAAM,CAACH,KAAK,CAAC,IACtB,EAAE,YAAY,IAAKA,KAAiC,CAAC,CAAC,IACxDC,IAAI,KAAK,sBAAsB,IAC/BA,IAAI,KAAK,gBAAgB;AAE7B;AAEA,OAAO,SAASG,YAAYA,CAC1BC,KAA8B,EAC9BC,UAAoB,EACpBC,QAAiC,GAAG,CAAC,CAAC,EACtC;EACA,MAAMC,cAAc,GAAG;IAAE,GAAGD;EAAS,CAAC;EACtC,KAAK,MAAME,GAAG,IAAIH,UAAU,EAAE;IAC5B,IAAII,KAAK,GAAGL,KAAK,CAACI,GAAG,CAAC;IACtB,IAAIV,aAAa,CAACW,KAAK,EAAED,GAAG,CAAC,EAAE;MAC7B,IAAIA,GAAG,KAAK,sBAAsB,IAAIA,GAAG,KAAK,SAAS,EAAE;QACvDC,KAAK,GAAGC,wBAAwB,CAACN,KAAK,CAACI,GAAG,CAAC,CAAC;MAC9C,CAAC,MAAM,IAAIA,GAAG,KAAK,SAAS,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;QACzDA,KAAK,GAAG;UAAEE,GAAG,EAAEF,KAAK;UAAEG,IAAI,EAAEH,KAAK;UAAEI,MAAM,EAAEJ,KAAK;UAAEK,KAAK,EAAEL;QAAM,CAAC;MAClE;MACAF,cAAc,CAACC,GAAG,CAAC,GAAGC,KAAK;IAC7B;EACF;EACA,OAAOF,cAAc;AACvB;AAEA,OAAO,SAASG,wBAAwBA,CAACK,UAAe,EAAE;EACxDA,UAAU,GAAGpB,OAAO,CAACoB,UAAU,CAAC;EAEhC,IAAIxB,QAAQ,CAACyB,EAAE,KAAK,KAAK,EAAE;IACzB,OAAOD,UAAU,CACdE,GAAG,CAAC,CAAC;MAAEC;IAA0B,CAAC,KAAKA,OAAO,CAAC,CAC/CC,MAAM,CAAEC,MAAW,IAAKA,MAAM,CAAC;EACpC;EACA;EACA,OAAOL,UAAU,CACdE,GAAG,CACDI,SAAc,IACb3B,cAAc,CAAC2B,SAAS,CAAC,IAAIA,SAAS,CAACH,OAAO,EAAEI,UAAU,IAAI,CAAC,CACnE,CAAC,CACAH,MAAM,CAAEG,UAAkB,IAAKA,UAAU,GAAG,CAAC,CAAC;AACnD;AAEA,OAAO,SAAS9B,cAAcA,CAC5B+B,IAA2E,EACJ;EACvE,IAAIhC,QAAQ,CAACyB,EAAE,KAAK,KAAK,EAAE;IACzB,OAAOO,IAAI;EACb;EACA,OAAO9B,gBAAgB,CAAC8B,IAAI,CAAC,IAAI,IAAI;AACvC;AACA,IAAIC,wBAAwB,GAAG,KAAK;AAEpC,OAAO,SAASC,uBAAuBA,CAAA,EAAG;EACxC,IAAI,CAACD,wBAAwB,EAAE;IAC7BA,wBAAwB,GAAG,IAAI;IAC/B3B,gBAAgB,CAAC,MAAM;MACrBD,sBAAsB,CAAC8B,eAAe,CAAC,CAAC;MAExCF,wBAAwB,GAAG,KAAK;IAClC,CAAC,CAAC;EACJ;AACF", "ignoreList": []}