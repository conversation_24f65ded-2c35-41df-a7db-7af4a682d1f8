{"version": 3, "names": ["React", "FlatList", "RNFlatList", "Switch", "RNSwitch", "TextInput", "RNTextInput", "ScrollView", "RNScrollView", "View", "createNativeWrapper", "jsx", "_jsx", "disallowInterruption", "shouldCancelWhenOutside", "shouldActivateOnStart", "DrawerLayoutAndroid", "console", "warn", "RefreshControl", "forwardRef", "props", "ref", "renderScrollComponent", "scrollProps"], "sourceRoot": "../../../src", "sources": ["components/GestureComponents.web.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,IAAIC,UAAU,EACtBC,MAAM,IAAIC,QAAQ,EAClBC,SAAS,IAAIC,WAAW,EACxBC,UAAU,IAAIC,YAAY,EAE1BC,IAAI,QACC,cAAc;AAErB,OAAOC,mBAAmB,MAAM,iCAAiC;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAElE,OAAO,MAAML,UAAU,GAAGG,mBAAmB,CAACF,YAAY,EAAE;EAC1DK,oBAAoB,EAAE;AACxB,CAAC,CAAC;AAEF,OAAO,MAAMV,MAAM,GAAGO,mBAAmB,CAACN,QAAQ,EAAE;EAClDU,uBAAuB,EAAE,KAAK;EAC9BC,qBAAqB,EAAE,IAAI;EAC3BF,oBAAoB,EAAE;AACxB,CAAC,CAAC;AACF,OAAO,MAAMR,SAAS,GAAGK,mBAAmB,CAACJ,WAAW,CAAC;AACzD,OAAO,MAAMU,mBAAmB,GAAGA,CAAA,KAAM;EACvCC,OAAO,CAACC,IAAI,CAAC,8CAA8C,CAAC;EAC5D,oBAAON,IAAA,CAACH,IAAI,IAAE,CAAC;AACjB,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMU,cAAc,GAAGT,mBAAmB,CAACD,IAAI,CAAC;AAEvD,OAAO,MAAMR,QAAQ,gBAAGD,KAAK,CAACoB,UAAU,CACtC,CAASC,KAA2B,EAAEC,GAAQ,kBAC5CV,IAAA,CAACV,UAAU;EACToB,GAAG,EAAEA,GAAI;EAAA,GACLD,KAAK;EACTE,qBAAqB,EAAGC,WAAW,iBAAKZ,IAAA,CAACL,UAAU;IAAA,GAAKiB;EAAW,CAAG;AAAE,CACzE,CAEL,CAAC", "ignoreList": []}