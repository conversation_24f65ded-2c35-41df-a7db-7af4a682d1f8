{"version": 3, "names": ["React", "View", "jsx", "_jsx", "forwardRef", "props", "ref", "accessibilityRole"], "sourceRoot": "../../../src", "sources": ["components/GestureHandlerButton.web.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAEpC,4BAAeH,KAAK,CAACI,UAAU,CAC7B,CAACC,KAAK,EAAEC,GAAG,kBAAKH,IAAA,CAACF,IAAI;EAACK,GAAG,EAAEA,GAAI;EAACC,iBAAiB,EAAC,QAAQ;EAAA,GAAKF;AAAK,CAAG,CACzE,CAAC", "ignoreList": []}