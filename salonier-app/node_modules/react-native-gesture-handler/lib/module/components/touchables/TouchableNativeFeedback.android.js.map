{"version": 3, "names": ["Platform", "React", "Component", "GenericTouchable", "jsx", "_jsx", "TouchableNativeFeedback", "defaultProps", "useForeground", "extraButtonProps", "rippleColor", "SelectableBackground", "rippleRadius", "type", "attribute", "SelectableBackgroundBorderless", "<PERSON><PERSON><PERSON>", "color", "borderless", "canUseNativeForeground", "OS", "Version", "getExtraButtonProps", "extraProps", "background", "props", "render", "style", "rest"], "sourceRoot": "../../../../src", "sources": ["components/touchables/TouchableNativeFeedback.android.tsx"], "mappings": ";;AAAA,SAASA,QAAQ,QAAoB,cAAc;AACnD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAMlD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMC,uBAAuB,SAASJ,SAAS,CAA+B;EAC3F,OAAOK,YAAY,GAAG;IACpB,GAAGJ,gBAAgB,CAACI,YAAY;IAChCC,aAAa,EAAE,IAAI;IACnBC,gBAAgB,EAAE;MAChB;MACAC,WAAW,EAAE;IACf;EACF,CAAC;;EAED;EACA,OAAOC,oBAAoB,GAAIC,YAAqB,KAAM;IACxDC,IAAI,EAAE,kBAAkB;IACxB;IACAC,SAAS,EAAE,0BAA0B;IACrCF;EACF,CAAC,CAAC;EACF,OAAOG,8BAA8B,GAAIH,YAAqB,KAAM;IAClEC,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAE,oCAAoC;IAC/CF;EACF,CAAC,CAAC;EACF,OAAOI,MAAM,GAAGA,CACdC,KAAiB,EACjBC,UAAmB,EACnBN,YAAqB,MACjB;IACJC,IAAI,EAAE,eAAe;IACrBI,KAAK;IACLC,UAAU;IACVN;EACF,CAAC,CAAC;EAEF,OAAOO,sBAAsB,GAAGA,CAAA,KAC9BnB,QAAQ,CAACoB,EAAE,KAAK,SAAS,IAAIpB,QAAQ,CAACqB,OAAO,IAAI,EAAE;EAErDC,mBAAmBA,CAAA,EAAG;IACpB,MAAMC,UAA6C,GAAG,CAAC,CAAC;IACxD,MAAM;MAAEC;IAAW,CAAC,GAAG,IAAI,CAACC,KAAK;IACjC,IAAID,UAAU,EAAE;MACd;MACA;MACA,IAAIA,UAAU,CAACX,IAAI,KAAK,eAAe,EAAE;QACvCU,UAAU,CAAC,YAAY,CAAC,GAAGC,UAAU,CAACN,UAAU;QAChDK,UAAU,CAAC,aAAa,CAAC,GAAGC,UAAU,CAACP,KAAK;MAC9C,CAAC,MAAM,IAAIO,UAAU,CAACX,IAAI,KAAK,kBAAkB,EAAE;QACjDU,UAAU,CAAC,YAAY,CAAC,GACtBC,UAAU,CAACV,SAAS,KAAK,oCAAoC;MACjE;MACA;MACAS,UAAU,CAAC,cAAc,CAAC,GAAGC,UAAU,CAACZ,YAAY;IACtD;IACAW,UAAU,CAAC,YAAY,CAAC,GAAG,IAAI,CAACE,KAAK,CAACjB,aAAa;IACnD,OAAOe,UAAU;EACnB;EACAG,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC,KAAK,GAAG,CAAC,CAAC;MAAE,GAAGC;IAAK,CAAC,GAAG,IAAI,CAACH,KAAK;IAC1C,oBACEpB,IAAA,CAACF,gBAAgB;MAAA,GACXyB,IAAI;MACRD,KAAK,EAAEA,KAAM;MACblB,gBAAgB,EAAE,IAAI,CAACa,mBAAmB,CAAC;IAAE,CAC9C,CAAC;EAEN;AACF", "ignoreList": []}