{"version": 3, "names": ["findFocusedRoute", "getActionFromState", "getActionFromStateDefault", "getPathFromState", "getPathFromStateDefault", "getStateFromPath", "getStateFromPathDefault", "useNavigationIndependentTree", "isEqual", "React", "createMemoryHistory", "ServerContext", "findMatchingState", "a", "b", "undefined", "key", "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "history", "length", "routes", "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aRoute", "index", "bRoute", "aChildState", "state", "bChildState", "series", "cb", "queue", "Promise", "resolve", "callback", "then", "linkingHandlers", "useLinking", "ref", "enabled", "config", "onUnhandledLinking", "independent", "useEffect", "process", "env", "NODE_ENV", "console", "error", "join", "trim", "handler", "Symbol", "push", "indexOf", "splice", "useState", "enabledRef", "useRef", "configRef", "getStateFromPathRef", "getPathFromStateRef", "getActionFromStateRef", "current", "validateRoutesNotExistInRootState", "useCallback", "navigation", "rootState", "getRootState", "some", "r", "routeNames", "includes", "name", "server", "useContext", "getInitialState", "value", "location", "window", "path", "pathname", "search", "thenable", "onfulfilled", "catch", "previousIndexRef", "previousStateRef", "pendingPopStatePathRef", "listen", "previousIndex", "record", "get", "resetRoot", "action", "dispatch", "e", "warn", "message", "getPathForRoute", "route", "stateForPath", "focusedRoute", "params", "previousRoute", "hash", "replace", "onStateChange", "previousState", "pending<PERSON><PERSON>", "previousFocusedState", "focusedState", "history<PERSON><PERSON><PERSON>", "nextIndex", "backIndex", "currentIndex", "go", "addListener"], "sourceRoot": "../../src", "sources": ["useLinking.tsx"], "mappings": ";;AAAA,SACEA,gBAAgB,EAChBC,kBAAkB,IAAIC,yBAAyB,EAC/CC,gBAAgB,IAAIC,uBAAuB,EAC3CC,gBAAgB,IAAIC,uBAAuB,EAI3CC,4BAA4B,QACvB,wBAAwB;AAC/B,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,SAASC,mBAAmB,QAAQ,0BAAuB;AAC3D,SAASC,aAAa,QAAQ,oBAAiB;AAK/C;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAGA,CACxBC,CAAgB,EAChBC,CAAgB,KACmB;EACnC,IAAID,CAAC,KAAKE,SAAS,IAAID,CAAC,KAAKC,SAAS,IAAIF,CAAC,CAACG,GAAG,KAAKF,CAAC,CAACE,GAAG,EAAE;IACzD,OAAO,CAACD,SAAS,EAAEA,SAAS,CAAC;EAC/B;;EAEA;EACA,MAAME,cAAc,GAAGJ,CAAC,CAACK,OAAO,GAAGL,CAAC,CAACK,OAAO,CAACC,MAAM,GAAGN,CAAC,CAACO,MAAM,CAACD,MAAM;EACrE,MAAME,cAAc,GAAGP,CAAC,CAACI,OAAO,GAAGJ,CAAC,CAACI,OAAO,CAACC,MAAM,GAAGL,CAAC,CAACM,MAAM,CAACD,MAAM;EAErE,MAAMG,MAAM,GAAGT,CAAC,CAACO,MAAM,CAACP,CAAC,CAACU,KAAK,CAAC;EAChC,MAAMC,MAAM,GAAGV,CAAC,CAACM,MAAM,CAACN,CAAC,CAACS,KAAK,CAAC;EAEhC,MAAME,WAAW,GAAGH,MAAM,CAACI,KAAsB;EACjD,MAAMC,WAAW,GAAGH,MAAM,CAACE,KAAsB;;EAEjD;EACA;EACA;EACA;EACA;EACA,IACET,cAAc,KAAKI,cAAc,IACjCC,MAAM,CAACN,GAAG,KAAKQ,MAAM,CAACR,GAAG,IACzBS,WAAW,KAAKV,SAAS,IACzBY,WAAW,KAAKZ,SAAS,IACzBU,WAAW,CAACT,GAAG,KAAKW,WAAW,CAACX,GAAG,EACnC;IACA,OAAO,CAACH,CAAC,EAAEC,CAAC,CAAC;EACf;EAEA,OAAOF,iBAAiB,CAACa,WAAW,EAAEE,WAAW,CAAC;AACpD,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,MAAM,GAAIC,EAAuB,IAAK;EACjD,IAAIC,KAAK,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB;IACAH,KAAK,GAAGA,KAAK,CAACI,IAAI,CAACL,EAAE,CAAC;EACxB,CAAC;EACD,OAAOI,QAAQ;AACjB,CAAC;AAED,MAAME,eAAyB,GAAG,EAAE;AAIpC,OAAO,SAASC,UAAUA,CACxBC,GAAkE,EAClE;EACEC,OAAO,GAAG,IAAI;EACdC,MAAM;EACNlC,gBAAgB,GAAGC,uBAAuB;EAC1CH,gBAAgB,GAAGC,uBAAuB;EAC1CH,kBAAkB,GAAGC;AACd,CAAC,EACVsC,kBAAqE,EACrE;EACA,MAAMC,WAAW,GAAGlC,4BAA4B,CAAC,CAAC;EAElDE,KAAK,CAACiC,SAAS,CAAC,MAAM;IACpB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,OAAO9B,SAAS;IAClB;IAEA,IAAI0B,WAAW,EAAE;MACf,OAAO1B,SAAS;IAClB;IAEA,IAAIuB,OAAO,KAAK,KAAK,IAAIH,eAAe,CAAChB,MAAM,EAAE;MAC/C2B,OAAO,CAACC,KAAK,CACX,CACE,6KAA6K,EAC7K,uFAAuF,EACvF,4DAA4D,CAC7D,CACEC,IAAI,CAAC,IAAI,CAAC,CACVC,IAAI,CAAC,CACV,CAAC;IACH;IAEA,MAAMC,OAAO,GAAGC,MAAM,CAAC,CAAC;IAExB,IAAIb,OAAO,KAAK,KAAK,EAAE;MACrBH,eAAe,CAACiB,IAAI,CAACF,OAAO,CAAC;IAC/B;IAEA,OAAO,MAAM;MACX,MAAM3B,KAAK,GAAGY,eAAe,CAACkB,OAAO,CAACH,OAAO,CAAC;MAE9C,IAAI3B,KAAK,GAAG,CAAC,CAAC,EAAE;QACdY,eAAe,CAACmB,MAAM,CAAC/B,KAAK,EAAE,CAAC,CAAC;MAClC;IACF,CAAC;EACH,CAAC,EAAE,CAACe,OAAO,EAAEG,WAAW,CAAC,CAAC;EAE1B,MAAM,CAACvB,OAAO,CAAC,GAAGT,KAAK,CAAC8C,QAAQ,CAAC7C,mBAAmB,CAAC;;EAErD;EACA;EACA;EACA,MAAM8C,UAAU,GAAG/C,KAAK,CAACgD,MAAM,CAACnB,OAAO,CAAC;EACxC,MAAMoB,SAAS,GAAGjD,KAAK,CAACgD,MAAM,CAAClB,MAAM,CAAC;EACtC,MAAMoB,mBAAmB,GAAGlD,KAAK,CAACgD,MAAM,CAACpD,gBAAgB,CAAC;EAC1D,MAAMuD,mBAAmB,GAAGnD,KAAK,CAACgD,MAAM,CAACtD,gBAAgB,CAAC;EAC1D,MAAM0D,qBAAqB,GAAGpD,KAAK,CAACgD,MAAM,CAACxD,kBAAkB,CAAC;EAE9DQ,KAAK,CAACiC,SAAS,CAAC,MAAM;IACpBc,UAAU,CAACM,OAAO,GAAGxB,OAAO;IAC5BoB,SAAS,CAACI,OAAO,GAAGvB,MAAM;IAC1BoB,mBAAmB,CAACG,OAAO,GAAGzD,gBAAgB;IAC9CuD,mBAAmB,CAACE,OAAO,GAAG3D,gBAAgB;IAC9C0D,qBAAqB,CAACC,OAAO,GAAG7D,kBAAkB;EACpD,CAAC,CAAC;EAEF,MAAM8D,iCAAiC,GAAGtD,KAAK,CAACuD,WAAW,CACxDtC,KAAkB,IAAK;IACtB,MAAMuC,UAAU,GAAG5B,GAAG,CAACyB,OAAO;IAC9B,MAAMI,SAAS,GAAGD,UAAU,EAAEE,YAAY,CAAC,CAAC;IAC5C;IACA;IACA,OAAOzC,KAAK,EAAEN,MAAM,CAACgD,IAAI,CAAEC,CAAC,IAAK,CAACH,SAAS,EAAEI,UAAU,CAACC,QAAQ,CAACF,CAAC,CAACG,IAAI,CAAC,CAAC;EAC3E,CAAC,EACD,CAACnC,GAAG,CACN,CAAC;EAED,MAAMoC,MAAM,GAAGhE,KAAK,CAACiE,UAAU,CAAC/D,aAAa,CAAC;EAE9C,MAAMgE,eAAe,GAAGlE,KAAK,CAACuD,WAAW,CAAC,MAAM;IAC9C,IAAIY,KAA8B;IAElC,IAAIpB,UAAU,CAACM,OAAO,EAAE;MACtB,MAAMe,QAAQ,GACZJ,MAAM,EAAEI,QAAQ,KACf,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACD,QAAQ,GAAG9D,SAAS,CAAC;MAE/D,MAAMgE,IAAI,GAAGF,QAAQ,GAAGA,QAAQ,CAACG,QAAQ,GAAGH,QAAQ,CAACI,MAAM,GAAGlE,SAAS;MAEvE,IAAIgE,IAAI,EAAE;QACRH,KAAK,GAAGjB,mBAAmB,CAACG,OAAO,CAACiB,IAAI,EAAErB,SAAS,CAACI,OAAO,CAAC;MAC9D;;MAEA;MACAtB,kBAAkB,CAACuC,IAAI,CAAC;IAC1B;IAEA,MAAMG,QAAQ,GAAG;MACfhD,IAAIA,CAACiD,WAAsD,EAAE;QAC3D,OAAOpD,OAAO,CAACC,OAAO,CAACmD,WAAW,GAAGA,WAAW,CAACP,KAAK,CAAC,GAAGA,KAAK,CAAC;MAClE,CAAC;MACDQ,KAAKA,CAAA,EAAG;QACN,OAAOF,QAAQ;MACjB;IACF,CAAC;IAED,OAAOA,QAAQ;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,gBAAgB,GAAG5E,KAAK,CAACgD,MAAM,CAAqB1C,SAAS,CAAC;EACpE,MAAMuE,gBAAgB,GAAG7E,KAAK,CAACgD,MAAM,CAA8B1C,SAAS,CAAC;EAC7E,MAAMwE,sBAAsB,GAAG9E,KAAK,CAACgD,MAAM,CAAqB1C,SAAS,CAAC;EAE1EN,KAAK,CAACiC,SAAS,CAAC,MAAM;IACpB2C,gBAAgB,CAACvB,OAAO,GAAG5C,OAAO,CAACK,KAAK;IAExC,OAAOL,OAAO,CAACsE,MAAM,CAAC,MAAM;MAC1B,MAAMvB,UAAU,GAAG5B,GAAG,CAACyB,OAAO;MAE9B,IAAI,CAACG,UAAU,IAAI,CAAC3B,OAAO,EAAE;QAC3B;MACF;MAEA,MAAM;QAAEuC;MAAS,CAAC,GAAGC,MAAM;MAE3B,MAAMC,IAAI,GAAGF,QAAQ,CAACG,QAAQ,GAAGH,QAAQ,CAACI,MAAM;MAChD,MAAM1D,KAAK,GAAGL,OAAO,CAACK,KAAK;MAE3B,MAAMkE,aAAa,GAAGJ,gBAAgB,CAACvB,OAAO,IAAI,CAAC;MAEnDuB,gBAAgB,CAACvB,OAAO,GAAGvC,KAAK;MAChCgE,sBAAsB,CAACzB,OAAO,GAAGiB,IAAI;;MAErC;MACA;MACA;MACA,MAAMW,MAAM,GAAGxE,OAAO,CAACyE,GAAG,CAACpE,KAAK,CAAC;MAEjC,IAAImE,MAAM,EAAEX,IAAI,KAAKA,IAAI,IAAIW,MAAM,EAAEhE,KAAK,EAAE;QAC1CuC,UAAU,CAAC2B,SAAS,CAACF,MAAM,CAAChE,KAAK,CAAC;QAClC;MACF;MAEA,MAAMA,KAAK,GAAGiC,mBAAmB,CAACG,OAAO,CAACiB,IAAI,EAAErB,SAAS,CAACI,OAAO,CAAC;;MAElE;MACA;MACA,IAAIpC,KAAK,EAAE;QACT;QACAc,kBAAkB,CAACuC,IAAI,CAAC;QACxB;QACA;QACA,IAAIhB,iCAAiC,CAACrC,KAAK,CAAC,EAAE;UAC5C;QACF;QAEA,IAAIH,KAAK,GAAGkE,aAAa,EAAE;UACzB,MAAMI,MAAM,GAAGhC,qBAAqB,CAACC,OAAO,CAC1CpC,KAAK,EACLgC,SAAS,CAACI,OACZ,CAAC;UAED,IAAI+B,MAAM,KAAK9E,SAAS,EAAE;YACxB,IAAI;cACFkD,UAAU,CAAC6B,QAAQ,CAACD,MAAM,CAAC;YAC7B,CAAC,CAAC,OAAOE,CAAC,EAAE;cACV;cACA;cACAjD,OAAO,CAACkD,IAAI,CACV,qDAAqDjB,IAAI,MACvD,OAAOgB,CAAC,KAAK,QAAQ,IAAIA,CAAC,IAAI,IAAI,IAAI,SAAS,IAAIA,CAAC,GAChDA,CAAC,CAACE,OAAO,GACTF,CAAC,EAET,CAAC;YACH;UACF,CAAC,MAAM;YACL9B,UAAU,CAAC2B,SAAS,CAAClE,KAAK,CAAC;UAC7B;QACF,CAAC,MAAM;UACLuC,UAAU,CAAC2B,SAAS,CAAClE,KAAK,CAAC;QAC7B;MACF,CAAC,MAAM;QACL;QACAuC,UAAU,CAAC2B,SAAS,CAAClE,KAAK,CAAC;MAC7B;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CACDY,OAAO,EACPpB,OAAO,EACPsB,kBAAkB,EAClBH,GAAG,EACH0B,iCAAiC,CAClC,CAAC;EAEFtD,KAAK,CAACiC,SAAS,CAAC,MAAM;IACpB,IAAI,CAACJ,OAAO,EAAE;MACZ;IACF;IAEA,MAAM4D,eAAe,GAAGA,CACtBC,KAA0C,EAC1CzE,KAAsB,KACX;MACX,IAAIqD,IAAI;;MAER;MACA;MACA,IAAIoB,KAAK,EAAEpB,IAAI,EAAE;QACf,MAAMqB,YAAY,GAAGzC,mBAAmB,CAACG,OAAO,CAC9CqC,KAAK,CAACpB,IAAI,EACVrB,SAAS,CAACI,OACZ,CAAC;QAED,IAAIsC,YAAY,EAAE;UAChB,MAAMC,YAAY,GAAGrG,gBAAgB,CAACoG,YAAY,CAAC;UAEnD,IACEC,YAAY,IACZA,YAAY,CAAC7B,IAAI,KAAK2B,KAAK,CAAC3B,IAAI,IAChChE,OAAO,CAAC6F,YAAY,CAACC,MAAM,EAAEH,KAAK,CAACG,MAAM,CAAC,EAC1C;YACAvB,IAAI,GAAGoB,KAAK,CAACpB,IAAI;UACnB;QACF;MACF;MAEA,IAAIA,IAAI,IAAI,IAAI,EAAE;QAChBA,IAAI,GAAGnB,mBAAmB,CAACE,OAAO,CAACpC,KAAK,EAAEgC,SAAS,CAACI,OAAO,CAAC;MAC9D;MAEA,MAAMyC,aAAa,GAAGjB,gBAAgB,CAACxB,OAAO,GAC1C9D,gBAAgB,CAACsF,gBAAgB,CAACxB,OAAO,CAAC,GAC1C/C,SAAS;;MAEb;MACA,IACEwF,aAAa,IACbJ,KAAK,IACL,KAAK,IAAII,aAAa,IACtB,KAAK,IAAIJ,KAAK,IACdI,aAAa,CAACvF,GAAG,KAAKmF,KAAK,CAACnF,GAAG,EAC/B;QACA+D,IAAI,GAAGA,IAAI,GAAGF,QAAQ,CAAC2B,IAAI;MAC7B;MAEA,OAAOzB,IAAI;IACb,CAAC;IAED,IAAI1C,GAAG,CAACyB,OAAO,EAAE;MACf;MACA;MACA,MAAMpC,KAAK,GAAGW,GAAG,CAACyB,OAAO,CAACK,YAAY,CAAC,CAAC;MAExC,IAAIzC,KAAK,EAAE;QACT,MAAMyE,KAAK,GAAGnG,gBAAgB,CAAC0B,KAAK,CAAC;QACrC,MAAMqD,IAAI,GAAGmB,eAAe,CAACC,KAAK,EAAEzE,KAAK,CAAC;QAE1C,IAAI4D,gBAAgB,CAACxB,OAAO,KAAK/C,SAAS,EAAE;UAC1CuE,gBAAgB,CAACxB,OAAO,GAAGpC,KAAK;QAClC;QAEAR,OAAO,CAACuF,OAAO,CAAC;UAAE1B,IAAI;UAAErD;QAAM,CAAC,CAAC;MAClC;IACF;IAEA,MAAMgF,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,MAAMzC,UAAU,GAAG5B,GAAG,CAACyB,OAAO;MAE9B,IAAI,CAACG,UAAU,IAAI,CAAC3B,OAAO,EAAE;QAC3B;MACF;MAEA,MAAMqE,aAAa,GAAGrB,gBAAgB,CAACxB,OAAO;MAC9C,MAAMpC,KAAK,GAAGuC,UAAU,CAACE,YAAY,CAAC,CAAC;;MAEvC;MACA,IAAI,CAACzC,KAAK,EAAE;QACV;MACF;MAEA,MAAMkF,WAAW,GAAGrB,sBAAsB,CAACzB,OAAO;MAClD,MAAMqC,KAAK,GAAGnG,gBAAgB,CAAC0B,KAAK,CAAC;MACrC,MAAMqD,IAAI,GAAGmB,eAAe,CAACC,KAAK,EAAEzE,KAAK,CAAC;MAE1C4D,gBAAgB,CAACxB,OAAO,GAAGpC,KAAK;MAChC6D,sBAAsB,CAACzB,OAAO,GAAG/C,SAAS;;MAE1C;MACA;MACA;MACA;MACA,MAAM,CAAC8F,oBAAoB,EAAEC,YAAY,CAAC,GAAGlG,iBAAiB,CAC5D+F,aAAa,EACbjF,KACF,CAAC;MAED,IACEmF,oBAAoB,IACpBC,YAAY;MACZ;MACA;MACA/B,IAAI,KAAK6B,WAAW,EACpB;QACA,MAAMG,YAAY,GAChB,CAACD,YAAY,CAAC5F,OAAO,GACjB4F,YAAY,CAAC5F,OAAO,CAACC,MAAM,GAC3B2F,YAAY,CAAC1F,MAAM,CAACD,MAAM,KAC7B0F,oBAAoB,CAAC3F,OAAO,GACzB2F,oBAAoB,CAAC3F,OAAO,CAACC,MAAM,GACnC0F,oBAAoB,CAACzF,MAAM,CAACD,MAAM,CAAC;QAEzC,IAAI4F,YAAY,GAAG,CAAC,EAAE;UACpB;UACA;UACA7F,OAAO,CAACkC,IAAI,CAAC;YAAE2B,IAAI;YAAErD;UAAM,CAAC,CAAC;QAC/B,CAAC,MAAM,IAAIqF,YAAY,GAAG,CAAC,EAAE;UAC3B;;UAEA,MAAMC,SAAS,GAAG9F,OAAO,CAAC+F,SAAS,CAAC;YAAElC;UAAK,CAAC,CAAC;UAC7C,MAAMmC,YAAY,GAAGhG,OAAO,CAACK,KAAK;UAElC,IAAI;YACF,IACEyF,SAAS,KAAK,CAAC,CAAC,IAChBA,SAAS,GAAGE,YAAY;YACxB;YACAhG,OAAO,CAACyE,GAAG,CAACqB,SAAS,CAAC,EACtB;cACA;cACA,MAAM9F,OAAO,CAACiG,EAAE,CAACH,SAAS,GAAGE,YAAY,CAAC;YAC5C,CAAC,MAAM;cACL;cACA;cACA;cACA,MAAMhG,OAAO,CAACiG,EAAE,CAACJ,YAAY,CAAC;YAChC;;YAEA;YACA7F,OAAO,CAACuF,OAAO,CAAC;cAAE1B,IAAI;cAAErD;YAAM,CAAC,CAAC;UAClC,CAAC,CAAC,OAAOqE,CAAC,EAAE;YACV;UAAA;QAEJ,CAAC,MAAM;UACL;UACA7E,OAAO,CAACuF,OAAO,CAAC;YAAE1B,IAAI;YAAErD;UAAM,CAAC,CAAC;QAClC;MACF,CAAC,MAAM;QACL;QACA;QACAR,OAAO,CAACuF,OAAO,CAAC;UAAE1B,IAAI;UAAErD;QAAM,CAAC,CAAC;MAClC;IACF,CAAC;;IAED;IACA;IACA;IACA,OAAOW,GAAG,CAACyB,OAAO,EAAEsD,WAAW,CAAC,OAAO,EAAExF,MAAM,CAAC8E,aAAa,CAAC,CAAC;EACjE,CAAC,EAAE,CAACpE,OAAO,EAAEpB,OAAO,EAAEmB,GAAG,CAAC,CAAC;EAE3B,OAAO;IACLsC;EACF,CAAC;AACH", "ignoreList": []}