{"version": 3, "names": ["nanoid", "createMemoryHistory", "index", "items", "pending", "interrupt", "for<PERSON>ach", "it", "cb", "history", "id", "window", "state", "findIndex", "item", "get", "backIndex", "path", "i", "push", "slice", "length", "pushState", "replace", "pathWithHash", "hash", "includes", "location", "replaceState", "go", "n", "nextIndex", "lastItemIndex", "Promise", "resolve", "reject", "done", "interrupted", "clearTimeout", "timer", "Error", "title", "document", "ref", "setTimeout", "foundIndex", "splice", "onPopState", "last", "pop", "removeEventListener", "addEventListener", "listen", "listener"], "sourceRoot": "../../src", "sources": ["createMemoryHistory.tsx"], "mappings": ";;AACA,SAASA,MAAM,QAAQ,mBAAmB;AAW1C,OAAO,SAASC,mBAAmBA,CAAA,EAAG;EACpC,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,KAAsB,GAAG,EAAE;;EAE/B;EACA;EACA,MAAMC,OAAgE,GAAG,EAAE;EAE3E,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtB;IACA;IACA;IACAD,OAAO,CAACE,OAAO,CAAEC,EAAE,IAAK;MACtB,MAAMC,EAAE,GAAGD,EAAE,CAACC,EAAE;MAChBD,EAAE,CAACC,EAAE,GAAG,MAAMA,EAAE,CAAC,IAAI,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,OAAO,GAAG;IACd,IAAIP,KAAKA,CAAA,EAAW;MAClB;MACA;MACA,MAAMQ,EAAE,GAAGC,MAAM,CAACF,OAAO,CAACG,KAAK,EAAEF,EAAE;MAEnC,IAAIA,EAAE,EAAE;QACN,MAAMR,KAAK,GAAGC,KAAK,CAACU,SAAS,CAAEC,IAAI,IAAKA,IAAI,CAACJ,EAAE,KAAKA,EAAE,CAAC;QAEvD,OAAOR,KAAK,GAAG,CAAC,CAAC,GAAGA,KAAK,GAAG,CAAC;MAC/B;MAEA,OAAO,CAAC;IACV,CAAC;IAEDa,GAAGA,CAACb,KAAa,EAAE;MACjB,OAAOC,KAAK,CAACD,KAAK,CAAC;IACrB,CAAC;IAEDc,SAASA,CAAC;MAAEC;IAAuB,CAAC,EAAE;MACpC;MACA,KAAK,IAAIC,CAAC,GAAGhB,KAAK,GAAG,CAAC,EAAEgB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACnC,MAAMJ,IAAI,GAAGX,KAAK,CAACe,CAAC,CAAC;QAErB,IAAIJ,IAAI,CAACG,IAAI,KAAKA,IAAI,EAAE;UACtB,OAAOC,CAAC;QACV;MACF;MAEA,OAAO,CAAC,CAAC;IACX,CAAC;IAEDC,IAAIA,CAAC;MAAEF,IAAI;MAAEL;IAAgD,CAAC,EAAE;MAC9DP,SAAS,CAAC,CAAC;MAEX,MAAMK,EAAE,GAAGV,MAAM,CAAC,CAAC;;MAEnB;MACA;MACAG,KAAK,GAAGA,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAElB,KAAK,GAAG,CAAC,CAAC;MAEjCC,KAAK,CAACgB,IAAI,CAAC;QAAEF,IAAI;QAAEL,KAAK;QAAEF;MAAG,CAAC,CAAC;MAC/BR,KAAK,GAAGC,KAAK,CAACkB,MAAM,GAAG,CAAC;;MAExB;MACA;MACA;MACA;MACAV,MAAM,CAACF,OAAO,CAACa,SAAS,CAAC;QAAEZ;MAAG,CAAC,EAAE,EAAE,EAAEO,IAAI,CAAC;IAC5C,CAAC;IAEDM,OAAOA,CAAC;MAAEN,IAAI;MAAEL;IAAgD,CAAC,EAAE;MACjEP,SAAS,CAAC,CAAC;MAEX,MAAMK,EAAE,GAAGC,MAAM,CAACF,OAAO,CAACG,KAAK,EAAEF,EAAE,IAAIV,MAAM,CAAC,CAAC;;MAE/C;MACA;MACA,IAAIwB,YAAY,GAAGP,IAAI;MACvB,MAAMQ,IAAI,GAAGD,YAAY,CAACE,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,GAAGC,QAAQ,CAACF,IAAI;MAE5D,IAAI,CAACtB,KAAK,CAACkB,MAAM,IAAIlB,KAAK,CAACU,SAAS,CAAEC,IAAI,IAAKA,IAAI,CAACJ,EAAE,KAAKA,EAAE,CAAC,GAAG,CAAC,EAAE;QAClE;QACA;QACA;QACA;QACA;QACA;;QAEAc,YAAY,GAAGA,YAAY,GAAGC,IAAI;QAClCtB,KAAK,GAAG,CAAC;UAAEc,IAAI,EAAEO,YAAY;UAAEZ,KAAK;UAAEF;QAAG,CAAC,CAAC;QAC3CR,KAAK,GAAG,CAAC;MACX,CAAC,MAAM;QACL,IAAIC,KAAK,CAACD,KAAK,CAAC,CAACe,IAAI,KAAKA,IAAI,EAAE;UAC9BO,YAAY,GAAGA,YAAY,GAAGC,IAAI;QACpC;QACAtB,KAAK,CAACD,KAAK,CAAC,GAAG;UAAEe,IAAI;UAAEL,KAAK;UAAEF;QAAG,CAAC;MACpC;MAEAC,MAAM,CAACF,OAAO,CAACmB,YAAY,CAAC;QAAElB;MAAG,CAAC,EAAE,EAAE,EAAEc,YAAY,CAAC;IACvD,CAAC;IAED;IACA;IACA;IACA;IACA;IACAK,EAAEA,CAACC,CAAS,EAAE;MACZzB,SAAS,CAAC,CAAC;;MAEX;MACA;MACA,MAAM0B,SAAS,GAAG7B,KAAK,GAAG4B,CAAC;MAC3B,MAAME,aAAa,GAAG7B,KAAK,CAACkB,MAAM,GAAG,CAAC;MACtC,IAAIS,CAAC,GAAG,CAAC,IAAI,CAAC3B,KAAK,CAAC4B,SAAS,CAAC,EAAE;QAC9B;QACAD,CAAC,GAAG,CAAC5B,KAAK;QACVA,KAAK,GAAG,CAAC;MACX,CAAC,MAAM,IAAI4B,CAAC,GAAG,CAAC,IAAIC,SAAS,GAAGC,aAAa,EAAE;QAC7C;QACAF,CAAC,GAAGE,aAAa,GAAG9B,KAAK;QACzBA,KAAK,GAAG8B,aAAa;MACvB,CAAC,MAAM;QACL9B,KAAK,GAAG6B,SAAS;MACnB;MAEA,IAAID,CAAC,KAAK,CAAC,EAAE;QACX;MACF;;MAEA;MACA;MACA;MACA;MACA;MACA,OAAO,IAAIG,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,KAAK;QAC5C,MAAMC,IAAI,GAAIC,WAAqB,IAAK;UACtCC,YAAY,CAACC,KAAK,CAAC;UAEnB,IAAIF,WAAW,EAAE;YACfF,MAAM,CAAC,IAAIK,KAAK,CAAC,wCAAwC,CAAC,CAAC;YAC3D;UACF;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,MAAM;YAAEC;UAAM,CAAC,GAAG9B,MAAM,CAAC+B,QAAQ;UAEjC/B,MAAM,CAAC+B,QAAQ,CAACD,KAAK,GAAG,EAAE;UAC1B9B,MAAM,CAAC+B,QAAQ,CAACD,KAAK,GAAGA,KAAK;UAE7BP,OAAO,CAAC,CAAC;QACX,CAAC;QAED9B,OAAO,CAACe,IAAI,CAAC;UAAEwB,GAAG,EAAEP,IAAI;UAAE5B,EAAE,EAAE4B;QAAK,CAAC,CAAC;;QAErC;QACA;QACA;QACA;QACA;QACA,MAAMG,KAAK,GAAGK,UAAU,CAAC,MAAM;UAC7B,MAAMC,UAAU,GAAGzC,OAAO,CAACS,SAAS,CAAEN,EAAE,IAAKA,EAAE,CAACoC,GAAG,KAAKP,IAAI,CAAC;UAE7D,IAAIS,UAAU,GAAG,CAAC,CAAC,EAAE;YACnBzC,OAAO,CAACyC,UAAU,CAAC,CAACrC,EAAE,CAAC,CAAC;YACxBJ,OAAO,CAAC0C,MAAM,CAACD,UAAU,EAAE,CAAC,CAAC;UAC/B;UAEA3C,KAAK,GAAG,IAAI,CAACA,KAAK;QACpB,CAAC,EAAE,GAAG,CAAC;QAEP,MAAM6C,UAAU,GAAGA,CAAA,KAAM;UACvB;UACA;UACA7C,KAAK,GAAG,IAAI,CAACA,KAAK;UAElB,MAAM8C,IAAI,GAAG5C,OAAO,CAAC6C,GAAG,CAAC,CAAC;UAE1BtC,MAAM,CAACuC,mBAAmB,CAAC,UAAU,EAAEH,UAAU,CAAC;UAClDC,IAAI,EAAExC,EAAE,CAAC,CAAC;QACZ,CAAC;QAEDG,MAAM,CAACwC,gBAAgB,CAAC,UAAU,EAAEJ,UAAU,CAAC;QAC/CpC,MAAM,CAACF,OAAO,CAACoB,EAAE,CAACC,CAAC,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC;IAED;IACA;IACA;IACAsB,MAAMA,CAACC,QAAoB,EAAE;MAC3B,MAAMN,UAAU,GAAGA,CAAA,KAAM;QACvB;QACA;QACA7C,KAAK,GAAG,IAAI,CAACA,KAAK;QAElB,IAAIE,OAAO,CAACiB,MAAM,EAAE;UAClB;UACA;QACF;QAEAgC,QAAQ,CAAC,CAAC;MACZ,CAAC;MAED1C,MAAM,CAACwC,gBAAgB,CAAC,UAAU,EAAEJ,UAAU,CAAC;MAE/C,OAAO,MAAMpC,MAAM,CAACuC,mBAAmB,CAAC,UAAU,EAAEH,UAAU,CAAC;IACjE;EACF,CAAC;EAED,OAAOtC,OAAO;AAChB", "ignoreList": []}