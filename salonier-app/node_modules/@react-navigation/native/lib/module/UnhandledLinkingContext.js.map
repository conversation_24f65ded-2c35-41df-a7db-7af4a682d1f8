{"version": 3, "names": ["React", "MISSING_CONTEXT_ERROR", "UnhandledLinkingContext", "createContext", "lastUnhandledLink", "Error", "setLastUnhandledLink", "displayName"], "sourceRoot": "../../src", "sources": ["UnhandledLinkingContext.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,MAAMC,qBAAqB,GACzB,mDAAmD;AAErD,OAAO,MAAMC,uBAAuB,gBAAGF,KAAK,CAACG,aAAa,CAGvD;EACD,IAAIC,iBAAiBA,CAAA,EAAQ;IAC3B,MAAM,IAAIC,KAAK,CAACJ,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAIK,oBAAoBA,CAAA,EAAQ;IAC9B,MAAM,IAAID,KAAK,CAACJ,qBAAqB,CAAC;EACxC;AACF,CAAC,CAAC;AAEFC,uBAAuB,CAACK,WAAW,GAAG,yBAAyB", "ignoreList": []}