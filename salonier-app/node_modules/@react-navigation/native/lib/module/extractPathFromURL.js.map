{"version": 3, "names": ["escapeStringRegexp", "extractPathFromURL", "prefixes", "url", "prefix", "protocol", "match", "host", "replace", "RegExp", "prefixRegex", "split", "map", "it", "join", "originAndPath", "searchParams", "normalizedURL", "concat", "length", "test", "undefined"], "sourceRoot": "../../src", "sources": ["extractPathFromURL.tsx"], "mappings": ";;AAAA,OAAOA,kBAAkB,MAAM,sBAAsB;AAErD,OAAO,SAASC,kBAAkBA,CAACC,QAAkB,EAAEC,GAAW,EAAE;EAClE,KAAK,MAAMC,MAAM,IAAIF,QAAQ,EAAE;IAC7B,MAAMG,QAAQ,GAAGD,MAAM,CAACE,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;IACnD,MAAMC,IAAI,GAAGH,MAAM,CAChBI,OAAO,CAAC,IAAIC,MAAM,CAAC,IAAIT,kBAAkB,CAACK,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAC3DG,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAAA,CACrBA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;;IAEvB,MAAME,WAAW,GAAG,IAAID,MAAM,CAC5B,IAAIT,kBAAkB,CAACK,QAAQ,CAAC,OAAOE,IAAI,CACxCI,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAEC,EAAE,IAAMA,EAAE,KAAK,GAAG,GAAG,OAAO,GAAGb,kBAAkB,CAACa,EAAE,CAAE,CAAC,CAC5DC,IAAI,CAAC,KAAK,CAAC,EAChB,CAAC;IAED,MAAM,CAACC,aAAa,EAAE,GAAGC,YAAY,CAAC,GAAGb,GAAG,CAACQ,KAAK,CAAC,GAAG,CAAC;IACvD,MAAMM,aAAa,GAAGF,aAAa,CAChCP,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBU,MAAM,CAACF,YAAY,CAACG,MAAM,GAAG,IAAIH,YAAY,CAACF,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;IAElE,IAAIJ,WAAW,CAACU,IAAI,CAACH,aAAa,CAAC,EAAE;MACnC,OAAOA,aAAa,CAACT,OAAO,CAACE,WAAW,EAAE,EAAE,CAAC;IAC/C;EACF;EAEA,OAAOW,SAAS;AAClB", "ignoreList": []}