{"version": 3, "names": ["BaseNavigationContainer", "getActionFromState", "getPathFromState", "getStateFromPath", "ThemeProvider", "validatePathConfig", "React", "I18nManager", "useLatestCallback", "LinkingContext", "LocaleDirContext", "DefaultTheme", "UnhandledLinkingContext", "useBackButton", "useDocumentTitle", "useLinking", "useThenable", "jsx", "_jsx", "globalThis", "REACT_NAVIGATION_DEVTOOLS", "WeakMap", "NavigationContainerInner", "direction", "getConstants", "isRTL", "theme", "linking", "fallback", "documentTitle", "onReady", "onStateChange", "rest", "ref", "isLinkingEnabled", "enabled", "config", "ref<PERSON><PERSON><PERSON>", "useRef", "lastUnhandledLink", "setLastUnhandledLink", "useState", "getInitialState", "prefixes", "linkingContext", "useMemo", "options", "unhandledLinkingContext", "onReadyForLinkingHandling", "path", "current", "getCurrentRoute", "previousLastUnhandledLink", "undefined", "onStateChangeForLinkingHandling", "state", "useEffect", "set", "isResolved", "initialState", "useImperativeHandle", "isLinkingReady", "Provider", "value", "children", "NavigationContainer", "forwardRef"], "sourceRoot": "../../src", "sources": ["NavigationContainer.tsx"], "mappings": ";;AAAA,SACEA,uBAAuB,EACvBC,kBAAkB,EAClBC,gBAAgB,EAChBC,gBAAgB,EAKhBC,aAAa,EACbC,kBAAkB,QACb,wBAAwB;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,cAAc;AAC1C,OAAOC,iBAAiB,MAAM,qBAAqB;AAEnD,SAASC,cAAc,QAAQ,qBAAkB;AACjD,SAASC,gBAAgB,QAAQ,uBAAoB;AACrD,SAASC,YAAY,QAAQ,2BAAwB;AAMrD,SAASC,uBAAuB,QAAQ,8BAA2B;AACnE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,WAAW,QAAQ,kBAAe;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAS5CC,UAAU,CAACC,yBAAyB,GAAG,IAAIC,OAAO,CAAC,CAAC;AASpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwBA,CAC/B;EACEC,SAAS,GAAGhB,WAAW,CAACiB,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;EAC5DC,KAAK,GAAGf,YAAY;EACpBgB,OAAO;EACPC,QAAQ,GAAG,IAAI;EACfC,aAAa;EACbC,OAAO;EACPC,aAAa;EACb,GAAGC;AACiB,CAAC,EACvBC,GAA6D,EAC7D;EACA,MAAMC,gBAAgB,GAAGP,OAAO,GAAGA,OAAO,CAACQ,OAAO,KAAK,KAAK,GAAG,KAAK;EAEpE,IAAIR,OAAO,EAAES,MAAM,EAAE;IACnB/B,kBAAkB,CAACsB,OAAO,CAACS,MAAM,CAAC;EACpC;EAEA,MAAMC,YAAY,GAChB/B,KAAK,CAACgC,MAAM,CAAwC,IAAI,CAAC;EAE3DzB,aAAa,CAACwB,YAAY,CAAC;EAC3BvB,gBAAgB,CAACuB,YAAY,EAAER,aAAa,CAAC;EAE7C,MAAM,CAACU,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlC,KAAK,CAACmC,QAAQ,CAE9D,CAAC;EAEH,MAAM;IAAEC;EAAgB,CAAC,GAAG3B,UAAU,CACpCsB,YAAY,EACZ;IACEF,OAAO,EAAED,gBAAgB;IACzBS,QAAQ,EAAE,EAAE;IACZ,GAAGhB;EACL,CAAC,EACDa,oBACF,CAAC;EAED,MAAMI,cAAc,GAAGtC,KAAK,CAACuC,OAAO,CAAC,OAAO;IAAEC,OAAO,EAAEnB;EAAQ,CAAC,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAE7E,MAAMoB,uBAAuB,GAAGzC,KAAK,CAACuC,OAAO,CAC3C,OAAO;IAAEN,iBAAiB;IAAEC;EAAqB,CAAC,CAAC,EACnD,CAACD,iBAAiB,EAAEC,oBAAoB,CAC1C,CAAC;EAED,MAAMQ,yBAAyB,GAAGxC,iBAAiB,CAAC,MAAM;IACxD;IACA,MAAMyC,IAAI,GAAGZ,YAAY,CAACa,OAAO,EAAEC,eAAe,CAAC,CAAC,EAAEF,IAAI;IAC1DT,oBAAoB,CAAEY,yBAAyB,IAAK;MAClD,IAAIA,yBAAyB,KAAKH,IAAI,EAAE;QACtC,OAAOI,SAAS;MAClB;MACA,OAAOD,yBAAyB;IAClC,CAAC,CAAC;IACFtB,OAAO,GAAG,CAAC;EACb,CAAC,CAAC;EAEF,MAAMwB,+BAA+B,GAAG9C,iBAAiB,CACtD+C,KAA4C,IAAK;IAChD;IACA,MAAMN,IAAI,GAAGZ,YAAY,CAACa,OAAO,EAAEC,eAAe,CAAC,CAAC,EAAEF,IAAI;IAC1DT,oBAAoB,CAAEY,yBAAyB,IAAK;MAClD,IAAIA,yBAAyB,KAAKH,IAAI,EAAE;QACtC,OAAOI,SAAS;MAClB;MACA,OAAOD,yBAAyB;IAClC,CAAC,CAAC;IACFrB,aAAa,GAAGwB,KAAK,CAAC;EACxB,CACF,CAAC;EACD;EACA;EACAjD,KAAK,CAACkD,SAAS,CAAC,MAAM;IACpB,IAAInB,YAAY,CAACa,OAAO,EAAE;MACxB9B,yBAAyB,CAACqC,GAAG,CAACpB,YAAY,CAACa,OAAO,EAAE;QAClD,IAAIvB,OAAOA,CAAA,EAAG;UACZ,OAAO;YACL,GAAGA,OAAO;YACVQ,OAAO,EAAED,gBAAgB;YACzBS,QAAQ,EAAEhB,OAAO,EAAEgB,QAAQ,IAAI,EAAE;YACjCxC,gBAAgB,EAAEwB,OAAO,EAAExB,gBAAgB,IAAIA,gBAAgB;YAC/DD,gBAAgB,EAAEyB,OAAO,EAAEzB,gBAAgB,IAAIA,gBAAgB;YAC/DD,kBAAkB,EAChB0B,OAAO,EAAE1B,kBAAkB,IAAIA;UACnC,CAAC;QACH;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,MAAM,CAACyD,UAAU,EAAEC,YAAY,CAAC,GAAG3C,WAAW,CAAC0B,eAAe,CAAC;;EAE/D;EACA;EACApC,KAAK,CAACsD,mBAAmB,CAAC3B,GAAG,EAAE,MAAMI,YAAY,CAACa,OAAO,CAAC;EAE1D,MAAMW,cAAc,GAClB7B,IAAI,CAAC2B,YAAY,IAAI,IAAI,IAAI,CAACzB,gBAAgB,IAAIwB,UAAU;EAE9D,IAAI,CAACG,cAAc,EAAE;IACnB,oBACE3C,IAAA,CAACR,gBAAgB,CAACoD,QAAQ;MAACC,KAAK,EAAExC,SAAU;MAAAyC,QAAA,eAC1C9C,IAAA,CAACd,aAAa;QAAC2D,KAAK,EAAErC,KAAM;QAAAsC,QAAA,EAAEpC;MAAQ,CAAgB;IAAC,CAC9B,CAAC;EAEhC;EAEA,oBACEV,IAAA,CAACR,gBAAgB,CAACoD,QAAQ;IAACC,KAAK,EAAExC,SAAU;IAAAyC,QAAA,eAC1C9C,IAAA,CAACN,uBAAuB,CAACkD,QAAQ;MAACC,KAAK,EAAEhB,uBAAwB;MAAAiB,QAAA,eAC/D9C,IAAA,CAACT,cAAc,CAACqD,QAAQ;QAACC,KAAK,EAAEnB,cAAe;QAAAoB,QAAA,eAC7C9C,IAAA,CAAClB,uBAAuB;UAAA,GAClBgC,IAAI;UACRN,KAAK,EAAEA,KAAM;UACbI,OAAO,EAAEkB,yBAA0B;UACnCjB,aAAa,EAAEuB,+BAAgC;UAC/CK,YAAY,EACV3B,IAAI,CAAC2B,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAG3B,IAAI,CAAC2B,YACjD;UACD1B,GAAG,EAAEI;QAAa,CACnB;MAAC,CACqB;IAAC,CACM;EAAC,CACV,CAAC;AAEhC;AAEA,OAAO,MAAM4B,mBAAmB,gBAAG3D,KAAK,CAAC4D,UAAU,CACjD5C,wBACF,CAIuB", "ignoreList": []}