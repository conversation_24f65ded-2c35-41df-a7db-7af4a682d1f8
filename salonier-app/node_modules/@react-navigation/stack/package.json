{"name": "@react-navigation/stack", "description": "Stack navigator component for iOS and Android with animated transitions and gestures", "version": "7.3.4", "keywords": ["react-native-component", "react-component", "react-native", "react-navigation", "ios", "android", "stack"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/stack"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org/docs/stack-navigator/", "main": "./lib/module/index.js", "types": "./lib/typescript/src/index.d.ts", "exports": {".": {"source": "./src/index.tsx", "types": "./lib/typescript/src/index.d.ts", "default": "./lib/module/index.js"}, "./package.json": "./package.json"}, "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"@react-navigation/elements": "^2.4.4", "color": "^4.2.3"}, "devDependencies": {"@jest/globals": "^30.0.0", "@react-navigation/native": "^7.1.11", "@testing-library/react-native": "^13.2.0", "@types/color": "^4.2.0", "@types/react": "~19.0.10", "del-cli": "^6.0.0", "react": "19.0.0", "react-native": "0.79.3", "react-native-builder-bob": "^0.40.12", "react-native-gesture-handler": "~2.26.0", "react-native-safe-area-context": "5.4.1", "react-native-screens": "~4.11.1", "react-test-renderer": "19.0.0", "typescript": "^5.8.3"}, "peerDependencies": {"@react-navigation/native": "^7.1.11", "react": ">= 18.2.0", "react-native": "*", "react-native-gesture-handler": ">= 2.0.0", "react-native-safe-area-context": ">= 4.0.0", "react-native-screens": ">= 4.0.0"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "27560ad2fc55987654a2f9419a016225387e78fe"}