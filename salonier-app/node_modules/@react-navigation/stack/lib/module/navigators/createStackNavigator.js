"use strict";

import { createNavigatorF<PERSON>y, StackActions, StackRouter, useLocale, useNavigationBuilder } from '@react-navigation/native';
import * as React from 'react';
import { StackView } from "../views/Stack/StackView.js";
import { jsx as _jsx } from "react/jsx-runtime";
function StackNavigator({
  id,
  initialRouteName,
  children,
  layout,
  screenListeners,
  screenOptions,
  screenLayout,
  UNSTABLE_router,
  ...rest
}) {
  const {
    direction
  } = useLocale();
  const {
    state,
    describe,
    descriptors,
    navigation,
    NavigationContent
  } = useNavigationBuilder(StackRouter, {
    id,
    initialRouteName,
    children,
    layout,
    screenListeners,
    screenOptions,
    screenLayout,
    UNSTABLE_router
  });
  React.useEffect(() =>
  // @ts-expect-error: there may not be a tab navigator in parent
  navigation.addListener?.('tabPress', e => {
    const isFocused = navigation.isFocused();

    // Run the operation in the next frame so we're sure all listeners have been run
    // This is necessary to know if preventDefault() has been called
    requestAnimationFrame(() => {
      if (state.index > 0 && isFocused && !e.defaultPrevented) {
        // When user taps on already focused tab and we're inside the tab,
        // reset the stack to replicate native behaviour
        navigation.dispatch({
          ...StackActions.popToTop(),
          target: state.key
        });
      }
    });
  }), [navigation, state.index, state.key]);
  return /*#__PURE__*/_jsx(NavigationContent, {
    children: /*#__PURE__*/_jsx(StackView, {
      ...rest,
      direction: direction,
      state: state,
      describe: describe,
      descriptors: descriptors,
      navigation: navigation
    })
  });
}
export function createStackNavigator(config) {
  return createNavigatorFactory(StackNavigator)(config);
}
//# sourceMappingURL=createStackNavigator.js.map