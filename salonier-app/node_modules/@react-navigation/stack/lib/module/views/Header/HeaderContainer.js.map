{"version": 3, "names": ["getHeaderTitle", "HeaderBackContext", "NavigationContext", "NavigationRouteContext", "useLinkBuilder", "React", "Animated", "StyleSheet", "View", "forNoAnimation", "forSlideLeft", "forSlideRight", "forSlideUp", "Header", "jsx", "_jsx", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mode", "scenes", "layout", "getPreviousScene", "getFocusedRoute", "onContentHeightChange", "style", "focusedRoute", "parentHeaderBack", "useContext", "buildHref", "pointerEvents", "children", "slice", "map", "scene", "i", "self", "length", "header", "headerMode", "headerShown", "headerTransparent", "headerStyleInterpolator", "descriptor", "options", "isFocused", "key", "route", "previousScene", "headerBack", "title", "name", "href", "params", "previousDescriptor", "nextDescriptor", "previousHeaderShown", "previousHeaderMode", "nextHeaderlessScene", "find", "currentHeaderShown", "currentHeaderMode", "gestureDirection", "nextHeaderlessGestureDirection", "isHeaderStatic", "props", "back", "progress", "navigation", "styleInterpolator", "Provider", "value", "onLayout", "e", "height", "nativeEvent", "undefined", "styles", "create", "position", "top", "start", "end"], "sourceRoot": "../../../../src", "sources": ["views/Header/HeaderContainer.tsx"], "mappings": ";;AAAA,SAASA,cAAc,EAAEC,iBAAiB,QAAQ,4BAA4B;AAC9E,SACEC,iBAAiB,EACjBC,sBAAsB,EAGtBC,cAAc,QACT,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EAERC,UAAU,EACVC,IAAI,QAEC,cAAc;AAErB,SACEC,cAAc,EACdC,YAAY,EACZC,aAAa,EACbC,UAAU,QACL,qDAAkD;AAQzD,SAASC,MAAM,QAAQ,aAAU;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAelC,OAAO,SAASC,eAAeA,CAAC;EAC9BC,IAAI;EACJC,MAAM;EACNC,MAAM;EACNC,gBAAgB;EAChBC,eAAe;EACfC,qBAAqB;EACrBC;AACK,CAAC,EAAE;EACR,MAAMC,YAAY,GAAGH,eAAe,CAAC,CAAC;EACtC,MAAMI,gBAAgB,GAAGpB,KAAK,CAACqB,UAAU,CAACzB,iBAAiB,CAAC;EAC5D,MAAM;IAAE0B;EAAU,CAAC,GAAGvB,cAAc,CAAC,CAAC;EAEtC,oBACEW,IAAA,CAACT,QAAQ,CAACE,IAAI;IAACoB,aAAa,EAAC,UAAU;IAACL,KAAK,EAAEA,KAAM;IAAAM,QAAA,EAClDX,MAAM,CAACY,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,CAAC,EAAEC,IAAI,KAAK;MACxC,IAAKjB,IAAI,KAAK,QAAQ,IAAIgB,CAAC,KAAKC,IAAI,CAACC,MAAM,GAAG,CAAC,IAAK,CAACH,KAAK,EAAE;QAC1D,OAAO,IAAI;MACb;MAEA,MAAM;QACJI,MAAM;QACNC,UAAU;QACVC,WAAW,GAAG,IAAI;QAClBC,iBAAiB;QACjBC;MACF,CAAC,GAAGR,KAAK,CAACS,UAAU,CAACC,OAAO;MAE5B,IAAIL,UAAU,KAAKpB,IAAI,IAAI,CAACqB,WAAW,EAAE;QACvC,OAAO,IAAI;MACb;MAEA,MAAMK,SAAS,GAAGnB,YAAY,CAACoB,GAAG,KAAKZ,KAAK,CAACS,UAAU,CAACI,KAAK,CAACD,GAAG;MACjE,MAAME,aAAa,GAAG1B,gBAAgB,CAAC;QACrCyB,KAAK,EAAEb,KAAK,CAACS,UAAU,CAACI;MAC1B,CAAC,CAAC;MAEF,IAAIE,UAAU,GAAGtB,gBAAgB;MAEjC,IAAIqB,aAAa,EAAE;QACjB,MAAM;UAAEJ,OAAO;UAAEG;QAAM,CAAC,GAAGC,aAAa,CAACL,UAAU;QAEnDM,UAAU,GAAGD,aAAa,GACtB;UACEE,KAAK,EAAEhD,cAAc,CAAC0C,OAAO,EAAEG,KAAK,CAACI,IAAI,CAAC;UAC1CC,IAAI,EAAEvB,SAAS,CAACkB,KAAK,CAACI,IAAI,EAAEJ,KAAK,CAACM,MAAM;QAC1C,CAAC,GACD1B,gBAAgB;MACtB;;MAEA;MACA;MACA,MAAM2B,kBAAkB,GAAGlB,IAAI,CAACD,CAAC,GAAG,CAAC,CAAC,EAAEQ,UAAU;MAClD,MAAMY,cAAc,GAAGnB,IAAI,CAACD,CAAC,GAAG,CAAC,CAAC,EAAEQ,UAAU;MAE9C,MAAM;QACJH,WAAW,EAAEgB,mBAAmB,GAAG,IAAI;QACvCjB,UAAU,EAAEkB;MACd,CAAC,GAAGH,kBAAkB,EAAEV,OAAO,IAAI,CAAC,CAAC;;MAErC;MACA;MACA,MAAMc,mBAAmB,GAAGtB,IAAI,CAACJ,KAAK,CAACG,CAAC,GAAG,CAAC,CAAC,CAACwB,IAAI,CAAEzB,KAAK,IAAK;QAC5D,MAAM;UACJM,WAAW,EAAEoB,kBAAkB,GAAG,IAAI;UACtCrB,UAAU,EAAEsB;QACd,CAAC,GAAG3B,KAAK,EAAES,UAAU,CAACC,OAAO,IAAI,CAAC,CAAC;QAEnC,OAAOgB,kBAAkB,KAAK,KAAK,IAAIC,iBAAiB,KAAK,QAAQ;MACvE,CAAC,CAAC;MAEF,MAAM;QAAEC,gBAAgB,EAAEC;MAA+B,CAAC,GACxDL,mBAAmB,EAAEf,UAAU,CAACC,OAAO,IAAI,CAAC,CAAC;MAE/C,MAAMoB,cAAc,GACjB,CAACR,mBAAmB,KAAK,KAAK,IAAIC,kBAAkB,KAAK,QAAQ;MAChE;MACA;MACA,CAACF,cAAc,IACjBG,mBAAmB;MAErB,MAAMO,KAAuB,GAAG;QAC9B5C,MAAM;QACN6C,IAAI,EAAEjB,UAAU;QAChBkB,QAAQ,EAAEjC,KAAK,CAACiC,QAAQ;QACxBvB,OAAO,EAAEV,KAAK,CAACS,UAAU,CAACC,OAAO;QACjCG,KAAK,EAAEb,KAAK,CAACS,UAAU,CAACI,KAAK;QAC7BqB,UAAU,EAAElC,KAAK,CAACS,UAAU,CACzByB,UAAgD;QACnDC,iBAAiB,EACflD,IAAI,KAAK,OAAO,GACZ6C,cAAc,GACZD,8BAA8B,KAAK,UAAU,IAC7CA,8BAA8B,KAAK,mBAAmB,GACpDjD,UAAU,GACViD,8BAA8B,KAAK,qBAAqB,GACtDlD,aAAa,GACbD,YAAY,GAChB8B,uBAAuB,GACzB/B;MACR,CAAC;MAED,oBACEM,IAAA,CAACb,iBAAiB,CAACkE,QAAQ;QAEzBC,KAAK,EAAErC,KAAK,CAACS,UAAU,CAACyB,UAAW;QAAArC,QAAA,eAEnCd,IAAA,CAACZ,sBAAsB,CAACiE,QAAQ;UAACC,KAAK,EAAErC,KAAK,CAACS,UAAU,CAACI,KAAM;UAAAhB,QAAA,eAC7Dd,IAAA,CAACP,IAAI;YACH8D,QAAQ,EACNhD,qBAAqB,GAChBiD,CAAC,IAAK;cACL,MAAM;gBAAEC;cAAO,CAAC,GAAGD,CAAC,CAACE,WAAW,CAACtD,MAAM;cAEvCG,qBAAqB,CAAC;gBACpBuB,KAAK,EAAEb,KAAK,CAACS,UAAU,CAACI,KAAK;gBAC7B2B;cACF,CAAC,CAAC;YACJ,CAAC,GACDE,SACL;YACD9C,aAAa,EAAEe,SAAS,GAAG,UAAU,GAAG,MAAO;YAC/C,eAAa,CAACA,SAAU;YACxBpB,KAAK;YACH;YACA;YACCN,IAAI,KAAK,OAAO,IAAI,CAAC0B,SAAS,IAAKJ,iBAAiB,GACjDoC,MAAM,CAACvC,MAAM,GACb,IACL;YAAAP,QAAA,EAEAO,MAAM,KAAKsC,SAAS,GAAGtC,MAAM,CAAC2B,KAAK,CAAC,gBAAGhD,IAAA,CAACF,MAAM;cAAA,GAAKkD;YAAK,CAAG;UAAC,CACzD;QAAC,CACwB;MAAC,GA7B7B/B,KAAK,CAACS,UAAU,CAACI,KAAK,CAACD,GA8BF,CAAC;IAEjC,CAAC;EAAC,CACW,CAAC;AAEpB;AAEA,MAAM+B,MAAM,GAAGpE,UAAU,CAACqE,MAAM,CAAC;EAC/BxC,MAAM,EAAE;IACNyC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE;EACP;AACF,CAAC,CAAC", "ignoreList": []}