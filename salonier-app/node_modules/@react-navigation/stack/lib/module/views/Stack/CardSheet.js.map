{"version": 3, "names": ["React", "StyleSheet", "View", "jsx", "_jsx", "CardSheet", "forwardRef", "enabled", "layout", "style", "rest", "ref", "fill", "setFill", "useState", "pointerEvents", "setPointerEvents", "useImperativeHandle", "useEffect", "document", "body", "width", "clientWidth", "height", "clientHeight", "isFullHeight", "id", "unsubscribe", "navigator", "maxTouchPoints", "getElementById", "createElement", "updateStyle", "vh", "window", "innerHeight", "textContent", "join", "head", "contains", "append<PERSON><PERSON><PERSON>", "addEventListener", "removeEventListener", "remove", "styles", "page", "card", "create", "minHeight", "flex", "overflow"], "sourceRoot": "../../../../src", "sources": ["views/Stack/CardSheet.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,IAAI,QAAwB,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAYhE;AACA;AACA;AACA;AACA,OAAO,MAAMC,SAAS,gBAAGL,KAAK,CAACM,UAAU,CACvC,SAASD,SAASA,CAAC;EAAEE,OAAO;EAAEC,MAAM;EAAEC,KAAK;EAAE,GAAGC;AAAK,CAAC,EAAEC,GAAG,EAAE;EAC3D,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,KAAK,CAACc,QAAQ,CAAC,KAAK,CAAC;EAC7C;EACA;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GACrChB,KAAK,CAACc,QAAQ,CAA6B,MAAM,CAAC;EAEpDd,KAAK,CAACiB,mBAAmB,CAACN,GAAG,EAAE,MAAM;IACnC,OAAO;MAAEK;IAAiB,CAAC;EAC7B,CAAC,CAAC;EAEFhB,KAAK,CAACkB,SAAS,CAAC,MAAM;IACpB,IAAI,OAAOC,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,CAACC,IAAI,EAAE;MACrD;MACA;IACF;IAEA,MAAMC,KAAK,GAAGF,QAAQ,CAACC,IAAI,CAACE,WAAW;IACvC,MAAMC,MAAM,GAAGJ,QAAQ,CAACC,IAAI,CAACI,YAAY;;IAEzC;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,YAAY,GAAGF,MAAM,KAAKf,MAAM,CAACe,MAAM;IAC7C,MAAMG,EAAE,GAAG,qDAAqD;IAEhE,IAAIC,WAAqC;IAEzC,IAAIF,YAAY,IAAIG,SAAS,CAACC,cAAc,GAAG,CAAC,EAAE;MAChD,MAAMpB,KAAK,GACTU,QAAQ,CAACW,cAAc,CAACJ,EAAE,CAAC,IAAIP,QAAQ,CAACY,aAAa,CAAC,OAAO,CAAC;MAEhEtB,KAAK,CAACiB,EAAE,GAAGA,EAAE;MAEb,MAAMM,WAAW,GAAGA,CAAA,KAAM;QACxB,MAAMC,EAAE,GAAGC,MAAM,CAACC,WAAW,GAAG,IAAI;QAEpC1B,KAAK,CAAC2B,WAAW,GAAG,CAClB,iBAAiBH,EAAE,OAAO,EAC1B,8CAA8C,CAC/C,CAACI,IAAI,CAAC,IAAI,CAAC;MACd,CAAC;MAEDL,WAAW,CAAC,CAAC;MAEb,IAAI,CAACb,QAAQ,CAACmB,IAAI,CAACC,QAAQ,CAAC9B,KAAK,CAAC,EAAE;QAClCU,QAAQ,CAACmB,IAAI,CAACE,WAAW,CAAC/B,KAAK,CAAC;MAClC;MAEAyB,MAAM,CAACO,gBAAgB,CAAC,QAAQ,EAAET,WAAW,CAAC;MAE9CL,WAAW,GAAGA,CAAA,KAAM;QAClBO,MAAM,CAACQ,mBAAmB,CAAC,QAAQ,EAAEV,WAAW,CAAC;MACnD,CAAC;IACH,CAAC,MAAM;MACL;MACA;MACAb,QAAQ,CAACW,cAAc,CAACJ,EAAE,CAAC,EAAEiB,MAAM,CAAC,CAAC;IACvC;;IAEA;IACA9B,OAAO,CAACQ,KAAK,KAAKb,MAAM,CAACa,KAAK,IAAIE,MAAM,KAAKf,MAAM,CAACe,MAAM,CAAC;IAE3D,OAAOI,WAAW;EACpB,CAAC,EAAE,CAACnB,MAAM,CAACe,MAAM,EAAEf,MAAM,CAACa,KAAK,CAAC,CAAC;EAEjC,oBACEjB,IAAA,CAACF,IAAI;IAAA,GACCQ,IAAI;IACRK,aAAa,EAAEA,aAAc;IAC7BN,KAAK,EAAE,CAACF,OAAO,IAAIK,IAAI,GAAGgC,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACE,IAAI,EAAErC,KAAK;EAAE,CAC7D,CAAC;AAEN,CACF,CAAC;AAED,MAAMmC,MAAM,GAAG3C,UAAU,CAAC8C,MAAM,CAAC;EAC/BF,IAAI,EAAE;IACJG,SAAS,EAAE;EACb,CAAC;EACDF,IAAI,EAAE;IACJG,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}