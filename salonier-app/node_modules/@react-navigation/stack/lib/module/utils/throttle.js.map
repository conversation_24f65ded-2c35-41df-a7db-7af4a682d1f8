{"version": 3, "names": ["throttle", "func", "duration", "timeout", "args", "apply", "setTimeout", "undefined"], "sourceRoot": "../../../src", "sources": ["utils/throttle.tsx"], "mappings": ";;AAAA,OAAO,SAASA,QAAQA,CACtBC,IAAO,EACPC,QAAgB,EACb;EACH,IAAIC,OAAkD;EAEtD,OAAO,UAAyB,GAAGC,IAAI,EAAE;IACvC,IAAID,OAAO,IAAI,IAAI,EAAE;MACnBF,IAAI,CAACI,KAAK,CAAC,IAAI,EAAED,IAAI,CAAC;MAEtBD,OAAO,GAAGG,UAAU,CAAC,MAAM;QACzBH,OAAO,GAAGI,SAAS;MACrB,CAAC,EAAEL,QAAQ,CAAC;IACd;EACF,CAAC;AACH", "ignoreList": []}