{"version": 3, "names": ["React", "NavigationRouteContext", "useRoute", "route", "useContext", "undefined", "Error"], "sourceRoot": "../../src", "sources": ["useRoute.tsx"], "mappings": ";;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,sBAAsB,QAAQ,6BAA0B;AAGjE;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAAA,EAA0C;EAChE,MAAMC,KAAK,GAAGH,KAAK,CAACI,UAAU,CAACH,sBAAsB,CAAC;EAEtD,IAAIE,KAAK,KAAKE,SAAS,EAAE;IACvB,MAAM,IAAIC,KAAK,CACb,iFACF,CAAC;EACH;EAEA,OAAOH,KAAK;AACd", "ignoreList": []}