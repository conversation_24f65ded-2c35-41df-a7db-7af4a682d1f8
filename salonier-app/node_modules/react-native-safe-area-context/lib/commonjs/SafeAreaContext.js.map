{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_NativeSafeAreaProvider", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_extends", "assign", "bind", "arguments", "length", "apply", "isDev", "process", "env", "NODE_ENV", "SafeAreaInsetsContext", "exports", "createContext", "displayName", "SafeAreaFrameContext", "SafeAreaProvider", "children", "initialMetrics", "initialSafeAreaInsets", "style", "others", "parentInsets", "useParentSafeAreaInsets", "parentFrame", "useParentSafeAreaFrame", "insets", "setInsets", "useState", "frame", "set<PERSON>rame", "x", "y", "width", "Dimensions", "height", "onInsetsChange", "useCallback", "event", "nativeEvent", "next<PERSON><PERSON><PERSON>", "nextInsets", "curFrame", "curInsets", "bottom", "left", "right", "top", "createElement", "NativeSafeAreaProvider", "styles", "fill", "Provider", "value", "StyleSheet", "create", "flex", "useContext", "NO_INSETS_ERROR", "useSafeAreaInsets", "Error", "useSafeAreaFrame", "withSafeAreaInsets", "WrappedComponent", "forwardRef", "props", "ref", "useSafeArea", "SafeAreaConsumer", "Consumer", "SafeAreaContext"], "sourceRoot": "../../src", "sources": ["SafeAreaContext.tsx"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,uBAAA,GAAAF,OAAA;AAAkE,SAAAG,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAL,wBAAAK,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAW,SAAA,WAAAA,QAAA,GAAAR,MAAA,CAAAS,MAAA,GAAAT,MAAA,CAAAS,MAAA,CAAAC,IAAA,eAAAb,CAAA,aAAAR,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAE,CAAA,IAAAC,CAAA,OAAAY,cAAA,CAAAC,IAAA,CAAAb,CAAA,EAAAD,CAAA,MAAAM,CAAA,CAAAN,CAAA,IAAAC,CAAA,CAAAD,CAAA,aAAAM,CAAA,KAAAW,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAQlE,MAAMG,KAAK,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AAE5C,MAAMC,qBAAqB,GAAAC,OAAA,CAAAD,qBAAA,gBAAGnC,KAAK,CAACqC,aAAa,CACtD,IACF,CAAC;AACD,IAAIN,KAAK,EAAE;EACTI,qBAAqB,CAACG,WAAW,GAAG,uBAAuB;AAC7D;AAEO,MAAMC,oBAAoB,GAAAH,OAAA,CAAAG,oBAAA,gBAAGvC,KAAK,CAACqC,aAAa,CAAc,IAAI,CAAC;AAC1E,IAAIN,KAAK,EAAE;EACTQ,oBAAoB,CAACD,WAAW,GAAG,sBAAsB;AAC3D;AAWO,SAASE,gBAAgBA,CAAC;EAC/BC,QAAQ;EACRC,cAAc;EACdC,qBAAqB;EACrBC,KAAK;EACL,GAAGC;AACkB,CAAC,EAAE;EACxB,MAAMC,YAAY,GAAGC,uBAAuB,CAAC,CAAC;EAC9C,MAAMC,WAAW,GAAGC,sBAAsB,CAAC,CAAC;EAC5C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGnD,KAAK,CAACoD,QAAQ,CACxCV,cAAc,EAAEQ,MAAM,IAAIP,qBAAqB,IAAIG,YAAY,IAAI,IACrE,CAAC;EACD,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,KAAK,CAACoD,QAAQ,CACtCV,cAAc,EAAEW,KAAK,IACnBL,WAAW,IAAI;IACb;IACAO,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,KAAK,EAAEC,uBAAU,CAAC7C,GAAG,CAAC,QAAQ,CAAC,CAAC4C,KAAK;IACrCE,MAAM,EAAED,uBAAU,CAAC7C,GAAG,CAAC,QAAQ,CAAC,CAAC8C;EACnC,CACJ,CAAC;EACD,MAAMC,cAAc,GAAG5D,KAAK,CAAC6D,WAAW,CAAEC,KAAwB,IAAK;IACrE,MAAM;MACJC,WAAW,EAAE;QAAEV,KAAK,EAAEW,SAAS;QAAEd,MAAM,EAAEe;MAAW;IACtD,CAAC,GAAGH,KAAK;IAETR,QAAQ,CAAEY,QAAQ,IAAK;MACrB;MACE;MACAF,SAAS,KACRA,SAAS,CAACL,MAAM,KAAKO,QAAQ,CAACP,MAAM,IACnCK,SAAS,CAACP,KAAK,KAAKS,QAAQ,CAACT,KAAK,IAClCO,SAAS,CAACT,CAAC,KAAKW,QAAQ,CAACX,CAAC,IAC1BS,SAAS,CAACR,CAAC,KAAKU,QAAQ,CAACV,CAAC,CAAC,EAC7B;QACA,OAAOQ,SAAS;MAClB,CAAC,MAAM;QACL,OAAOE,QAAQ;MACjB;IACF,CAAC,CAAC;IAEFf,SAAS,CAAEgB,SAAS,IAAK;MACvB,IACE,CAACA,SAAS,IACVF,UAAU,CAACG,MAAM,KAAKD,SAAS,CAACC,MAAM,IACtCH,UAAU,CAACI,IAAI,KAAKF,SAAS,CAACE,IAAI,IAClCJ,UAAU,CAACK,KAAK,KAAKH,SAAS,CAACG,KAAK,IACpCL,UAAU,CAACM,GAAG,KAAKJ,SAAS,CAACI,GAAG,EAChC;QACA,OAAON,UAAU;MACnB,CAAC,MAAM;QACL,OAAOE,SAAS;MAClB;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEnE,KAAA,CAAAwE,aAAA,CAACpE,uBAAA,CAAAqE,sBAAsB,EAAAhD,QAAA;IACrBmB,KAAK,EAAE,CAAC8B,MAAM,CAACC,IAAI,EAAE/B,KAAK,CAAE;IAC5BgB,cAAc,EAAEA;EAAe,GAC3Bf,MAAM,GAETK,MAAM,IAAI,IAAI,gBACblD,KAAA,CAAAwE,aAAA,CAACjC,oBAAoB,CAACqC,QAAQ;IAACC,KAAK,EAAExB;EAAM,gBAC1CrD,KAAA,CAAAwE,aAAA,CAACrC,qBAAqB,CAACyC,QAAQ;IAACC,KAAK,EAAE3B;EAAO,GAC3CT,QAC6B,CACH,CAAC,GAC9B,IACkB,CAAC;AAE7B;AAEA,MAAMiC,MAAM,GAAGI,uBAAU,CAACC,MAAM,CAAC;EAC/BJ,IAAI,EAAE;IAAEK,IAAI,EAAE;EAAE;AAClB,CAAC,CAAC;AAEF,SAASjC,uBAAuBA,CAAA,EAAsB;EACpD,OAAO/C,KAAK,CAACiF,UAAU,CAAC9C,qBAAqB,CAAC;AAChD;AAEA,SAASc,sBAAsBA,CAAA,EAAgB;EAC7C,OAAOjD,KAAK,CAACiF,UAAU,CAAC1C,oBAAoB,CAAC;AAC/C;AAEA,MAAM2C,eAAe,GACnB,wGAAwG;AAEnG,SAASC,iBAAiBA,CAAA,EAAe;EAC9C,MAAMjC,MAAM,GAAGlD,KAAK,CAACiF,UAAU,CAAC9C,qBAAqB,CAAC;EACtD,IAAIe,MAAM,IAAI,IAAI,EAAE;IAClB,MAAM,IAAIkC,KAAK,CAACF,eAAe,CAAC;EAClC;EACA,OAAOhC,MAAM;AACf;AAEO,SAASmC,gBAAgBA,CAAA,EAAS;EACvC,MAAMhC,KAAK,GAAGrD,KAAK,CAACiF,UAAU,CAAC1C,oBAAoB,CAAC;EACpD,IAAIc,KAAK,IAAI,IAAI,EAAE;IACjB,MAAM,IAAI+B,KAAK,CAACF,eAAe,CAAC;EAClC;EACA,OAAO7B,KAAK;AACd;AAMO,SAASiC,kBAAkBA,CAChCC,gBAEC,EAGD;EACA,oBAAOvF,KAAK,CAACwF,UAAU,CAAa,CAACC,KAAK,EAAEC,GAAG,KAAK;IAClD,MAAMxC,MAAM,GAAGiC,iBAAiB,CAAC,CAAC;IAClC,oBAAOnF,KAAA,CAAAwE,aAAA,CAACe,gBAAgB,EAAA9D,QAAA,KAAKgE,KAAK;MAAEvC,MAAM,EAAEA,MAAO;MAACwC,GAAG,EAAEA;IAAI,EAAE,CAAC;EAClE,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACO,SAASC,WAAWA,CAAA,EAAe;EACxC,OAAOR,iBAAiB,CAAC,CAAC;AAC5B;;AAEA;AACA;AACA;AACO,MAAMS,gBAAgB,GAAAxD,OAAA,CAAAwD,gBAAA,GAAGzD,qBAAqB,CAAC0D,QAAQ;;AAE9D;AACA;AACA;AACO,MAAMC,eAAe,GAAA1D,OAAA,CAAA0D,eAAA,GAAG3D,qBAAqB", "ignoreList": []}