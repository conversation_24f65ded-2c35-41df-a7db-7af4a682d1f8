export declare const typescale: {
    displayLarge: {
        lineHeight: number;
        fontSize: number;
        fontFamily: string;
        letterSpacing: number;
        fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
    };
    displayMedium: {
        lineHeight: number;
        fontSize: number;
        fontFamily: string;
        letterSpacing: number;
        fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
    };
    displaySmall: {
        lineHeight: number;
        fontSize: number;
        fontFamily: string;
        letterSpacing: number;
        fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
    };
    headlineLarge: {
        lineHeight: number;
        fontSize: number;
        fontFamily: string;
        letterSpacing: number;
        fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
    };
    headlineMedium: {
        lineHeight: number;
        fontSize: number;
        fontFamily: string;
        letterSpacing: number;
        fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
    };
    headlineSmall: {
        lineHeight: number;
        fontSize: number;
        fontFamily: string;
        letterSpacing: number;
        fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
    };
    titleLarge: {
        lineHeight: number;
        fontSize: number;
        fontFamily: string;
        letterSpacing: number;
        fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
    };
    titleMedium: {
        lineHeight: number;
        fontSize: number;
        fontFamily: string;
        letterSpacing: number;
        fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
    };
    titleSmall: {
        letterSpacing: number;
        lineHeight: number;
        fontSize: number;
        fontFamily: string;
        fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
    };
    labelLarge: {
        letterSpacing: number;
        lineHeight: number;
        fontSize: number;
        fontFamily: string;
        fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
    };
    labelMedium: {
        letterSpacing: number;
        lineHeight: number;
        fontSize: number;
        fontFamily: string;
        fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
    };
    labelSmall: {
        letterSpacing: number;
        lineHeight: number;
        fontSize: number;
        fontFamily: string;
        fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
    };
    bodyLarge: {
        fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
        fontFamily: string;
        lineHeight: number;
        fontSize: number;
        letterSpacing: number;
    };
    bodyMedium: {
        fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
        fontFamily: string;
        letterSpacing: number;
        lineHeight: number;
        fontSize: number;
    };
    bodySmall: {
        fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
        fontFamily: string;
        letterSpacing: number;
        lineHeight: number;
        fontSize: number;
    };
    default: {
        fontFamily: string;
        letterSpacing: number;
        fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
    };
};
export declare const tokens: {
    md: {
        ref: {
            palette: {
                primary100: string;
                primary99: string;
                primary95: string;
                primary90: string;
                primary80: string;
                primary70: string;
                primary60: string;
                primary50: string;
                primary40: string;
                primary30: string;
                primary20: string;
                primary10: string;
                primary0: string;
                secondary100: string;
                secondary99: string;
                secondary95: string;
                secondary90: string;
                secondary80: string;
                secondary70: string;
                secondary60: string;
                secondary50: string;
                secondary40: string;
                secondary30: string;
                secondary20: string;
                secondary10: string;
                secondary0: string;
                tertiary100: string;
                tertiary99: string;
                tertiary95: string;
                tertiary90: string;
                tertiary80: string;
                tertiary70: string;
                tertiary60: string;
                tertiary50: string;
                tertiary40: string;
                tertiary30: string;
                tertiary20: string;
                tertiary10: string;
                tertiary0: string;
                neutral100: string;
                neutral99: string;
                neutral95: string;
                neutral90: string;
                neutral80: string;
                neutral70: string;
                neutral60: string;
                neutral50: string;
                neutral40: string;
                neutral30: string;
                neutral20: string;
                neutral10: string;
                neutral0: string;
                neutralVariant100: string;
                neutralVariant99: string;
                neutralVariant95: string;
                neutralVariant90: string;
                neutralVariant80: string;
                neutralVariant70: string;
                neutralVariant60: string;
                neutralVariant50: string;
                neutralVariant40: string;
                neutralVariant30: string;
                neutralVariant20: string;
                neutralVariant10: string;
                neutralVariant0: string;
                error100: string;
                error99: string;
                error95: string;
                error90: string;
                error80: string;
                error70: string;
                error60: string;
                error50: string;
                error40: string;
                error30: string;
                error20: string;
                error10: string;
                error0: string;
            };
            typeface: {
                brandRegular: string;
                weightRegular: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
                plainMedium: string;
                weightMedium: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
            };
            opacity: {
                level1: number;
                level2: number;
                level3: number;
                level4: number;
            };
        };
        sys: {
            typescale: {
                displayLarge: {
                    lineHeight: number;
                    fontSize: number;
                    fontFamily: string;
                    letterSpacing: number;
                    fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
                };
                displayMedium: {
                    lineHeight: number;
                    fontSize: number;
                    fontFamily: string;
                    letterSpacing: number;
                    fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
                };
                displaySmall: {
                    lineHeight: number;
                    fontSize: number;
                    fontFamily: string;
                    letterSpacing: number;
                    fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
                };
                headlineLarge: {
                    lineHeight: number;
                    fontSize: number;
                    fontFamily: string;
                    letterSpacing: number;
                    fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
                };
                headlineMedium: {
                    lineHeight: number;
                    fontSize: number;
                    fontFamily: string;
                    letterSpacing: number;
                    fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
                };
                headlineSmall: {
                    lineHeight: number;
                    fontSize: number;
                    fontFamily: string;
                    letterSpacing: number;
                    fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
                };
                titleLarge: {
                    lineHeight: number;
                    fontSize: number;
                    fontFamily: string;
                    letterSpacing: number;
                    fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
                };
                titleMedium: {
                    lineHeight: number;
                    fontSize: number;
                    fontFamily: string;
                    letterSpacing: number;
                    fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
                };
                titleSmall: {
                    letterSpacing: number;
                    lineHeight: number;
                    fontSize: number;
                    fontFamily: string;
                    fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
                };
                labelLarge: {
                    letterSpacing: number;
                    lineHeight: number;
                    fontSize: number;
                    fontFamily: string;
                    fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
                };
                labelMedium: {
                    letterSpacing: number;
                    lineHeight: number;
                    fontSize: number;
                    fontFamily: string;
                    fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
                };
                labelSmall: {
                    letterSpacing: number;
                    lineHeight: number;
                    fontSize: number;
                    fontFamily: string;
                    fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
                };
                bodyLarge: {
                    fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
                    fontFamily: string;
                    lineHeight: number;
                    fontSize: number;
                    letterSpacing: number;
                };
                bodyMedium: {
                    fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
                    fontFamily: string;
                    letterSpacing: number;
                    lineHeight: number;
                    fontSize: number;
                };
                bodySmall: {
                    fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
                    fontFamily: string;
                    letterSpacing: number;
                    lineHeight: number;
                    fontSize: number;
                };
                default: {
                    fontFamily: string;
                    letterSpacing: number;
                    fontWeight: "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900" | undefined;
                };
            };
        };
    };
};
export declare const MD3Colors: {
    primary100: string;
    primary99: string;
    primary95: string;
    primary90: string;
    primary80: string;
    primary70: string;
    primary60: string;
    primary50: string;
    primary40: string;
    primary30: string;
    primary20: string;
    primary10: string;
    primary0: string;
    secondary100: string;
    secondary99: string;
    secondary95: string;
    secondary90: string;
    secondary80: string;
    secondary70: string;
    secondary60: string;
    secondary50: string;
    secondary40: string;
    secondary30: string;
    secondary20: string;
    secondary10: string;
    secondary0: string;
    tertiary100: string;
    tertiary99: string;
    tertiary95: string;
    tertiary90: string;
    tertiary80: string;
    tertiary70: string;
    tertiary60: string;
    tertiary50: string;
    tertiary40: string;
    tertiary30: string;
    tertiary20: string;
    tertiary10: string;
    tertiary0: string;
    neutral100: string;
    neutral99: string;
    neutral95: string;
    neutral90: string;
    neutral80: string;
    neutral70: string;
    neutral60: string;
    neutral50: string;
    neutral40: string;
    neutral30: string;
    neutral20: string;
    neutral10: string;
    neutral0: string;
    neutralVariant100: string;
    neutralVariant99: string;
    neutralVariant95: string;
    neutralVariant90: string;
    neutralVariant80: string;
    neutralVariant70: string;
    neutralVariant60: string;
    neutralVariant50: string;
    neutralVariant40: string;
    neutralVariant30: string;
    neutralVariant20: string;
    neutralVariant10: string;
    neutralVariant0: string;
    error100: string;
    error99: string;
    error95: string;
    error90: string;
    error80: string;
    error70: string;
    error60: string;
    error50: string;
    error40: string;
    error30: string;
    error20: string;
    error10: string;
    error0: string;
};
//# sourceMappingURL=tokens.d.ts.map