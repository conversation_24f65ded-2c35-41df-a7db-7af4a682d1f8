{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_native", "_BottomNavigation", "_interopRequireDefault", "_MaterialCommunityIcon", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "MaterialBottomTabView", "state", "navigation", "descriptors", "rest", "buildLink", "useLinkBuilder", "createElement", "onIndexChange", "noop", "navigationState", "renderScene", "route", "key", "render", "renderTouchable", "Platform", "OS", "onPress", "accessibilityRole", "_0", "borderless", "_1", "centered", "_2", "rippleColor", "_3", "style", "Link", "to", "name", "params", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "button", "preventDefault", "styles", "touchable", "undefined", "renderIcon", "focused", "color", "options", "tabBarIcon", "direction", "I18nManager", "getConstants", "isRTL", "size", "getLabelText", "tabBarLabel", "title", "getColor", "tabBarColor", "getBadge", "tabBarBadge", "getAccessibilityLabel", "tabBarAccessibilityLabel", "getTestID", "tabBarButtonTestID", "onTabPress", "event", "emit", "type", "target", "canPreventDefault", "defaultPrevented", "dispatch", "CommonActions", "navigate", "onTabLongPress", "StyleSheet", "create", "display", "justifyContent"], "sourceRoot": "../../../../src", "sources": ["react-navigation/views/MaterialBottomTabView.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,OAAA,GAAAF,OAAA;AASA,IAAAG,iBAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,sBAAA,GAAAD,sBAAA,CAAAJ,OAAA;AAA2E,SAAAI,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAX,uBAAA,YAAAA,CAAAO,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAf,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAK,CAAA,IAAAF,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAZ,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAa,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAY5D,SAASG,qBAAqBA,CAAC;EAC5CC,KAAK;EACLC,UAAU;EACVC,WAAW;EACX,GAAGC;AACE,CAAC,EAAE;EACR,MAAMC,SAAS,GAAG,IAAAC,sBAAc,EAAC,CAAC;EAElC,oBACEvC,KAAA,CAAAwC,aAAA,CAACnC,iBAAA,CAAAK,OAAgB,EAAAiB,QAAA,KACXU,IAAI;IACRI,aAAa,EAAEC,IAAK;IACpBC,eAAe,EAAET,KAAM;IACvBU,WAAW,EAAEA,CAAC;MAAEC;IAAM,CAAC,KAAKT,WAAW,CAACS,KAAK,CAACC,GAAG,CAAC,CAACC,MAAM,CAAC,CAAE;IAC5DC,eAAe,EACbC,qBAAQ,CAACC,EAAE,KAAK,KAAK,GACjB,CAAC;MACCC,OAAO;MACPN,KAAK;MACLO,iBAAiB,EAAEC,EAAE;MACrBC,UAAU,EAAEC,EAAE;MACdC,QAAQ,EAAEC,EAAE;MACZC,WAAW,EAAEC,EAAE;MACfC,KAAK;MACL,GAAGvB;IACL,CAAC,KAAK;MACJ,oBACErC,KAAA,CAAAwC,aAAA,CAACpC,OAAA,CAAAyD,IAAI,EAAAlC,QAAA,KACCU,IAAI;QACR;QACAyB,EAAE,EAAExB,SAAS,CAACO,KAAK,CAACkB,IAAI,EAAElB,KAAK,CAACmB,MAAM,CAAE;QACxCZ,iBAAiB,EAAC,MAAM;QACxBD,OAAO,EAAG3C,CAAM,IAAK;UACnB,IACE,EAAEA,CAAC,CAACyD,OAAO,IAAIzD,CAAC,CAAC0D,MAAM,IAAI1D,CAAC,CAAC2D,OAAO,IAAI3D,CAAC,CAAC4D,QAAQ,CAAC;UAAI;UACtD5D,CAAC,CAAC6D,MAAM,IAAI,IAAI,IAAI7D,CAAC,CAAC6D,MAAM,KAAK,CAAC,CAAC,CAAC;UAAA,EACrC;YACA7D,CAAC,CAAC8D,cAAc,CAAC,CAAC;YAClBnB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAG3C,CAAC,CAAC;UACd;QACF,CAAE;QACFoD,KAAK,EAAE,CAACW,MAAM,CAACC,SAAS,EAAEZ,KAAK;MAAE,EAClC,CAAC;IAEN,CAAC,GACDa,SACL;IACDC,UAAU,EAAEA,CAAC;MAAE7B,KAAK;MAAE8B,OAAO;MAAEC;IAAM,CAAC,KAAK;MACzC,MAAM;QAAEC;MAAQ,CAAC,GAAGzC,WAAW,CAACS,KAAK,CAACC,GAAG,CAAC;MAE1C,IAAI,OAAO+B,OAAO,CAACC,UAAU,KAAK,QAAQ,EAAE;QAC1C,oBACE9E,KAAA,CAAAwC,aAAA,CAACjC,sBAAA,CAAAG,OAAqB;UACpBqE,SAAS,EAAEC,wBAAW,CAACC,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG,KAAM;UAC5DnB,IAAI,EAAEc,OAAO,CAACC,UAAW;UACzBF,KAAK,EAAEA,KAAM;UACbO,IAAI,EAAE;QAAG,CACV,CAAC;MAEN;MAEA,IAAI,OAAON,OAAO,CAACC,UAAU,KAAK,UAAU,EAAE;QAC5C,OAAOD,OAAO,CAACC,UAAU,CAAC;UAAEH,OAAO;UAAEC;QAAM,CAAC,CAAC;MAC/C;MAEA,OAAO,IAAI;IACb,CAAE;IACFQ,YAAY,EAAEA,CAAC;MAAEvC;IAAM,CAAC,KAAK;MAC3B,MAAM;QAAEgC;MAAQ,CAAC,GAAGzC,WAAW,CAACS,KAAK,CAACC,GAAG,CAAC;MAE1C,OAAO+B,OAAO,CAACQ,WAAW,KAAKZ,SAAS,GACpCI,OAAO,CAACQ,WAAW,GACnBR,OAAO,CAACS,KAAK,KAAKb,SAAS,GAC3BI,OAAO,CAACS,KAAK,GACZzC,KAAK,CAAmBkB,IAAI;IACnC,CAAE;IACFwB,QAAQ,EAAEA,CAAC;MAAE1C;IAAM,CAAC,KAAKT,WAAW,CAACS,KAAK,CAACC,GAAG,CAAC,CAAC+B,OAAO,CAACW,WAAY;IACpEC,QAAQ,EAAEA,CAAC;MAAE5C;IAAM,CAAC,KAAKT,WAAW,CAACS,KAAK,CAACC,GAAG,CAAC,CAAC+B,OAAO,CAACa,WAAY;IACpEC,qBAAqB,EAAEA,CAAC;MAAE9C;IAAM,CAAC,KAC/BT,WAAW,CAACS,KAAK,CAACC,GAAG,CAAC,CAAC+B,OAAO,CAACe,wBAChC;IACDC,SAAS,EAAEA,CAAC;MAAEhD;IAAM,CAAC,KACnBT,WAAW,CAACS,KAAK,CAACC,GAAG,CAAC,CAAC+B,OAAO,CAACiB,kBAChC;IACDC,UAAU,EAAEA,CAAC;MAAElD,KAAK;MAAEyB;IAAe,CAAC,KAAK;MACzC,MAAM0B,KAAK,GAAG7D,UAAU,CAAC8D,IAAI,CAAC;QAC5BC,IAAI,EAAE,UAAU;QAChBC,MAAM,EAAEtD,KAAK,CAACC,GAAG;QACjBsD,iBAAiB,EAAE;MACrB,CAAC,CAAC;MAEF,IAAIJ,KAAK,CAACK,gBAAgB,EAAE;QAC1B/B,cAAc,CAAC,CAAC;MAClB,CAAC,MAAM;QACLnC,UAAU,CAACmE,QAAQ,CAAC;UAClB,GAAGC,qBAAa,CAACC,QAAQ,CAAC3D,KAAK,CAACkB,IAAI,EAAElB,KAAK,CAACmB,MAAM,CAAC;UACnDmC,MAAM,EAAEjE,KAAK,CAACY;QAChB,CAAC,CAAC;MACJ;IACF,CAAE;IACF2D,cAAc,EAAEA,CAAC;MAAE5D;IAAM,CAAC,KACxBV,UAAU,CAAC8D,IAAI,CAAC;MAAEC,IAAI,EAAE,cAAc;MAAEC,MAAM,EAAEtD,KAAK,CAACC;IAAI,CAAC;EAC5D,EACF,CAAC;AAEN;AAEA,MAAMyB,MAAM,GAAGmC,uBAAU,CAACC,MAAM,CAAC;EAC/BnC,SAAS,EAAE;IACToC,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,SAASnE,IAAIA,CAAA,EAAG,CAAC", "ignoreList": []}