{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_native", "_MaterialBottomTabView", "_interopRequireDefault", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "MaterialBottomTabNavigator", "id", "initialRouteName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "screenListeners", "screenOptions", "rest", "state", "descriptors", "navigation", "NavigationContent", "useNavigationBuilder", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createElement", "_default", "exports", "createNavigatorFactory"], "sourceRoot": "../../../../src", "sources": ["react-navigation/navigators/createMaterialBottomTabNavigator.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAD,OAAA;AAgBA,IAAAE,sBAAA,GAAAC,sBAAA,CAAAH,OAAA;AAAmE,SAAAG,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAL,wBAAAK,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAT,uBAAA,YAAAA,CAAAK,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAf,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAK,CAAA,IAAAF,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAZ,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAa,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAWnE,SAASG,0BAA0BA,CAAC;EAClCC,EAAE;EACFC,gBAAgB;EAChBC,YAAY;EACZC,QAAQ;EACRC,eAAe;EACfC,aAAa;EACb,GAAGC;AAC4B,CAAC,EAAE;EAClC,MAAM;IAAEC,KAAK;IAAEC,WAAW;IAAEC,UAAU;IAAEC;EAAkB,CAAC,GACzD,IAAAC,4BAAoB,EAMlBC,iBAAS,EAAE;IACXZ,EAAE;IACFC,gBAAgB;IAChBC,YAAY;IACZC,QAAQ;IACRC,eAAe;IACfC;EACF,CAAC,CAAC;EAEJ,oBACErC,KAAA,CAAA6C,aAAA,CAACH,iBAAiB,qBAChB1C,KAAA,CAAA6C,aAAA,CAACzC,sBAAA,CAAAI,OAAqB,EAAAiB,QAAA,KAChBa,IAAI;IACRC,KAAK,EAAEA,KAAM;IACbE,UAAU,EAAEA,UAAW;IACvBD,WAAW,EAAEA;EAAY,EAC1B,CACgB,CAAC;AAExB;;AAEA;AACA;AACA;AACA;AAHA,IAAAM,QAAA,GAAAC,OAAA,CAAAvC,OAAA,GAIe,IAAAwC,8BAAsB,EAKnCjB,0BAA0B,CAAC", "ignoreList": []}