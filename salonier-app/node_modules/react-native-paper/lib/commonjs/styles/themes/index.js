"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "MD2DarkTheme", {
  enumerable: true,
  get: function () {
    return _DarkTheme2.MD2DarkTheme;
  }
});
Object.defineProperty(exports, "MD2LightTheme", {
  enumerable: true,
  get: function () {
    return _LightTheme2.MD2LightTheme;
  }
});
Object.defineProperty(exports, "MD3DarkTheme", {
  enumerable: true,
  get: function () {
    return _DarkTheme.MD3DarkTheme;
  }
});
Object.defineProperty(exports, "MD3LightTheme", {
  enumerable: true,
  get: function () {
    return _LightTheme.MD3LightTheme;
  }
});
var _LightTheme = require("./v3/LightTheme");
var _DarkTheme = require("./v3/DarkTheme");
var _LightTheme2 = require("./v2/LightTheme");
var _DarkTheme2 = require("./v2/DarkTheme");
//# sourceMappingURL=index.js.map