{"version": 3, "names": ["_color", "_interopRequireDefault", "require", "e", "__esModule", "default", "getTextColor", "theme", "disabled", "type", "_theme$colors", "colors", "dark", "error", "isV3", "onSurfaceDisabled", "onSurfaceVariant", "color", "text", "alpha", "rgb", "string"], "sourceRoot": "../../../../src", "sources": ["components/HelperText/utils.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA0B,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAUnB,SAASG,YAAYA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAgB,CAAC,EAAE;EAAA,IAAAC,aAAA;EACjE,MAAM;IAAEC,MAAM;IAAEC;EAAK,CAAC,GAAGL,KAAK;EAE9B,IAAIE,IAAI,KAAK,OAAO,EAAE;IACpB,OAAOE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,KAAK;EACtB;EAEA,IAAIN,KAAK,CAACO,IAAI,EAAE;IACd,IAAIN,QAAQ,EAAE;MACZ,OAAOD,KAAK,CAACI,MAAM,CAACI,iBAAiB;IACvC,CAAC,MAAM;MACL,OAAOR,KAAK,CAACI,MAAM,CAACK,gBAAgB;IACtC;EACF;EAEA,OAAO,IAAAC,cAAK,EAACV,KAAK,aAALA,KAAK,gBAAAG,aAAA,GAALH,KAAK,CAAEI,MAAM,cAAAD,aAAA,uBAAbA,aAAA,CAAeQ,IAAI,CAAC,CAC9BC,KAAK,CAACP,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,CACxBQ,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;AACb", "ignoreList": []}