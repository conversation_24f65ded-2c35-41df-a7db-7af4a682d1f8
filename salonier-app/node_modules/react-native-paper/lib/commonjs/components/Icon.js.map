{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_MaterialCommunityIcon", "_settings", "_theming", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "isImageSource", "source", "prototype", "uri", "Platform", "OS", "startsWith", "test", "getIconId", "isValidIcon", "exports", "isEqualIcon", "a", "b", "Icon", "color", "size", "theme", "themeOverrides", "testID", "rest", "useInternalTheme", "direction", "I18nManager", "getConstants", "isRTL", "s", "iconColor", "isV3", "colors", "onSurface", "text", "createElement", "Image", "style", "transform", "scaleX", "width", "height", "tintColor", "resizeMode", "accessibilityProps", "accessibilityIgnoresInvertColors", "Consumer", "icon", "name", "_default"], "sourceRoot": "../../../src", "sources": ["components/Icon.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAOA,IAAAE,sBAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,QAAA,GAAAJ,OAAA;AAAmD,SAAAD,wBAAAM,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAR,uBAAA,YAAAA,CAAAM,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAkB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAjB,CAAA,aAAAJ,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAC,CAAA,GAAAqB,SAAA,CAAAtB,CAAA,YAAAG,CAAA,IAAAF,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAd,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAe,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAkBnD,MAAMG,aAAa,GAAIC,MAAW;AAChC;AACC,OAAOA,MAAM,KAAK,QAAQ,IACzBA,MAAM,KAAK,IAAI,IACfV,MAAM,CAACW,SAAS,CAACb,cAAc,CAACC,IAAI,CAACW,MAAM,EAAE,KAAK,CAAC,IACnD,OAAOA,MAAM,CAACE,GAAG,KAAK,QAAQ;AAChC;AACA,OAAOF,MAAM,KAAK,QAAQ;AAC1B;AACCG,qBAAQ,CAACC,EAAE,KAAK,KAAK,IACpB,OAAOJ,MAAM,KAAK,QAAQ,KACzBA,MAAM,CAACK,UAAU,CAAC,YAAY,CAAC,IAC9B,+BAA+B,CAACC,IAAI,CAACN,MAAM,CAAC,CAAE;AAEpD,MAAMO,SAAS,GAAIP,MAAW,IAAK;EACjC,IACE,OAAOA,MAAM,KAAK,QAAQ,IAC1BA,MAAM,KAAK,IAAI,IACfV,MAAM,CAACW,SAAS,CAACb,cAAc,CAACC,IAAI,CAACW,MAAM,EAAE,KAAK,CAAC,IACnD,OAAOA,MAAM,CAACE,GAAG,KAAK,QAAQ,EAC9B;IACA,OAAOF,MAAM,CAACE,GAAG;EACnB;EAEA,OAAOF,MAAM;AACf,CAAC;AAEM,MAAMQ,WAAW,GAAIR,MAAW,IACrC,OAAOA,MAAM,KAAK,QAAQ,IAC1B,OAAOA,MAAM,KAAK,UAAU,IAC5BD,aAAa,CAACC,MAAM,CAAC;AAACS,OAAA,CAAAD,WAAA,GAAAA,WAAA;AAEjB,MAAME,WAAW,GAAGA,CAACC,CAAM,EAAEC,CAAM,KACxCD,CAAC,KAAKC,CAAC,IAAIL,SAAS,CAACI,CAAC,CAAC,KAAKJ,SAAS,CAACK,CAAC,CAAC;AAACH,OAAA,CAAAC,WAAA,GAAAA,WAAA;AAqB3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMG,IAAI,GAAGA,CAAC;EACZb,MAAM;EACNc,KAAK;EACLC,IAAI;EACJC,KAAK,EAAEC,cAAc;EACrBC,MAAM;EACN,GAAGC;AACE,CAAC,KAAK;EACX,MAAMH,KAAK,GAAG,IAAAI,yBAAgB,EAACH,cAAc,CAAC;EAC9C,MAAMI,SAAS,GACb,OAAOrB,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACqB,SAAS,IAAIrB,MAAM,CAACA,MAAM,GAC3DA,MAAM,CAACqB,SAAS,KAAK,MAAM,GACzBC,wBAAW,CAACC,YAAY,CAAC,CAAC,CAACC,KAAK,GAC9B,KAAK,GACL,KAAK,GACPxB,MAAM,CAACqB,SAAS,GAClB,IAAI;EAEV,MAAMI,CAAC,GACL,OAAOzB,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACqB,SAAS,IAAIrB,MAAM,CAACA,MAAM,GAC3DA,MAAM,CAACA,MAAM,GACbA,MAAM;EACZ,MAAM0B,SAAS,GACbZ,KAAK,KAAKE,KAAK,CAACW,IAAI,GAAGX,KAAK,CAACY,MAAM,CAACC,SAAS,GAAGb,KAAK,CAACY,MAAM,CAACE,IAAI,CAAC;EAEpE,IAAI/B,aAAa,CAAC0B,CAAC,CAAC,EAAE;IACpB,oBACE1D,KAAA,CAAAgE,aAAA,CAAC7D,YAAA,CAAA8D,KAAK,EAAAvC,QAAA,KACA0B,IAAI;MACRD,MAAM,EAAEA,MAAO;MACflB,MAAM,EAAEyB,CAAE;MACVQ,KAAK,EAAE,CACL;QACEC,SAAS,EAAE,CAAC;UAAEC,MAAM,EAAEd,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG;QAAE,CAAC;MACtD,CAAC,EACD;QACEe,KAAK,EAAErB,IAAI;QACXsB,MAAM,EAAEtB,IAAI;QACZuB,SAAS,EAAExB,KAAK;QAChByB,UAAU,EAAE;MACd,CAAC;IACD,GACEC,yCAAkB;MACtBC,gCAAgC;IAAA,EACjC,CAAC;EAEN,CAAC,MAAM,IAAI,OAAOhB,CAAC,KAAK,QAAQ,EAAE;IAChC,oBACE1D,KAAA,CAAAgE,aAAA,CAAC3D,SAAA,CAAAsE,QAAgB,QACd,CAAC;MAAEC;IAAK,CAAC,KAAK;MACb,OAAOA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAG;QACZC,IAAI,EAAEnB,CAAC;QACPX,KAAK,EAAEY,SAAS;QAChBX,IAAI;QACJM,SAAS;QACTH;MACF,CAAC,CAAC;IACJ,CACgB,CAAC;EAEvB,CAAC,MAAM,IAAI,OAAOO,CAAC,KAAK,UAAU,EAAE;IAClC,OAAOA,CAAC,CAAC;MAAEX,KAAK,EAAEY,SAAS;MAAEX,IAAI;MAAEM,SAAS;MAAEH;IAAO,CAAC,CAAC;EACzD;EAEA,OAAO,IAAI;AACb,CAAC;AAAC,IAAA2B,QAAA,GAAApC,OAAA,CAAAzB,OAAA,GAEa6B,IAAI", "ignoreList": []}