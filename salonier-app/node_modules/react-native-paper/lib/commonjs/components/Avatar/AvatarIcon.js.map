{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_theming", "_colors", "_getContrastingColor", "_interopRequireDefault", "_Icon", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "defaultSize", "Avatar", "icon", "size", "style", "theme", "themeOverrides", "rest", "_theme$colors", "useInternalTheme", "backgroundColor", "colors", "primary", "restStyle", "StyleSheet", "flatten", "textColor", "color", "getContrastingColor", "white", "createElement", "View", "width", "height", "borderRadius", "styles", "container", "source", "displayName", "create", "justifyContent", "alignItems", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/Avatar/AvatarIcon.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AAEA,IAAAI,oBAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,KAAA,GAAAD,sBAAA,CAAAL,OAAA;AAA2C,SAAAK,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAZ,uBAAA,YAAAA,CAAAQ,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAf,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAK,CAAA,IAAAF,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAZ,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAa,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAE3C,MAAMG,WAAW,GAAG,EAAE;AAsBtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAGA,CAAC;EACdC,IAAI;EACJC,IAAI,GAAGH,WAAW;EAClBI,KAAK;EACLC,KAAK,EAAEC,cAAc;EACrB,GAAGC;AACE,CAAC,KAAK;EAAA,IAAAC,aAAA;EACX,MAAMH,KAAK,GAAG,IAAAI,yBAAgB,EAACH,cAAc,CAAC;EAC9C,MAAM;IAAEI,eAAe,IAAAF,aAAA,GAAGH,KAAK,CAACM,MAAM,cAAAH,aAAA,uBAAZA,aAAA,CAAcI,OAAO;IAAE,GAAGC;EAAU,CAAC,GAC7DC,uBAAU,CAACC,OAAO,CAACX,KAAK,CAAC,IAAI,CAAC,CAAC;EACjC,MAAMY,SAAS,GACbT,IAAI,CAACU,KAAK,IACV,IAAAC,4BAAmB,EAACR,eAAe,EAAES,aAAK,EAAE,oBAAoB,CAAC;EAEnE,oBACErD,KAAA,CAAAsD,aAAA,CAACnD,YAAA,CAAAoD,IAAI,EAAA3B,QAAA;IACHU,KAAK,EAAE,CACL;MACEkB,KAAK,EAAEnB,IAAI;MACXoB,MAAM,EAAEpB,IAAI;MACZqB,YAAY,EAAErB,IAAI,GAAG,CAAC;MACtBO;IACF,CAAC,EACDe,MAAM,CAACC,SAAS,EAChBb,SAAS;EACT,GACEN,IAAI,gBAERzC,KAAA,CAAAsD,aAAA,CAAC9C,KAAA,CAAAG,OAAI;IAACkD,MAAM,EAAEzB,IAAK;IAACe,KAAK,EAAED,SAAU;IAACb,IAAI,EAAEA,IAAI,GAAG;EAAI,CAAE,CACrD,CAAC;AAEX,CAAC;AAEDF,MAAM,CAAC2B,WAAW,GAAG,aAAa;AAElC,MAAMH,MAAM,GAAGX,uBAAU,CAACe,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAxD,OAAA,GAEYwB,MAAM", "ignoreList": []}