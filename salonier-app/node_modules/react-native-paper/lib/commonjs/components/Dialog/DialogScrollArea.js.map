{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_theming", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "DialogScrollArea", "props", "theme", "useInternalTheme", "borderStyles", "borderColor", "isV3", "colors", "surfaceVariant", "borderTopWidth", "StyleSheet", "hairlineWidth", "borderBottomWidth", "createElement", "View", "style", "styles", "container", "v3Container", "children", "displayName", "create", "paddingHorizontal", "flexGrow", "flexShrink", "marginBottom", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/Dialog/DialogScrollArea.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAIA,IAAAE,QAAA,GAAAF,OAAA;AAAsD,SAAAD,wBAAAI,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAN,uBAAA,YAAAA,CAAAI,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAkB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAjB,CAAA,aAAAJ,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAC,CAAA,GAAAqB,SAAA,CAAAtB,CAAA,YAAAG,CAAA,IAAAF,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAd,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAe,QAAA,CAAAK,KAAA,OAAAF,SAAA;AActD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,gBAAgB,GAAIC,KAAY,IAAK;EACzC,MAAMC,KAAK,GAAG,IAAAC,yBAAgB,EAACF,KAAK,CAACC,KAAK,CAAC;EAC3C,MAAME,YAAY,GAAG;IACnBC,WAAW,EAAEH,KAAK,CAACI,IAAI,GACnBJ,KAAK,CAACK,MAAM,CAACC,cAAc,GAC3B,oBAAoB;IACxBC,cAAc,EAAEP,KAAK,CAACI,IAAI,GAAG,CAAC,GAAGI,uBAAU,CAACC,aAAa;IACzDC,iBAAiB,EAAEV,KAAK,CAACI,IAAI,GAAG,CAAC,GAAGI,uBAAU,CAACC;EACjD,CAAC;EACD,oBACEzC,KAAA,CAAA2C,aAAA,CAACxC,YAAA,CAAAyC,IAAI,EAAApB,QAAA,KACCO,KAAK;IACTc,KAAK,EAAE,CACLC,MAAM,CAACC,SAAS,EAChBb,YAAY,EACZF,KAAK,CAACI,IAAI,IAAIU,MAAM,CAACE,WAAW,EAChCjB,KAAK,CAACc,KAAK;EACX,IAEDd,KAAK,CAACkB,QACH,CAAC;AAEX,CAAC;AAEDnB,gBAAgB,CAACoB,WAAW,GAAG,mBAAmB;AAElD,MAAMJ,MAAM,GAAGN,uBAAU,CAACW,MAAM,CAAC;EAC/BJ,SAAS,EAAE;IACTK,iBAAiB,EAAE,EAAE;IACrBC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE;EACd,CAAC;EACDN,WAAW,EAAE;IACXO,YAAY,EAAE;EAChB;AACF,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAA1C,OAAA,GAEYe,gBAAgB", "ignoreList": []}