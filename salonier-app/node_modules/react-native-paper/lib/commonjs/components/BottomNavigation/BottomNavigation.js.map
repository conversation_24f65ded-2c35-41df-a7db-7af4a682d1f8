{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_useLatestCallback", "_interopRequireDefault", "_BottomNavigationBar", "_BottomNavigationRouteScreen", "_theming", "_useAnimatedValueArray", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "FAR_FAR_AWAY", "Platform", "OS", "SceneComponent", "memo", "component", "rest", "createElement", "BottomNavigation", "navigationState", "renderScene", "renderIcon", "renderLabel", "renderTouchable", "getLabelText", "getBadge", "getColor", "getAccessibilityLabel", "getTestID", "activeColor", "inactiveColor", "keyboardHidesNavigationBar", "barStyle", "labeled", "style", "activeIndicatorStyle", "sceneAnimationEnabled", "sceneAnimationType", "sceneAnimationEasing", "onTabPress", "onTabLongPress", "onIndexChange", "shifting", "shiftingProp", "safeAreaInsets", "labelMaxFontSizeMultiplier", "compact", "compactProp", "testID", "theme", "themeOverrides", "getLazy", "route", "lazy", "useInternalTheme", "scale", "animation", "isV3", "routes", "console", "warn", "<PERSON><PERSON><PERSON>", "index", "key", "tabsPositionAnims", "useAnimatedValueArray", "map", "_", "offsetsAnims", "loaded", "setLoaded", "useState", "includes", "animateToIndex", "useCallback", "Animated", "parallel", "timing", "toValue", "duration", "useNativeDriver", "easing", "start", "finished", "for<PERSON>ach", "offset", "setValue", "useEffect", "prevNavigationState", "useRef", "undefined", "_prevNavigationState$", "current", "handleTabPress", "useLatestCallback", "event", "defaultPrevented", "findIndex", "jumpTo", "colors", "View", "styles", "container", "content", "backgroundColor", "background", "_prevNavigationState$2", "focused", "previouslyFocused", "countAlphaOffscreen", "renderToHardwareTextureAndroid", "opacity", "interpolate", "inputRange", "outputRange", "offsetTarget", "top", "left", "zIndex", "pointerEvents", "accessibilityElementsHidden", "importantForAccessibility", "visibility", "StyleSheet", "absoluteFill", "collapsable", "removeClippedSubviews", "needsOffscreenAlphaCompositing", "transform", "translateX", "translateY", "animationEasing", "SceneMap", "scenes", "Bar", "BottomNavigationBar", "_default", "exports", "create", "flex", "overflow"], "sourceRoot": "../../../../src", "sources": ["components/BottomNavigation/BottomNavigation.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAWA,IAAAE,kBAAA,GAAAC,sBAAA,CAAAH,OAAA;AAEA,IAAAI,oBAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,4BAAA,GAAAF,sBAAA,CAAAH,OAAA;AACA,IAAAM,QAAA,GAAAN,OAAA;AAEA,IAAAO,sBAAA,GAAAJ,sBAAA,CAAAH,OAAA;AAAsE,SAAAG,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAT,wBAAAS,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAb,uBAAA,YAAAA,CAAAS,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAf,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAK,CAAA,IAAAF,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAZ,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAa,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAyPtE,MAAMG,YAAY,GAAGC,qBAAQ,CAACC,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI;AAErD,MAAMC,cAAc,gBAAGtC,KAAK,CAACuC,IAAI,CAAC,CAAC;EAAEC,SAAS;EAAE,GAAGC;AAAU,CAAC,kBAC5DzC,KAAK,CAAC0C,aAAa,CAACF,SAAS,EAAEC,IAAI,CACrC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,gBAAgB,GAAGA,CAA0B;EACjDC,eAAe;EACfC,WAAW;EACXC,UAAU;EACVC,WAAW;EACXC,eAAe;EACfC,YAAY;EACZC,QAAQ;EACRC,QAAQ;EACRC,qBAAqB;EACrBC,SAAS;EACTC,WAAW;EACXC,aAAa;EACbC,0BAA0B,GAAGpB,qBAAQ,CAACC,EAAE,KAAK,SAAS;EACtDoB,QAAQ;EACRC,OAAO,GAAG,IAAI;EACdC,KAAK;EACLC,oBAAoB;EACpBC,qBAAqB,GAAG,KAAK;EAC7BC,kBAAkB,GAAG,SAAS;EAC9BC,oBAAoB;EACpBC,UAAU;EACVC,cAAc;EACdC,aAAa;EACbC,QAAQ,EAAEC,YAAY;EACtBC,cAAc;EACdC,0BAA0B,GAAG,CAAC;EAC9BC,OAAO,EAAEC,WAAW;EACpBC,MAAM,GAAG,mBAAmB;EAC5BC,KAAK,EAAEC,cAAc;EACrBC,OAAO,GAAGA,CAAC;IAAEC;EAAwB,CAAC,KAAKA,KAAK,CAACC;AACrC,CAAC,KAAK;EAClB,MAAMJ,KAAK,GAAG,IAAAK,yBAAgB,EAACJ,cAAc,CAAC;EAC9C,MAAM;IAAEK;EAAM,CAAC,GAAGN,KAAK,CAACO,SAAS;EACjC,MAAMV,OAAO,GAAGC,WAAW,IAAI,CAACE,KAAK,CAACQ,IAAI;EAC1C,IAAIf,QAAQ,GACVC,YAAY,KAAKM,KAAK,CAACQ,IAAI,GAAG,KAAK,GAAGtC,eAAe,CAACuC,MAAM,CAAClD,MAAM,GAAG,CAAC,CAAC;EAE1E,IAAIkC,QAAQ,IAAIvB,eAAe,CAACuC,MAAM,CAAClD,MAAM,GAAG,CAAC,EAAE;IACjDkC,QAAQ,GAAG,KAAK;IAChBiB,OAAO,CAACC,IAAI,CACV,kEACF,CAAC;EACH;EAEA,MAAMC,UAAU,GAAG1C,eAAe,CAACuC,MAAM,CAACvC,eAAe,CAAC2C,KAAK,CAAC,CAACC,GAAG;;EAEpE;AACF;AACA;AACA;EACE,MAAMC,iBAAiB,GAAG,IAAAC,8BAAqB,EAC7C9C,eAAe,CAACuC,MAAM,CAACQ,GAAG,CAAC,CAACC,CAAC,EAAE1E,CAAC,KAC9BA,CAAC,KAAK0B,eAAe,CAAC2C,KAAK,GAAG,CAAC,GAAGrE,CAAC,IAAI0B,eAAe,CAAC2C,KAAK,GAAG,CAAC,GAAG,CAAC,CACtE,CACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMM,YAAY,GAAG,IAAAH,8BAAqB,EACxC9C,eAAe,CAACuC,MAAM,CAACQ,GAAG;EACxB;EACA,CAACC,CAAC,EAAE1E,CAAC,KAAMA,CAAC,KAAK0B,eAAe,CAAC2C,KAAK,GAAG,CAAC,GAAG,CAC/C,CACF,CAAC;;EAED;AACF;AACA;EACE,MAAM,CAACO,MAAM,EAAEC,SAAS,CAAC,GAAG/F,KAAK,CAACgG,QAAQ,CAAW,CAACV,UAAU,CAAC,CAAC;EAElE,IAAI,CAACQ,MAAM,CAACG,QAAQ,CAACX,UAAU,CAAC,EAAE;IAChC;IACAS,SAAS,CAAED,MAAM,IAAK,CAAC,GAAGA,MAAM,EAAER,UAAU,CAAC,CAAC;EAChD;EAEA,MAAMY,cAAc,GAAGlG,KAAK,CAACmG,WAAW,CACrCZ,KAAa,IAAK;IACjBa,qBAAQ,CAACC,QAAQ,CAAC,CAChB,GAAGzD,eAAe,CAACuC,MAAM,CAACQ,GAAG,CAAC,CAACC,CAAC,EAAE1E,CAAC,KACjCkF,qBAAQ,CAACE,MAAM,CAACb,iBAAiB,CAACvE,CAAC,CAAC,EAAE;MACpCqF,OAAO,EAAErF,CAAC,KAAKqE,KAAK,GAAG,CAAC,GAAGrE,CAAC,IAAIqE,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAC9CiB,QAAQ,EAAE9B,KAAK,CAACQ,IAAI,IAAIf,QAAQ,GAAG,GAAG,GAAGa,KAAK,GAAG,CAAC;MAClDyB,eAAe,EAAE,IAAI;MACrBC,MAAM,EAAE3C;IACV,CAAC,CACH,CAAC,CACF,CAAC,CAAC4C,KAAK,CAAC,CAAC;MAAEC;IAAS,CAAC,KAAK;MACzB,IAAIA,QAAQ,EAAE;QACZ;QACA;QACAf,YAAY,CAACgB,OAAO,CAAC,CAACC,MAAM,EAAE5F,CAAC,KAAK;UAClC,IAAIA,CAAC,KAAKqE,KAAK,EAAE;YACfuB,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;UACpB,CAAC,MAAM;YACLD,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;UACpB;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC,EACD,CACE5C,QAAQ,EACRvB,eAAe,CAACuC,MAAM,EACtBU,YAAY,EACZb,KAAK,EACLS,iBAAiB,EACjB1B,oBAAoB,EACpBW,KAAK,CAET,CAAC;EAED1E,KAAK,CAACgH,SAAS,CAAC,MAAM;IACpB;IACA;IACAd,cAAc,CAACtD,eAAe,CAAC2C,KAAK,CAAC;IACrC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM0B,mBAAmB,GAAGjH,KAAK,CAACkH,MAAM,CACtCC,SACF,CAAC;EAEDnH,KAAK,CAACgH,SAAS,CAAC,MAAM;IACpB;IACAnB,YAAY,CAACgB,OAAO,CAAC,CAACC,MAAM,EAAE5F,CAAC,KAAK;MAAA,IAAAkG,qBAAA;MAClC,IACElG,CAAC,KAAK0B,eAAe,CAAC2C,KAAK,IAC3BrE,CAAC,OAAAkG,qBAAA,GAAKH,mBAAmB,CAACI,OAAO,cAAAD,qBAAA,uBAA3BA,qBAAA,CAA6B7B,KAAK,GACxC;QACAuB,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IAEFb,cAAc,CAACtD,eAAe,CAAC2C,KAAK,CAAC;EACvC,CAAC,EAAE,CAAC3C,eAAe,CAAC2C,KAAK,EAAEW,cAAc,EAAEL,YAAY,CAAC,CAAC;EAEzD,MAAMyB,cAAc,GAAG,IAAAC,0BAAiB,EACrCC,KAAuC,IAAK;IAC3CxD,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAGwD,KAAK,CAAC;IAEnB,IAAIA,KAAK,CAACC,gBAAgB,EAAE;MAC1B;IACF;IAEA,MAAMlC,KAAK,GAAG3C,eAAe,CAACuC,MAAM,CAACuC,SAAS,CAC3C7C,KAAK,IAAK2C,KAAK,CAAC3C,KAAK,CAACW,GAAG,KAAKX,KAAK,CAACW,GACvC,CAAC;IAED,IAAID,KAAK,KAAK3C,eAAe,CAAC2C,KAAK,EAAE;MACnC0B,mBAAmB,CAACI,OAAO,GAAGzE,eAAe;MAC7CsB,aAAa,CAACqB,KAAK,CAAC;IACtB;EACF,CACF,CAAC;EAED,MAAMoC,MAAM,GAAG,IAAAJ,0BAAiB,EAAE/B,GAAW,IAAK;IAChD,MAAMD,KAAK,GAAG3C,eAAe,CAACuC,MAAM,CAACuC,SAAS,CAC3C7C,KAAK,IAAKA,KAAK,CAACW,GAAG,KAAKA,GAC3B,CAAC;IAEDyB,mBAAmB,CAACI,OAAO,GAAGzE,eAAe;IAC7CsB,aAAa,CAACqB,KAAK,CAAC;EACtB,CAAC,CAAC;EAEF,MAAM;IAAEJ;EAAO,CAAC,GAAGvC,eAAe;EAClC,MAAM;IAAEgF;EAAO,CAAC,GAAGlD,KAAK;EAExB,oBACE1E,KAAA,CAAA0C,aAAA,CAACvC,YAAA,CAAA0H,IAAI;IAAClE,KAAK,EAAE,CAACmE,MAAM,CAACC,SAAS,EAAEpE,KAAK,CAAE;IAACc,MAAM,EAAEA;EAAO,gBACrDzE,KAAA,CAAA0C,aAAA,CAACvC,YAAA,CAAA0H,IAAI;IAAClE,KAAK,EAAE,CAACmE,MAAM,CAACE,OAAO,EAAE;MAAEC,eAAe,EAAEL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEM;IAAW,CAAC;EAAE,GACpE/C,MAAM,CAACQ,GAAG,CAAC,CAACd,KAAK,EAAEU,KAAK,KAAK;IAAA,IAAA4C,sBAAA;IAC5B,IAAIvD,OAAO,CAAC;MAAEC;IAAM,CAAC,CAAC,KAAK,KAAK,IAAI,CAACiB,MAAM,CAACG,QAAQ,CAACpB,KAAK,CAACW,GAAG,CAAC,EAAE;MAC/D;MACA,OAAO,IAAI;IACb;IAEA,MAAM4C,OAAO,GAAGxF,eAAe,CAAC2C,KAAK,KAAKA,KAAK;IAC/C,MAAM8C,iBAAiB,GACrB,EAAAF,sBAAA,GAAAlB,mBAAmB,CAACI,OAAO,cAAAc,sBAAA,uBAA3BA,sBAAA,CAA6B5C,KAAK,MAAKA,KAAK;IAC9C,MAAM+C,mBAAmB,GACvBzE,qBAAqB,KAAKuE,OAAO,IAAIC,iBAAiB,CAAC;IACzD,MAAME,8BAA8B,GAClC1E,qBAAqB,IAAIuE,OAAO;IAElC,MAAMI,OAAO,GAAG3E,qBAAqB,GACjC4B,iBAAiB,CAACF,KAAK,CAAC,CAACkD,WAAW,CAAC;MACnCC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC,GACFP,OAAO,GACP,CAAC,GACD,CAAC;IAEL,MAAMQ,YAAY,GAAGR,OAAO,GAAG,CAAC,GAAGjG,YAAY;IAE/C,MAAM0G,GAAG,GAAGhF,qBAAqB,GAC7BgC,YAAY,CAACN,KAAK,CAAC,CAACkD,WAAW,CAAC;MAC9BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAAC,CAAC,EAAEC,YAAY;IAC/B,CAAC,CAAC,GACFA,YAAY;IAEhB,MAAME,IAAI,GACRhF,kBAAkB,KAAK,UAAU,GAC7B2B,iBAAiB,CAACF,KAAK,CAAC,CAACkD,WAAW,CAAC;MACnCC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBC,WAAW,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;IAC1B,CAAC,CAAC,GACF,CAAC;IAEP,MAAMI,MAAM,GAAGX,OAAO,GAAG,CAAC,GAAG,CAAC;IAE9B,oBACEpI,KAAA,CAAA0C,aAAA,CAACnC,4BAAA,CAAAK,OAA2B;MAC1B4E,GAAG,EAAEX,KAAK,CAACW,GAAI;MACfwD,aAAa,EAAEZ,OAAO,GAAG,MAAM,GAAG,MAAO;MACzCa,2BAA2B,EAAE,CAACb,OAAQ;MACtCc,yBAAyB,EACvBd,OAAO,GAAG,MAAM,GAAG,qBACpB;MACD7C,KAAK,EAAEA,KAAM;MACb4D,UAAU,EAAEX,OAAQ;MACpB7E,KAAK,EAAE,CAACyF,uBAAU,CAACC,YAAY,EAAE;QAAEN;MAAO,CAAC,CAAE;MAC7CO,WAAW,EAAE,KAAM;MACnBC,qBAAqB;MACnB;MACA;MACAnH,qBAAQ,CAACC,EAAE,KAAK,KAAK,GAAGO,eAAe,CAAC2C,KAAK,KAAKA,KAAK,GAAG;IAC3D,gBAEDvF,KAAA,CAAA0C,aAAA,CAACvC,YAAA,CAAAiG,QAAQ,CAACyB,IAAI,EAAAhG,QAAA,KACPO,qBAAQ,CAACC,EAAE,KAAK,SAAS,IAAI;MAChCmH,8BAA8B,EAAElB;IAClC,CAAC;MACDC,8BAA8B,EAAEA,8BAA+B;MAC/D5E,KAAK,EAAE,CACLmE,MAAM,CAACE,OAAO,EACd;QACEQ,OAAO;QACPiB,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAEZ;QAAK,CAAC,EAAE;UAAEa,UAAU,EAAEd;QAAI,CAAC;MACvD,CAAC;IACD,IAEDhG,WAAW,CAAC;MAAEgC,KAAK;MAAE8C;IAAO,CAAC,CACjB,CACY,CAAC;EAElC,CAAC,CACG,CAAC,eACP3H,KAAA,CAAA0C,aAAA,CAACpC,oBAAA,CAAAM,OAAmB;IAClBgC,eAAe,EAAEA,eAAgB;IACjCE,UAAU,EAAEA,UAAW;IACvBC,WAAW,EAAEA,WAAY;IACzBC,eAAe,EAAEA,eAAgB;IACjCC,YAAY,EAAEA,YAAa;IAC3BC,QAAQ,EAAEA,QAAS;IACnBC,QAAQ,EAAEA,QAAS;IACnBC,qBAAqB,EAAEA,qBAAsB;IAC7CC,SAAS,EAAEA,SAAU;IACrBC,WAAW,EAAEA,WAAY;IACzBC,aAAa,EAAEA,aAAc;IAC7BC,0BAA0B,EAAEA,0BAA2B;IACvDG,KAAK,EAAEF,QAAS;IAChBG,oBAAoB,EAAEA,oBAAqB;IAC3CF,OAAO,EAAEA,OAAQ;IACjBkG,eAAe,EAAE7F,oBAAqB;IACtCC,UAAU,EAAEsD,cAAe;IAC3BrD,cAAc,EAAEA,cAAe;IAC/BE,QAAQ,EAAEA,QAAS;IACnBE,cAAc,EAAEA,cAAe;IAC/BC,0BAA0B,EAAEA,0BAA2B;IACvDC,OAAO,EAAEA,OAAQ;IACjBE,MAAM,EAAE,GAAGA,MAAM,MAAO;IACxBC,KAAK,EAAEA;EAAM,CACd,CACG,CAAC;AAEX,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA/B,gBAAgB,CAACkH,QAAQ,GAA6BC,MAKrD,IAAK;EACJ,OAAO,CAAC;IACNjF,KAAK;IACL8C;EAIF,CAAC,kBACC3H,KAAA,CAAA0C,aAAA,CAACJ,cAAc;IACbkD,GAAG,EAAEX,KAAK,CAACW,GAAI;IACfhD,SAAS,EAAEsH,MAAM,CAACjF,KAAK,CAACW,GAAG,GAAGX,KAAK,CAACW,GAAG,GAAG,EAAE,CAAE;IAC9CX,KAAK,EAAEA,KAAM;IACb8C,MAAM,EAAEA;EAAO,CAChB,CACF;AACH,CAAC;;AAED;AACAhF,gBAAgB,CAACoH,GAAG,GAAGC,4BAAmB;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAtJ,OAAA,GAE5B+B,gBAAgB;AAE/B,MAAMmF,MAAM,GAAGsB,uBAAU,CAACe,MAAM,CAAC;EAC/BpC,SAAS,EAAE;IACTqC,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC;EACDrC,OAAO,EAAE;IACPoC,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}