{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_color", "_interopRequireDefault", "_Appbar<PERSON>ontent", "_utils", "_theming", "_Surface", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "Appbar", "children", "dark", "style", "mode", "elevated", "safeAreaInsets", "theme", "themeOverrides", "rest", "useInternalTheme", "isV3", "flattenedStyle", "StyleSheet", "flatten", "backgroundColor", "customBackground", "elevation", "restStyle", "getAppbarBackgroundColor", "isMode", "modeToCompare", "isDark", "color", "isLight", "isV3CenterAlignedMode", "shouldCenterC<PERSON>nt", "shouldAddLeftSpacing", "shouldAddRightSpacing", "Platform", "OS", "has<PERSON><PERSON>bar<PERSON><PERSON>nt", "leftItemsCount", "rightItemsCount", "Children", "for<PERSON>ach", "child", "isValidElement", "isLeading", "props", "type", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spacingStyle", "styles", "v3Spacing", "spacing", "insets", "paddingBottom", "bottom", "paddingTop", "top", "paddingLeft", "left", "paddingRight", "right", "createElement", "appbar", "height", "modeAppbarHeight", "DEFAULT_APPBAR_HEIGHT", "container", "View", "Fragment", "renderAppbarContent", "renderOnly", "filterAppbarActions", "renderExcept", "columnContainer", "centerAlignedContainer", "controlsRow", "rightActionControls", "exports", "create", "flexDirection", "alignItems", "paddingHorizontal", "width", "flex", "justifyContent", "_default"], "sourceRoot": "../../../../src", "sources": ["components/Appbar/Appbar.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAUA,IAAAE,MAAA,GAAAC,sBAAA,CAAAH,OAAA;AAEA,IAAAI,cAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AASA,IAAAM,QAAA,GAAAN,OAAA;AAEA,IAAAO,QAAA,GAAAJ,sBAAA,CAAAH,OAAA;AAAiC,SAAAG,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAT,wBAAAS,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAb,uBAAA,YAAAA,CAAAS,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAf,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAK,CAAA,IAAAF,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAZ,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAa,QAAA,CAAAK,KAAA,OAAAF,SAAA;AA6CjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,MAAM,GAAGA,CAAC;EACdC,QAAQ;EACRC,IAAI;EACJC,KAAK;EACLC,IAAI,GAAG,OAAO;EACdC,QAAQ;EACRC,cAAc;EACdC,KAAK,EAAEC,cAAc;EACrB,GAAGC;AACE,CAAC,KAAK;EACX,MAAMF,KAAK,GAAG,IAAAG,yBAAgB,EAACF,cAAc,CAAC;EAC9C,MAAM;IAAEG;EAAK,CAAC,GAAGJ,KAAK;EACtB,MAAMK,cAAc,GAAGC,uBAAU,CAACC,OAAO,CAACX,KAAK,CAAC;EAChD,MAAM;IACJY,eAAe,EAAEC,gBAAgB;IACjCC,SAAS,GAAGN,IAAI,GAAIN,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAI,CAAC;IACzC,GAAGa;EACL,CAAC,GAAIN,cAAc,IAAI,CAAC,CAGvB;EAED,MAAMG,eAAe,GAAG,IAAAI,+BAAwB,EAC9CZ,KAAK,EACLU,SAAS,EACTD,gBAAgB,EAChBX,QACF,CAAC;EAED,MAAMe,MAAM,GAAIC,aAA0B,IAAK;IAC7C,OAAOV,IAAI,IAAIP,IAAI,KAAKiB,aAAa;EACvC,CAAC;EAED,IAAIC,MAAM,GAAG,KAAK;EAElB,IAAI,OAAOpB,IAAI,KAAK,SAAS,EAAE;IAC7BoB,MAAM,GAAGpB,IAAI;EACf,CAAC,MAAM,IAAI,CAACS,IAAI,EAAE;IAChBW,MAAM,GACJP,eAAe,KAAK,aAAa,GAC7B,KAAK,GACL,OAAOA,eAAe,KAAK,QAAQ,GACnC,CAAC,IAAAQ,cAAK,EAACR,eAAe,CAAC,CAACS,OAAO,CAAC,CAAC,GACjC,IAAI;EACZ;EAEA,MAAMC,qBAAqB,GAAGd,IAAI,IAAIS,MAAM,CAAC,gBAAgB,CAAC;EAE9D,IAAIM,mBAAmB,GAAG,KAAK;EAC/B,IAAIC,oBAAoB,GAAG,KAAK;EAChC,IAAIC,qBAAqB,GAAG,KAAK;EACjC,IAAK,CAACjB,IAAI,IAAIkB,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAKL,qBAAqB,EAAE;IAC7D,IAAIM,gBAAgB,GAAG,KAAK;IAC5B,IAAIC,cAAc,GAAG,CAAC;IACtB,IAAIC,eAAe,GAAG,CAAC;IAEvBpE,KAAK,CAACqE,QAAQ,CAACC,OAAO,CAAClC,QAAQ,EAAGmC,KAAK,IAAK;MAC1C,iBAAIvE,KAAK,CAACwE,cAAc,CAAmBD,KAAK,CAAC,EAAE;QACjD,MAAME,SAAS,GAAGF,KAAK,CAACG,KAAK,CAACD,SAAS,KAAK,IAAI;QAEhD,IAAIF,KAAK,CAACI,IAAI,KAAKC,sBAAa,EAAE;UAChCV,gBAAgB,GAAG,IAAI;QACzB,CAAC,MAAM,IAAIO,SAAS,IAAI,CAACP,gBAAgB,EAAE;UACzCC,cAAc,EAAE;QAClB,CAAC,MAAM;UACLC,eAAe,EAAE;QACnB;MACF;IACF,CAAC,CAAC;IAEFP,mBAAmB,GACjBK,gBAAgB,IAChBC,cAAc,GAAG,CAAC,IAClBC,eAAe,IAAItB,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAClCgB,oBAAoB,GAAGD,mBAAmB,IAAIM,cAAc,KAAK,CAAC;IAClEJ,qBAAqB,GAAGF,mBAAmB,IAAIO,eAAe,KAAK,CAAC;EACtE;EAEA,MAAMS,YAAY,GAAG/B,IAAI,GAAGgC,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACE,OAAO;EAE7D,MAAMC,MAAM,GAAG;IACbC,aAAa,EAAEzC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0C,MAAM;IACrCC,UAAU,EAAE3C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE4C,GAAG;IAC/BC,WAAW,EAAE7C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE8C,IAAI;IACjCC,YAAY,EAAE/C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEgD;EAChC,CAAC;EAED,oBACEzF,KAAA,CAAA0F,aAAA,CAACjF,QAAA,CAAAG,OAAO,EAAAiB,QAAA;IACNS,KAAK,EAAE,CACL;MAAEY;IAAgB,CAAC,EACnB4B,MAAM,CAACa,MAAM,EACb;MACEC,MAAM,EAAE9C,IAAI,GAAG+C,uBAAgB,CAACtD,IAAI,CAAC,GAAGuD;IAC1C,CAAC,EACDb,MAAM,EACN5B,SAAS,EACT,CAACX,KAAK,CAACI,IAAI,IAAI;MAAEM;IAAU,CAAC,CAC5B;IACFA,SAAS,EAAEA,SAA0B;IACrC2C,SAAS;EAAA,GACLnD,IAAI,GAEPkB,oBAAoB,gBAAG9D,KAAA,CAAA0F,aAAA,CAACvF,YAAA,CAAA6F,IAAI;IAAC1D,KAAK,EAAEuC;EAAa,CAAE,CAAC,GAAG,IAAI,EAC3D,CAAC,CAAC/B,IAAI,IAAIS,MAAM,CAAC,OAAO,CAAC,IAAIA,MAAM,CAAC,gBAAgB,CAAC,kBACpDvD,KAAA,CAAA0F,aAAA,CAAA1F,KAAA,CAAAiG,QAAA,QAEG,IAAAC,0BAAmB,EAAC;IACnB9D,QAAQ;IACRqB,MAAM;IACNf,KAAK;IACLI,IAAI;IACJqD,UAAU,EAAE,CAAC,mBAAmB,CAAC;IACjCtC,mBAAmB,EAAED,qBAAqB,IAAIC;EAChD,CAAC,CAAC,EAED,IAAAqC,0BAAmB,EAAC;IACnB;IACA9D,QAAQ,EAAE,CACR,GAAG,IAAAgE,0BAAmB,EAAChE,QAAQ,EAAE,IAAI,CAAC,EACtC,GAAG,IAAAgE,0BAAmB,EAAChE,QAAQ,CAAC,CACjC;IACDqB,MAAM;IACNf,KAAK;IACLI,IAAI;IACJuD,YAAY,EAAE,CAAC,mBAAmB,CAAC;IACnCxC,mBAAmB,EAAED,qBAAqB,IAAIC;EAChD,CAAC,CACD,CACH,EACA,CAACN,MAAM,CAAC,QAAQ,CAAC,IAAIA,MAAM,CAAC,OAAO,CAAC,kBACnCvD,KAAA,CAAA0F,aAAA,CAACvF,YAAA,CAAA6F,IAAI;IACH1D,KAAK,EAAE,CACLwC,MAAM,CAACwB,eAAe,EACtB/C,MAAM,CAAC,gBAAgB,CAAC,IAAIuB,MAAM,CAACyB,sBAAsB;EACzD,gBAGFvG,KAAA,CAAA0F,aAAA,CAACvF,YAAA,CAAA6F,IAAI;IAAC1D,KAAK,EAAEwC,MAAM,CAAC0B;EAAY,GAE7B,IAAAN,0BAAmB,EAAC;IACnB9D,QAAQ;IACRqB,MAAM;IACNX,IAAI;IACJqD,UAAU,EAAE,CAAC,mBAAmB,CAAC;IACjC5D;EACF,CAAC,CAAC,EACD,IAAA2D,0BAAmB,EAAC;IACnB9D,QAAQ,EAAE,IAAAgE,0BAAmB,EAAChE,QAAQ,EAAE,IAAI,CAAC;IAC7CqB,MAAM;IACNX,IAAI;IACJqD,UAAU,EAAE,CAAC,eAAe,CAAC;IAC7B5D;EACF,CAAC,CAAC,eAEFvC,KAAA,CAAA0F,aAAA,CAACvF,YAAA,CAAA6F,IAAI;IAAC1D,KAAK,EAAEwC,MAAM,CAAC2B;EAAoB,GACrC,IAAAP,0BAAmB,EAAC;IACnB9D,QAAQ,EAAE,IAAAgE,0BAAmB,EAAChE,QAAQ,CAAC;IACvCqB,MAAM;IACNX,IAAI;IACJuD,YAAY,EAAE,CACZ,QAAQ,EACR,mBAAmB,EACnB,gBAAgB,EAChB,eAAe,CAChB;IACD9D;EACF,CAAC,CACG,CACF,CAAC,EACN,IAAA2D,0BAAmB,EAAC;IACnB9D,QAAQ;IACRqB,MAAM;IACNX,IAAI;IACJqD,UAAU,EAAE,CAAC,gBAAgB,CAAC;IAC9B5D;EACF,CAAC,CACG,CACP,EACAwB,qBAAqB,gBAAG/D,KAAA,CAAA0F,aAAA,CAACvF,YAAA,CAAA6F,IAAI;IAAC1D,KAAK,EAAEuC;EAAa,CAAE,CAAC,GAAG,IAClD,CAAC;AAEd,CAAC;AAAC6B,OAAA,CAAAvE,MAAA,GAAAA,MAAA;AAEF,MAAM2C,MAAM,GAAG9B,uBAAU,CAAC2D,MAAM,CAAC;EAC/BhB,MAAM,EAAE;IACNiB,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,iBAAiB,EAAE;EACrB,CAAC;EACD9B,OAAO,EAAE;IACP+B,KAAK,EAAE;EACT,CAAC;EACDhC,SAAS,EAAE;IACTgC,KAAK,EAAE;EACT,CAAC;EACDP,WAAW,EAAE;IACXQ,IAAI,EAAE,CAAC;IACPJ,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBI,cAAc,EAAE;EAClB,CAAC;EACDR,mBAAmB,EAAE;IACnBG,aAAa,EAAE,KAAK;IACpBI,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE;EAClB,CAAC;EACDX,eAAe,EAAE;IACfM,aAAa,EAAE,QAAQ;IACvBI,IAAI,EAAE,CAAC;IACP5B,UAAU,EAAE;EACd,CAAC;EACDmB,sBAAsB,EAAE;IACtBnB,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAAC,IAAA8B,QAAA,GAAAR,OAAA,CAAA9F,OAAA,GAEYuB,MAAM,EAErB", "ignoreList": []}