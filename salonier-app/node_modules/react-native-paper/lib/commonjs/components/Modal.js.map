{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_reactNativeSafeAreaContext", "_useLatestCallback", "_interopRequireDefault", "_Surface", "_theming", "_addEventListener", "_<PERSON><PERSON><PERSON><PERSON>", "_useAnimatedValue", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "DEFAULT_DURATION", "AnimatedPressable", "Animated", "createAnimatedComponent", "Pressable", "Modal", "dismissable", "dismissable<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible", "overlayAccessibilityLabel", "on<PERSON><PERSON><PERSON>", "children", "contentContainerStyle", "style", "theme", "themeOverrides", "testID", "_theme$colors", "useInternalTheme", "onDismissCallback", "useLatestCallback", "scale", "animation", "top", "bottom", "useSafeAreaInsets", "opacity", "useAnimatedValue", "visibleInternal", "setVisibleInternal", "useState", "showModalAnimation", "useCallback", "timing", "toValue", "duration", "easing", "Easing", "out", "cubic", "useNativeDriver", "start", "hideModalAnimation", "finished", "useEffect", "undefined", "onHardwareBackPress", "subscription", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "remove", "createElement", "View", "pointerEvents", "accessibilityViewIsModal", "accessibilityLiveRegion", "StyleSheet", "absoluteFill", "onAccessibilityEscape", "accessibilityLabel", "accessibilityRole", "disabled", "onPress", "importantForAccessibility", "styles", "backdrop", "backgroundColor", "colors", "wrapper", "marginTop", "marginBottom", "content", "container", "_default", "exports", "create", "flex", "absoluteFillObject", "justifyContent"], "sourceRoot": "../../../src", "sources": ["components/Modal.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAUA,IAAAE,2BAAA,GAAAF,OAAA;AACA,IAAAG,kBAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAEA,IAAAK,QAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,QAAA,GAAAN,OAAA;AAEA,IAAAO,iBAAA,GAAAP,OAAA;AACA,IAAAQ,YAAA,GAAAR,OAAA;AACA,IAAAS,iBAAA,GAAAL,sBAAA,CAAAJ,OAAA;AAAyD,SAAAI,uBAAAM,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAX,wBAAAW,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAf,uBAAA,YAAAA,CAAAW,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AA8CzD,MAAMgB,gBAAgB,GAAG,GAAG;AAC5B,MAAMC,iBAAiB,GAAGC,qBAAQ,CAACC,uBAAuB,CAACC,sBAAS,CAAC;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAAC;EACbC,WAAW,GAAG,IAAI;EAClBC,qBAAqB,GAAGD,WAAW;EACnCE,OAAO,GAAG,KAAK;EACfC,yBAAyB,GAAG,aAAa;EACzCC,SAAS,GAAGA,CAAA,KAAM,CAAC,CAAC;EACpBC,QAAQ;EACRC,qBAAqB;EACrBC,KAAK;EACLC,KAAK,EAAEC,cAAc;EACrBC,MAAM,GAAG;AACJ,CAAC,EAAE;EAAA,IAAAC,aAAA;EACR,MAAMH,KAAK,GAAG,IAAAI,yBAAgB,EAACH,cAAc,CAAC;EAC9C,MAAMI,iBAAiB,GAAG,IAAAC,0BAAiB,EAACV,SAAS,CAAC;EACtD,MAAM;IAAEW;EAAM,CAAC,GAAGP,KAAK,CAACQ,SAAS;EACjC,MAAM;IAAEC,GAAG;IAAEC;EAAO,CAAC,GAAG,IAAAC,6CAAiB,EAAC,CAAC;EAC3C,MAAMC,OAAO,GAAG,IAAAC,yBAAgB,EAACnB,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;EACjD,MAAM,CAACoB,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,KAAK,CAAC6D,QAAQ,CAACtB,OAAO,CAAC;EAErE,MAAMuB,kBAAkB,GAAG9D,KAAK,CAAC+D,WAAW,CAAC,MAAM;IACjD9B,qBAAQ,CAAC+B,MAAM,CAACP,OAAO,EAAE;MACvBQ,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAEd,KAAK,GAAGrB,gBAAgB;MAClCoC,MAAM,EAAEC,mBAAM,CAACC,GAAG,CAACD,mBAAM,CAACE,KAAK,CAAC;MAChCC,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAACf,OAAO,EAAEL,KAAK,CAAC,CAAC;EAEpB,MAAMqB,kBAAkB,GAAGzE,KAAK,CAAC+D,WAAW,CAAC,MAAM;IACjD9B,qBAAQ,CAAC+B,MAAM,CAACP,OAAO,EAAE;MACvBQ,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAEd,KAAK,GAAGrB,gBAAgB;MAClCoC,MAAM,EAAEC,mBAAM,CAACC,GAAG,CAACD,mBAAM,CAACE,KAAK,CAAC;MAChCC,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;MAAEE;IAAS,CAAC,KAAK;MACzB,IAAI,CAACA,QAAQ,EAAE;QACb;MACF;MAEAd,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACH,OAAO,EAAEL,KAAK,CAAC,CAAC;EAEpBpD,KAAK,CAAC2E,SAAS,CAAC,MAAM;IACpB,IAAIhB,eAAe,KAAKpB,OAAO,EAAE;MAC/B;IACF;IAEA,IAAI,CAACoB,eAAe,IAAIpB,OAAO,EAAE;MAC/BqB,kBAAkB,CAAC,IAAI,CAAC;MACxB,OAAOE,kBAAkB,CAAC,CAAC;IAC7B;IAEA,IAAIH,eAAe,IAAI,CAACpB,OAAO,EAAE;MAC/B,OAAOkC,kBAAkB,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,CAAClC,OAAO,EAAEuB,kBAAkB,EAAEW,kBAAkB,EAAEd,eAAe,CAAC,CAAC;EAEtE3D,KAAK,CAAC2E,SAAS,CAAC,MAAM;IACpB,IAAI,CAACpC,OAAO,EAAE;MACZ,OAAOqC,SAAS;IAClB;IAEA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;MAChC,IAAIxC,WAAW,IAAIC,qBAAqB,EAAE;QACxCY,iBAAiB,CAAC,CAAC;MACrB;MAEA,OAAO,IAAI;IACb,CAAC;IAED,MAAM4B,YAAY,GAAG,IAAAC,kCAAgB,EACnCC,wBAAW,EACX,mBAAmB,EACnBH,mBACF,CAAC;IACD,OAAO,MAAMC,YAAY,CAACG,MAAM,CAAC,CAAC;EACpC,CAAC,EAAE,CAAC5C,WAAW,EAAEC,qBAAqB,EAAEY,iBAAiB,EAAEX,OAAO,CAAC,CAAC;EAEpE,IAAI,CAACoB,eAAe,EAAE;IACpB,OAAO,IAAI;EACb;EAEA,oBACE3D,KAAA,CAAAkF,aAAA,CAAC/E,YAAA,CAAA8B,QAAQ,CAACkD,IAAI;IACZC,aAAa,EAAE7C,OAAO,GAAG,MAAM,GAAG,MAAO;IACzC8C,wBAAwB;IACxBC,uBAAuB,EAAC,QAAQ;IAChC1C,KAAK,EAAE2C,uBAAU,CAACC,YAAa;IAC/BC,qBAAqB,EAAEvC,iBAAkB;IACzCH,MAAM,EAAEA;EAAO,gBAEf/C,KAAA,CAAAkF,aAAA,CAAClD,iBAAiB;IAChB0D,kBAAkB,EAAElD,yBAA0B;IAC9CmD,iBAAiB,EAAC,QAAQ;IAC1BC,QAAQ,EAAE,CAACvD,WAAY;IACvBwD,OAAO,EAAExD,WAAW,GAAGa,iBAAiB,GAAG0B,SAAU;IACrDkB,yBAAyB,EAAC,IAAI;IAC9BlD,KAAK,EAAE,CACLmD,MAAM,CAACC,QAAQ,EACf;MACEC,eAAe,GAAAjD,aAAA,GAAEH,KAAK,CAACqD,MAAM,cAAAlD,aAAA,uBAAZA,aAAA,CAAcgD,QAAQ;MACvCvC;IACF,CAAC,CACD;IACFV,MAAM,EAAE,GAAGA,MAAM;EAAY,CAC9B,CAAC,eACF/C,KAAA,CAAAkF,aAAA,CAAC/E,YAAA,CAAAgF,IAAI;IACHvC,KAAK,EAAE,CACLmD,MAAM,CAACI,OAAO,EACd;MAAEC,SAAS,EAAE9C,GAAG;MAAE+C,YAAY,EAAE9C;IAAO,CAAC,EACxCX,KAAK,CACL;IACFwC,aAAa,EAAC,UAAU;IACxBrC,MAAM,EAAE,GAAGA,MAAM;EAAW,gBAE5B/C,KAAA,CAAAkF,aAAA,CAAC3E,QAAA,CAAAO,OAAO;IACNiC,MAAM,EAAE,GAAGA,MAAM,UAAW;IAC5BF,KAAK,EAAEA,KAAM;IACbD,KAAK,EAAE,CAAC;MAAEa;IAAQ,CAAC,EAAEsC,MAAM,CAACO,OAAO,EAAE3D,qBAAqB,CAAE;IAC5D4D,SAAS;EAAA,GAER7D,QACM,CACL,CACO,CAAC;AAEpB;AAAC,IAAA8D,QAAA,GAAAC,OAAA,CAAA3F,OAAA,GAEcsB,KAAK;AAEpB,MAAM2D,MAAM,GAAGR,uBAAU,CAACmB,MAAM,CAAC;EAC/BV,QAAQ,EAAE;IACRW,IAAI,EAAE;EACR,CAAC;EACDR,OAAO,EAAE;IACP,GAAGZ,uBAAU,CAACqB,kBAAkB;IAChCC,cAAc,EAAE;EAClB,CAAC;EACD;EACAP,OAAO,EAAE;IACPL,eAAe,EAAE,aAAa;IAC9BY,cAAc,EAAE;EAClB;AACF,CAAC,CAAC", "ignoreList": []}