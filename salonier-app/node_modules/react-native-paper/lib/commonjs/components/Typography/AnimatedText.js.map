{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_theming", "_forwardRef", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "AnimatedText", "forwardRef", "style", "theme", "themeOverrides", "variant", "rest", "ref", "useInternalTheme", "writingDirection", "I18nManager", "getConstants", "isRTL", "isV3", "font", "fonts", "Error", "keys", "join", "createElement", "Animated", "Text", "styles", "text", "color", "colors", "onSurface", "regular", "bodyMedium", "textStyle", "StyleSheet", "create", "textAlign", "customAnimatedText", "exports", "_default"], "sourceRoot": "../../../../src", "sources": ["components/Typography/AnimatedText.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAUA,IAAAE,QAAA,GAAAF,OAAA;AAEA,IAAAG,WAAA,GAAAH,OAAA;AAAoD,SAAAD,wBAAAK,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAP,uBAAA,YAAAA,CAAAK,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAkB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAjB,CAAA,aAAAJ,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAC,CAAA,GAAAqB,SAAA,CAAAtB,CAAA,YAAAG,CAAA,IAAAF,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAd,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAe,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAyBpD;AACA;AACA;AACA;AACA;AACA,MAAMG,YAAY,GAAG,IAAAC,sBAAU,EAC7B,SAASD,YAAYA,CACnB;EAAEE,KAAK;EAAEC,KAAK,EAAEC,cAAc;EAAEC,OAAO;EAAE,GAAGC;AAAK,CAAC,EAClDC,GAAG,EACH;EACA,MAAMJ,KAAK,GAAG,IAAAK,yBAAgB,EAACJ,cAAc,CAAC;EAC9C,MAAMK,gBAAgB,GAAGC,wBAAW,CAACC,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;EAEzE,IAAIT,KAAK,CAACU,IAAI,IAAIR,OAAO,EAAE;IACzB,MAAMS,IAAI,GAAGX,KAAK,CAACY,KAAK,CAACV,OAAO,CAAC;IACjC,IAAI,OAAOS,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,IAAIE,KAAK,CACb,WAAWX,OAAO,kDAAkDd,MAAM,CAAC0B,IAAI,CAC7Ed,KAAK,CAACY,KACR,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC,GACd,CAAC;IACH;IAEA,oBACEjD,KAAA,CAAAkD,aAAA,CAAC/C,YAAA,CAAAgD,QAAQ,CAACC,IAAI,EAAA3B,QAAA;MACZa,GAAG,EAAEA;IAAI,GACLD,IAAI;MACRJ,KAAK,EAAE,CACLY,IAAI,EACJQ,MAAM,CAACC,IAAI,EACX;QAAEd,gBAAgB;QAAEe,KAAK,EAAErB,KAAK,CAACsB,MAAM,CAACC;MAAU,CAAC,EACnDxB,KAAK;IACL,EACH,CAAC;EAEN,CAAC,MAAM;IACL,MAAMY,IAAI,GAAG,CAACX,KAAK,CAACU,IAAI,GAAGV,KAAK,CAACY,KAAK,CAACY,OAAO,GAAGxB,KAAK,CAACY,KAAK,CAACa,UAAU;IACvE,MAAMC,SAAS,GAAG;MAChB,GAAGf,IAAI;MACPU,KAAK,EAAErB,KAAK,CAACU,IAAI,GAAGV,KAAK,CAACsB,MAAM,CAACC,SAAS,GAAGvB,KAAK,CAACsB,MAAM,CAACF;IAC5D,CAAC;IACD,oBACEtD,KAAA,CAAAkD,aAAA,CAAC/C,YAAA,CAAAgD,QAAQ,CAACC,IAAI,EAAA3B,QAAA;MACZa,GAAG,EAAEA;IAAI,GACLD,IAAI;MACRJ,KAAK,EAAE,CACLoB,MAAM,CAACC,IAAI,EACXM,SAAS,EACT;QACEpB;MACF,CAAC,EACDP,KAAK;IACL,EACH,CAAC;EAEN;AACF,CACF,CAAC;AAED,MAAMoB,MAAM,GAAGQ,uBAAU,CAACC,MAAM,CAAC;EAC/BR,IAAI,EAAE;IACJS,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEK,MAAMC,kBAAkB,GAAGA,CAAA,KAChCjC,YAAgD;AAACkC,OAAA,CAAAD,kBAAA,GAAAA,kBAAA;AAAA,IAAAE,QAAA,GAAAD,OAAA,CAAAjD,OAAA,GAEpCe,YAAY", "ignoreList": []}