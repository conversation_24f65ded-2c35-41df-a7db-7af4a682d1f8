{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_AnimatedText", "_interopRequireDefault", "_StyledText", "_theming", "_forwardRef", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "Text", "style", "variant", "theme", "initialTheme", "rest", "ref", "root", "useRef", "useInternalTheme", "writingDirection", "I18nManager", "getConstants", "isRTL", "useImperativeHandle", "setNativeProps", "args", "_root$current", "current", "isV3", "font", "fonts", "textStyle", "isValidElement", "children", "type", "Component", "AnimatedText", "StyledText", "props", "Error", "keys", "join", "createElement", "styles", "text", "color", "colors", "onSurface", "_theme$fonts", "_theme$colors", "regular", "StyleSheet", "create", "textAlign", "forwardRef", "customText", "exports", "_default"], "sourceRoot": "../../../../src", "sources": ["components/Typography/Text.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAQA,IAAAE,aAAA,GAAAC,sBAAA,CAAAH,OAAA;AAEA,IAAAI,WAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,QAAA,GAAAL,OAAA;AAEA,IAAAM,WAAA,GAAAN,OAAA;AAAoD,SAAAG,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAZ,uBAAA,YAAAA,CAAAQ,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAf,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAK,CAAA,IAAAF,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAZ,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAa,QAAA,CAAAK,KAAA,OAAAF,SAAA;AA6BpD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,IAAI,GAAGA,CACX;EAAEC,KAAK;EAAEC,OAAO;EAAEC,KAAK,EAAEC,YAAY;EAAE,GAAGC;AAAoB,CAAC,EAC/DC,GAAY,KACT;EACH,MAAMC,IAAI,GAAGzC,KAAK,CAAC0C,MAAM,CAAoB,IAAI,CAAC;EAClD;EACA,MAAML,KAAK,GAAG,IAAAM,yBAAgB,EAACL,YAAY,CAAC;EAC5C,MAAMM,gBAAgB,GAAGC,wBAAW,CAACC,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;EAEzE/C,KAAK,CAACgD,mBAAmB,CAACR,GAAG,EAAE,OAAO;IACpCS,cAAc,EAAGC,IAAY;MAAA,IAAAC,aAAA;MAAA,QAAAA,aAAA,GAAKV,IAAI,CAACW,OAAO,cAAAD,aAAA,uBAAZA,aAAA,CAAcF,cAAc,CAACC,IAAI,CAAC;IAAA;EACtE,CAAC,CAAC,CAAC;EAEH,IAAIb,KAAK,CAACgB,IAAI,IAAIjB,OAAO,EAAE;IACzB,IAAIkB,IAAI,GAAGjB,KAAK,CAACkB,KAAK,CAACnB,OAAO,CAAC;IAC/B,IAAIoB,SAAS,GAAG,CAACF,IAAI,EAAEnB,KAAK,CAAC;IAE7B,IACE,aAAAnC,KAAK,CAACyD,cAAc,CAAClB,IAAI,CAACmB,QAAQ,CAAC,KAClCnB,IAAI,CAACmB,QAAQ,CAACC,IAAI,KAAKC,SAAS,IAC/BrB,IAAI,CAACmB,QAAQ,CAACC,IAAI,KAAKE,qBAAY,IACnCtB,IAAI,CAACmB,QAAQ,CAACC,IAAI,KAAKG,mBAAU,CAAC,EACpC;MACA,MAAM;QAAEC;MAAM,CAAC,GAAGxB,IAAI,CAACmB,QAEtB;;MAED;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIK,KAAK,CAAC3B,OAAO,EAAE;QACjBkB,IAAI,GAAGjB,KAAK,CAACkB,KAAK,CAACQ,KAAK,CAAC3B,OAAO,CAAsC;QACtEoB,SAAS,GAAG,CAACrB,KAAK,EAAEmB,IAAI,CAAC;MAC3B;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACS,KAAK,CAAC3B,OAAO,EAAE;QAClBoB,SAAS,GAAG,CAACrB,KAAK,EAAE4B,KAAK,CAAC5B,KAAK,CAAC;MAClC;IACF;IAEA,IAAI,OAAOmB,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,IAAIU,KAAK,CACb,WAAW5B,OAAO,kDAAkDX,MAAM,CAACwC,IAAI,CAC7E5B,KAAK,CAACkB,KACR,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC,GACd,CAAC;IACH;IAEA,oBACElE,KAAA,CAAAmE,aAAA,CAAChE,YAAA,CAAA+B,IAAU,EAAAN,QAAA;MACTY,GAAG,EAAEC,IAAK;MACVN,KAAK,EAAE,CACLiC,MAAM,CAACC,IAAI,EACX;QAAEzB,gBAAgB;QAAE0B,KAAK,EAAEjC,KAAK,CAACkC,MAAM,CAACC;MAAU,CAAC,EACnDhB,SAAS;IACT,GACEjB,IAAI,CACT,CAAC;EAEN,CAAC,MAAM;IAAA,IAAAkC,YAAA,EAAAC,aAAA;IACL,MAAMpB,IAAI,GAAGjB,KAAK,CAACgB,IAAI,GAAGhB,KAAK,CAACkB,KAAK,CAAC5C,OAAO,IAAA8D,YAAA,GAAGpC,KAAK,CAACkB,KAAK,cAAAkB,YAAA,uBAAXA,YAAA,CAAaE,OAAO;IACpE,MAAMnB,SAAS,GAAG;MAChB,GAAGF,IAAI;MACPgB,KAAK,EAAEjC,KAAK,CAACgB,IAAI,IAAAqB,aAAA,GAAGrC,KAAK,CAACkC,MAAM,cAAAG,aAAA,uBAAZA,aAAA,CAAcF,SAAS,GAAGnC,KAAK,CAACkC,MAAM,CAACF;IAC7D,CAAC;IACD,oBACErE,KAAA,CAAAmE,aAAA,CAAChE,YAAA,CAAA+B,IAAU,EAAAN,QAAA,KACLW,IAAI;MACRC,GAAG,EAAEC,IAAK;MACVN,KAAK,EAAE,CAACiC,MAAM,CAACC,IAAI,EAAEb,SAAS,EAAE;QAAEZ;MAAiB,CAAC,EAAET,KAAK;IAAE,EAC9D,CAAC;EAEN;AACF,CAAC;AAED,MAAMiC,MAAM,GAAGQ,uBAAU,CAACC,MAAM,CAAC;EAC/BR,IAAI,EAAE;IACJS,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAMF,MAAMlB,SAAS,GAAG,IAAAmB,sBAAU,EAAC7C,IAAI,CAAyB;AAEnD,MAAM8C,UAAU,GAAGA,CAAA,KAAUpB,SAAwC;AAACqB,OAAA,CAAAD,UAAA,GAAAA,UAAA;AAAA,IAAAE,QAAA,GAAAD,OAAA,CAAAtE,OAAA,GAE9DiD,SAAS", "ignoreList": []}