{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_Icon", "_theming", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "CrossFadeIcon", "color", "size", "source", "theme", "themeOverrides", "testID", "useInternalTheme", "currentIcon", "setCurrentIcon", "useState", "previousIcon", "setPreviousIcon", "current", "fade", "useRef", "Animated", "Value", "scale", "animation", "useEffect", "isValidIcon", "isEqualIcon", "setValue", "timing", "duration", "toValue", "useNativeDriver", "start", "opacityPrev", "opacityNext", "interpolate", "inputRange", "outputRange", "rotatePrev", "rotateNext", "createElement", "View", "style", "styles", "content", "height", "width", "icon", "opacity", "transform", "rotate", "_default", "exports", "StyleSheet", "create", "alignItems", "justifyContent", "position", "top", "left", "right", "bottom"], "sourceRoot": "../../../src", "sources": ["components/CrossFadeIcon.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,KAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAAmD,SAAAD,wBAAAK,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAP,uBAAA,YAAAA,CAAAK,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AA0BnD,MAAMkB,aAAa,GAAGA,CAAC;EACrBC,KAAK;EACLC,IAAI;EACJC,MAAM;EACNC,KAAK,EAAEC,cAAc;EACrBC,MAAM,GAAG;AACJ,CAAC,KAAK;EACX,MAAMF,KAAK,GAAG,IAAAG,yBAAgB,EAACF,cAAc,CAAC;EAC9C,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAGlC,KAAK,CAACmC,QAAQ,CAClD,MAAMP,MACR,CAAC;EACD,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAGrC,KAAK,CAACmC,QAAQ,CACpD,IACF,CAAC;EACD,MAAM;IAAEG,OAAO,EAAEC;EAAK,CAAC,GAAGvC,KAAK,CAACwC,MAAM,CAAiB,IAAIC,qBAAQ,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EAE7E,MAAM;IAAEC;EAAM,CAAC,GAAGd,KAAK,CAACe,SAAS;EAEjC,IAAIX,WAAW,KAAKL,MAAM,EAAE;IAC1BS,eAAe,CAAC,MAAMJ,WAAW,CAAC;IAClCC,cAAc,CAAC,MAAMN,MAAM,CAAC;EAC9B;EAEA5B,KAAK,CAAC6C,SAAS,CAAC,MAAM;IACpB,IAAI,IAAAC,iBAAW,EAACV,YAAY,CAAC,IAAI,CAAC,IAAAW,iBAAW,EAACX,YAAY,EAAEH,WAAW,CAAC,EAAE;MACxEM,IAAI,CAACS,QAAQ,CAAC,CAAC,CAAC;MAEhBP,qBAAQ,CAACQ,MAAM,CAACV,IAAI,EAAE;QACpBW,QAAQ,EAAEP,KAAK,GAAG,GAAG;QACrBQ,OAAO,EAAE,CAAC;QACVC,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACpB,WAAW,EAAEG,YAAY,EAAEG,IAAI,EAAEI,KAAK,CAAC,CAAC;EAE5C,MAAMW,WAAW,GAAGf,IAAI;EACxB,MAAMgB,WAAW,GAAGnB,YAAY,GAC5BG,IAAI,CAACiB,WAAW,CAAC;IACfC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;EACpB,CAAC,CAAC,GACF,CAAC;EAEL,MAAMC,UAAU,GAAGpB,IAAI,CAACiB,WAAW,CAAC;IAClCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,QAAQ,EAAE,MAAM;EAChC,CAAC,CAAC;EAEF,MAAME,UAAU,GAAGxB,YAAY,GAC3BG,IAAI,CAACiB,WAAW,CAAC;IACfC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,MAAM,EAAE,SAAS;EACjC,CAAC,CAAC,GACF,MAAM;EAEV,oBACE1D,KAAA,CAAA6D,aAAA,CAAC1D,YAAA,CAAA2D,IAAI;IACHC,KAAK,EAAE,CACLC,MAAM,CAACC,OAAO,EACd;MACEC,MAAM,EAAEvC,IAAI;MACZwC,KAAK,EAAExC;IACT,CAAC;EACD,GAEDS,YAAY,gBACXpC,KAAA,CAAA6D,aAAA,CAAC1D,YAAA,CAAAsC,QAAQ,CAACqB,IAAI;IACZC,KAAK,EAAE,CACLC,MAAM,CAACI,IAAI,EACX;MACEC,OAAO,EAAEf,WAAW;MACpBgB,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAEZ;MAAW,CAAC;IACpC,CAAC,CACD;IACF5B,MAAM,EAAE,GAAGA,MAAM;EAAY,gBAE7B/B,KAAA,CAAA6D,aAAA,CAACzD,KAAA,CAAAY,OAAI;IAACY,MAAM,EAAEQ,YAAa;IAACT,IAAI,EAAEA,IAAK;IAACD,KAAK,EAAEA,KAAM;IAACG,KAAK,EAAEA;EAAM,CAAE,CACxD,CAAC,GACd,IAAI,eACR7B,KAAA,CAAA6D,aAAA,CAAC1D,YAAA,CAAAsC,QAAQ,CAACqB,IAAI;IACZC,KAAK,EAAE,CACLC,MAAM,CAACI,IAAI,EACX;MACEC,OAAO,EAAEd,WAAW;MACpBe,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAEX;MAAW,CAAC;IACpC,CAAC,CACD;IACF7B,MAAM,EAAE,GAAGA,MAAM;EAAW,gBAE5B/B,KAAA,CAAA6D,aAAA,CAACzD,KAAA,CAAAY,OAAI;IAACY,MAAM,EAAEK,WAAY;IAACN,IAAI,EAAEA,IAAK;IAACD,KAAK,EAAEA,KAAM;IAACG,KAAK,EAAEA;EAAM,CAAE,CACvD,CACX,CAAC;AAEX,CAAC;AAAC,IAAA2C,QAAA,GAAAC,OAAA,CAAAzD,OAAA,GAEaS,aAAa;AAE5B,MAAMuC,MAAM,GAAGU,uBAAU,CAACC,MAAM,CAAC;EAC/BV,OAAO,EAAE;IACPW,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDT,IAAI,EAAE;IACJU,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV;AACF,CAAC,CAAC", "ignoreList": []}