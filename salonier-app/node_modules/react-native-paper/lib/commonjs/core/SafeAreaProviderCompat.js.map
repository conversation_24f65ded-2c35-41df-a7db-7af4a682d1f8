{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_reactNativeSafeAreaContext", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "width", "height", "Dimensions", "initialMetrics", "Platform", "OS", "initialWindowMetrics", "frame", "x", "y", "insets", "top", "left", "right", "bottom", "SafeAreaProviderCompat", "children", "style", "createElement", "SafeAreaInsetsContext", "Consumer", "View", "styles", "container", "SafeAreaProvider", "StyleSheet", "create", "flex"], "sourceRoot": "../../../src", "sources": ["core/SafeAreaProviderCompat.tsx"], "mappings": ";;;;;;AAGA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AASA,IAAAE,2BAAA,GAAAF,OAAA;AAIwC,SAAAD,wBAAAI,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAN,uBAAA,YAAAA,CAAAI,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAjBxC;AACA;AACA;;AAsBA,MAAM;EAAEkB,KAAK,GAAG,CAAC;EAAEC,MAAM,GAAG;AAAE,CAAC,GAAGC,uBAAU,CAACT,GAAG,CAAC,QAAQ,CAAC;;AAE1D;AACA;AACA;AACA,MAAMU,cAAc,GAClBC,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAIC,gDAAoB,IAAI,IAAI,GACjD;EACEC,KAAK,EAAE;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAET,KAAK;IAAEC;EAAO,CAAC;EACpCS,MAAM,EAAE;IAAEC,GAAG,EAAE,CAAC;IAAEC,IAAI,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE;AACjD,CAAC,GACDR,gDAAoB;AAEX,SAASS,sBAAsBA,CAAC;EAAEC,QAAQ;EAAEC;AAAa,CAAC,EAAE;EACzE,oBACEzC,KAAA,CAAA0C,aAAA,CAACtC,2BAAA,CAAAuC,qBAAqB,CAACC,QAAQ,QAC3BV,MAAM,IAAK;IACX,IAAIA,MAAM,EAAE;MACV;MACA;MACA;MACA,oBAAOlC,KAAA,CAAA0C,aAAA,CAACvC,YAAA,CAAA0C,IAAI;QAACJ,KAAK,EAAE,CAACK,MAAM,CAACC,SAAS,EAAEN,KAAK;MAAE,GAAED,QAAe,CAAC;IAClE;IAEA,oBACExC,KAAA,CAAA0C,aAAA,CAACtC,2BAAA,CAAA4C,gBAAgB;MAACrB,cAAc,EAAEA,cAAe;MAACc,KAAK,EAAEA;IAAM,GAC5DD,QACe,CAAC;EAEvB,CAC8B,CAAC;AAErC;AAEAD,sBAAsB,CAACZ,cAAc,GAAGA,cAAc;AAEtD,MAAMmB,MAAM,GAAGG,uBAAU,CAACC,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}