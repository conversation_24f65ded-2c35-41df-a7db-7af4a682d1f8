{"version": 3, "names": ["_reactNative", "require", "_useLazyRef", "_interopRequireDefault", "e", "__esModule", "default", "useAnimatedValue", "initialValue", "current", "useLazyRef", "Animated", "Value"], "sourceRoot": "../../../src", "sources": ["utils/useAnimatedValue.tsx"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAsC,SAAAE,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEvB,SAASG,gBAAgBA,CAACC,YAAoB,EAAE;EAC7D,MAAM;IAAEC;EAAQ,CAAC,GAAG,IAAAC,mBAAU,EAAC,MAAM,IAAIC,qBAAQ,CAACC,KAAK,CAACJ,YAAY,CAAC,CAAC;EAEtE,OAAOC,OAAO;AAChB", "ignoreList": []}