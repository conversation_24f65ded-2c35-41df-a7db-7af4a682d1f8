{"version": 3, "names": ["React", "I18nManager", "Platform", "StyleSheet", "CommonActions", "Link", "useLinkBuilder", "BottomNavigation", "MaterialCommunityIcon", "MaterialBottomTabView", "state", "navigation", "descriptors", "rest", "buildLink", "createElement", "_extends", "onIndexChange", "noop", "navigationState", "renderScene", "route", "key", "render", "renderTouchable", "OS", "onPress", "accessibilityRole", "_0", "borderless", "_1", "centered", "_2", "rippleColor", "_3", "style", "to", "name", "params", "e", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "button", "preventDefault", "styles", "touchable", "undefined", "renderIcon", "focused", "color", "options", "tabBarIcon", "direction", "getConstants", "isRTL", "size", "getLabelText", "tabBarLabel", "title", "getColor", "tabBarColor", "getBadge", "tabBarBadge", "getAccessibilityLabel", "tabBarAccessibilityLabel", "getTestID", "tabBarButtonTestID", "onTabPress", "event", "emit", "type", "target", "canPreventDefault", "defaultPrevented", "dispatch", "navigate", "onTabLongPress", "create", "display", "justifyContent"], "sourceRoot": "../../../../src", "sources": ["react-navigation/views/MaterialBottomTabView.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,cAAc;AAEhE,SACEC,aAAa,EACbC,IAAI,EAIJC,cAAc,QACT,0BAA0B;AAEjC,OAAOC,gBAAgB,MAAM,oDAAoD;AACjF,OAAOC,qBAAqB,MAAM,wCAAwC;AAY1E,eAAe,SAASC,qBAAqBA,CAAC;EAC5CC,KAAK;EACLC,UAAU;EACVC,WAAW;EACX,GAAGC;AACE,CAAC,EAAE;EACR,MAAMC,SAAS,GAAGR,cAAc,CAAC,CAAC;EAElC,oBACEN,KAAA,CAAAe,aAAA,CAACR,gBAAgB,EAAAS,QAAA,KACXH,IAAI;IACRI,aAAa,EAAEC,IAAK;IACpBC,eAAe,EAAET,KAAM;IACvBU,WAAW,EAAEA,CAAC;MAAEC;IAAM,CAAC,KAAKT,WAAW,CAACS,KAAK,CAACC,GAAG,CAAC,CAACC,MAAM,CAAC,CAAE;IAC5DC,eAAe,EACbtB,QAAQ,CAACuB,EAAE,KAAK,KAAK,GACjB,CAAC;MACCC,OAAO;MACPL,KAAK;MACLM,iBAAiB,EAAEC,EAAE;MACrBC,UAAU,EAAEC,EAAE;MACdC,QAAQ,EAAEC,EAAE;MACZC,WAAW,EAAEC,EAAE;MACfC,KAAK;MACL,GAAGtB;IACL,CAAC,KAAK;MACJ,oBACEb,KAAA,CAAAe,aAAA,CAACV,IAAI,EAAAW,QAAA,KACCH,IAAI;QACR;QACAuB,EAAE,EAAEtB,SAAS,CAACO,KAAK,CAACgB,IAAI,EAAEhB,KAAK,CAACiB,MAAM,CAAE;QACxCX,iBAAiB,EAAC,MAAM;QACxBD,OAAO,EAAGa,CAAM,IAAK;UACnB,IACE,EAAEA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,MAAM,IAAIF,CAAC,CAACG,OAAO,IAAIH,CAAC,CAACI,QAAQ,CAAC;UAAI;UACtDJ,CAAC,CAACK,MAAM,IAAI,IAAI,IAAIL,CAAC,CAACK,MAAM,KAAK,CAAC,CAAC,CAAC;UAAA,EACrC;YACAL,CAAC,CAACM,cAAc,CAAC,CAAC;YAClBnB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAGa,CAAC,CAAC;UACd;QACF,CAAE;QACFJ,KAAK,EAAE,CAACW,MAAM,CAACC,SAAS,EAAEZ,KAAK;MAAE,EAClC,CAAC;IAEN,CAAC,GACDa,SACL;IACDC,UAAU,EAAEA,CAAC;MAAE5B,KAAK;MAAE6B,OAAO;MAAEC;IAAM,CAAC,KAAK;MACzC,MAAM;QAAEC;MAAQ,CAAC,GAAGxC,WAAW,CAACS,KAAK,CAACC,GAAG,CAAC;MAE1C,IAAI,OAAO8B,OAAO,CAACC,UAAU,KAAK,QAAQ,EAAE;QAC1C,oBACErD,KAAA,CAAAe,aAAA,CAACP,qBAAqB;UACpB8C,SAAS,EAAErD,WAAW,CAACsD,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG,KAAM;UAC5DnB,IAAI,EAAEe,OAAO,CAACC,UAAW;UACzBF,KAAK,EAAEA,KAAM;UACbM,IAAI,EAAE;QAAG,CACV,CAAC;MAEN;MAEA,IAAI,OAAOL,OAAO,CAACC,UAAU,KAAK,UAAU,EAAE;QAC5C,OAAOD,OAAO,CAACC,UAAU,CAAC;UAAEH,OAAO;UAAEC;QAAM,CAAC,CAAC;MAC/C;MAEA,OAAO,IAAI;IACb,CAAE;IACFO,YAAY,EAAEA,CAAC;MAAErC;IAAM,CAAC,KAAK;MAC3B,MAAM;QAAE+B;MAAQ,CAAC,GAAGxC,WAAW,CAACS,KAAK,CAACC,GAAG,CAAC;MAE1C,OAAO8B,OAAO,CAACO,WAAW,KAAKX,SAAS,GACpCI,OAAO,CAACO,WAAW,GACnBP,OAAO,CAACQ,KAAK,KAAKZ,SAAS,GAC3BI,OAAO,CAACQ,KAAK,GACZvC,KAAK,CAAmBgB,IAAI;IACnC,CAAE;IACFwB,QAAQ,EAAEA,CAAC;MAAExC;IAAM,CAAC,KAAKT,WAAW,CAACS,KAAK,CAACC,GAAG,CAAC,CAAC8B,OAAO,CAACU,WAAY;IACpEC,QAAQ,EAAEA,CAAC;MAAE1C;IAAM,CAAC,KAAKT,WAAW,CAACS,KAAK,CAACC,GAAG,CAAC,CAAC8B,OAAO,CAACY,WAAY;IACpEC,qBAAqB,EAAEA,CAAC;MAAE5C;IAAM,CAAC,KAC/BT,WAAW,CAACS,KAAK,CAACC,GAAG,CAAC,CAAC8B,OAAO,CAACc,wBAChC;IACDC,SAAS,EAAEA,CAAC;MAAE9C;IAAM,CAAC,KACnBT,WAAW,CAACS,KAAK,CAACC,GAAG,CAAC,CAAC8B,OAAO,CAACgB,kBAChC;IACDC,UAAU,EAAEA,CAAC;MAAEhD,KAAK;MAAEwB;IAAe,CAAC,KAAK;MACzC,MAAMyB,KAAK,GAAG3D,UAAU,CAAC4D,IAAI,CAAC;QAC5BC,IAAI,EAAE,UAAU;QAChBC,MAAM,EAAEpD,KAAK,CAACC,GAAG;QACjBoD,iBAAiB,EAAE;MACrB,CAAC,CAAC;MAEF,IAAIJ,KAAK,CAACK,gBAAgB,EAAE;QAC1B9B,cAAc,CAAC,CAAC;MAClB,CAAC,MAAM;QACLlC,UAAU,CAACiE,QAAQ,CAAC;UAClB,GAAGxE,aAAa,CAACyE,QAAQ,CAACxD,KAAK,CAACgB,IAAI,EAAEhB,KAAK,CAACiB,MAAM,CAAC;UACnDmC,MAAM,EAAE/D,KAAK,CAACY;QAChB,CAAC,CAAC;MACJ;IACF,CAAE;IACFwD,cAAc,EAAEA,CAAC;MAAEzD;IAAM,CAAC,KACxBV,UAAU,CAAC4D,IAAI,CAAC;MAAEC,IAAI,EAAE,cAAc;MAAEC,MAAM,EAAEpD,KAAK,CAACC;IAAI,CAAC;EAC5D,EACF,CAAC;AAEN;AAEA,MAAMwB,MAAM,GAAG3C,UAAU,CAAC4E,MAAM,CAAC;EAC/BhC,SAAS,EAAE;IACTiC,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,SAAS/D,IAAIA,CAAA,EAAG,CAAC", "ignoreList": []}