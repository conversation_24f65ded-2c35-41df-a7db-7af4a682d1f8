{"version": 3, "names": ["React", "StyleSheet", "View", "getSelectionControlIOSColor", "useInternalTheme", "MaterialCommunityIcon", "TouchableRipple", "CheckboxIOS", "status", "disabled", "onPress", "theme", "themeOverrides", "testID", "rest", "checked", "indeterminate", "checkedColor", "rippleColor", "customColor", "color", "icon", "opacity", "createElement", "_extends", "borderless", "accessibilityRole", "accessibilityState", "accessibilityLiveRegion", "style", "styles", "container", "allowFontScaling", "name", "size", "direction", "displayName", "create", "borderRadius", "padding"], "sourceRoot": "../../../../src", "sources": ["components/Checkbox/CheckboxIOS.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAAgCC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AAEtE,SAASC,2BAA2B,QAAQ,SAAS;AACrD,SAASC,gBAAgB,QAAQ,oBAAoB;AAErD,OAAOC,qBAAqB,MAAM,0BAA0B;AAC5D,OAAOC,eAAe,MAAM,oCAAoC;AA6BhE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAGA,CAAC;EACnBC,MAAM;EACNC,QAAQ;EACRC,OAAO;EACPC,KAAK,EAAEC,cAAc;EACrBC,MAAM;EACN,GAAGC;AACE,CAAC,KAAK;EACX,MAAMH,KAAK,GAAGP,gBAAgB,CAACQ,cAAc,CAAC;EAC9C,MAAMG,OAAO,GAAGP,MAAM,KAAK,SAAS;EACpC,MAAMQ,aAAa,GAAGR,MAAM,KAAK,eAAe;EAEhD,MAAM;IAAES,YAAY;IAAEC;EAAY,CAAC,GAAGf,2BAA2B,CAAC;IAChEQ,KAAK;IACLF,QAAQ;IACRU,WAAW,EAAEL,IAAI,CAACM;EACpB,CAAC,CAAC;EAEF,MAAMC,IAAI,GAAGL,aAAa,GAAG,OAAO,GAAG,OAAO;EAC9C,MAAMM,OAAO,GAAGN,aAAa,IAAID,OAAO,GAAG,CAAC,GAAG,CAAC;EAEhD,oBACEf,KAAA,CAAAuB,aAAA,CAACjB,eAAe,EAAAkB,QAAA,KACVV,IAAI;IACRW,UAAU;IACVP,WAAW,EAAEA,WAAY;IACzBR,OAAO,EAAEA,OAAQ;IACjBD,QAAQ,EAAEA,QAAS;IACnBiB,iBAAiB,EAAC,UAAU;IAC5BC,kBAAkB,EAAE;MAAElB,QAAQ;MAAEM;IAAQ,CAAE;IAC1Ca,uBAAuB,EAAC,QAAQ;IAChCC,KAAK,EAAEC,MAAM,CAACC,SAAU;IACxBlB,MAAM,EAAEA,MAAO;IACfF,KAAK,EAAEA;EAAM,iBAEbX,KAAA,CAAAuB,aAAA,CAACrB,IAAI;IAAC2B,KAAK,EAAE;MAAEP;IAAQ;EAAE,gBACvBtB,KAAA,CAAAuB,aAAA,CAAClB,qBAAqB;IACpB2B,gBAAgB,EAAE,KAAM;IACxBC,IAAI,EAAEZ,IAAK;IACXa,IAAI,EAAE,EAAG;IACTd,KAAK,EAAEH,YAAa;IACpBkB,SAAS,EAAC;EAAK,CAChB,CACG,CACS,CAAC;AAEtB,CAAC;AAED5B,WAAW,CAAC6B,WAAW,GAAG,cAAc;AAExC,MAAMN,MAAM,GAAG7B,UAAU,CAACoC,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AAEF,eAAehC,WAAW;;AAE1B;AACA,SAASA,WAAW", "ignoreList": []}