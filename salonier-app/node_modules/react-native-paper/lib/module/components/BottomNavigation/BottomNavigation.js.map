{"version": 3, "names": ["React", "Animated", "Platform", "StyleSheet", "View", "useLatestCallback", "BottomNavigationBar", "BottomNavigationRouteScreen", "useInternalTheme", "useAnimatedValueArray", "FAR_FAR_AWAY", "OS", "SceneComponent", "memo", "component", "rest", "createElement", "BottomNavigation", "navigationState", "renderScene", "renderIcon", "renderLabel", "renderTouchable", "getLabelText", "getBadge", "getColor", "getAccessibilityLabel", "getTestID", "activeColor", "inactiveColor", "keyboardHidesNavigationBar", "barStyle", "labeled", "style", "activeIndicatorStyle", "sceneAnimationEnabled", "sceneAnimationType", "sceneAnimationEasing", "onTabPress", "onTabLongPress", "onIndexChange", "shifting", "shiftingProp", "safeAreaInsets", "labelMaxFontSizeMultiplier", "compact", "compactProp", "testID", "theme", "themeOverrides", "getLazy", "route", "lazy", "scale", "animation", "isV3", "routes", "length", "console", "warn", "<PERSON><PERSON><PERSON>", "index", "key", "tabsPositionAnims", "map", "_", "i", "offsetsAnims", "loaded", "setLoaded", "useState", "includes", "animateToIndex", "useCallback", "parallel", "timing", "toValue", "duration", "useNativeDriver", "easing", "start", "finished", "for<PERSON>ach", "offset", "setValue", "useEffect", "prevNavigationState", "useRef", "undefined", "_prevNavigationState$", "current", "handleTabPress", "event", "defaultPrevented", "findIndex", "jumpTo", "colors", "styles", "container", "content", "backgroundColor", "background", "_prevNavigationState$2", "focused", "previouslyFocused", "countAlphaOffscreen", "renderToHardwareTextureAndroid", "opacity", "interpolate", "inputRange", "outputRange", "offsetTarget", "top", "left", "zIndex", "pointerEvents", "accessibilityElementsHidden", "importantForAccessibility", "visibility", "absoluteFill", "collapsable", "removeClippedSubviews", "_extends", "needsOffscreenAlphaCompositing", "transform", "translateX", "translateY", "animationEasing", "SceneMap", "scenes", "Bar", "create", "flex", "overflow"], "sourceRoot": "../../../../src", "sources": ["components/BottomNavigation/BottomNavigation.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EAGRC,QAAQ,EAERC,UAAU,EACVC,IAAI,QAEC,cAAc;AAErB,OAAOC,iBAAiB,MAAM,qBAAqB;AAEnD,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,2BAA2B,MAAM,+BAA+B;AACvE,SAASC,gBAAgB,QAAQ,oBAAoB;AAErD,OAAOC,qBAAqB,MAAM,mCAAmC;AAyPrE,MAAMC,YAAY,GAAGR,QAAQ,CAACS,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI;AAErD,MAAMC,cAAc,gBAAGZ,KAAK,CAACa,IAAI,CAAC,CAAC;EAAEC,SAAS;EAAE,GAAGC;AAAU,CAAC,kBAC5Df,KAAK,CAACgB,aAAa,CAACF,SAAS,EAAEC,IAAI,CACrC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,gBAAgB,GAAGA,CAA0B;EACjDC,eAAe;EACfC,WAAW;EACXC,UAAU;EACVC,WAAW;EACXC,eAAe;EACfC,YAAY;EACZC,QAAQ;EACRC,QAAQ;EACRC,qBAAqB;EACrBC,SAAS;EACTC,WAAW;EACXC,aAAa;EACbC,0BAA0B,GAAG5B,QAAQ,CAACS,EAAE,KAAK,SAAS;EACtDoB,QAAQ;EACRC,OAAO,GAAG,IAAI;EACdC,KAAK;EACLC,oBAAoB;EACpBC,qBAAqB,GAAG,KAAK;EAC7BC,kBAAkB,GAAG,SAAS;EAC9BC,oBAAoB;EACpBC,UAAU;EACVC,cAAc;EACdC,aAAa;EACbC,QAAQ,EAAEC,YAAY;EACtBC,cAAc;EACdC,0BAA0B,GAAG,CAAC;EAC9BC,OAAO,EAAEC,WAAW;EACpBC,MAAM,GAAG,mBAAmB;EAC5BC,KAAK,EAAEC,cAAc;EACrBC,OAAO,GAAGA,CAAC;IAAEC;EAAwB,CAAC,KAAKA,KAAK,CAACC;AACrC,CAAC,KAAK;EAClB,MAAMJ,KAAK,GAAGxC,gBAAgB,CAACyC,cAAc,CAAC;EAC9C,MAAM;IAAEI;EAAM,CAAC,GAAGL,KAAK,CAACM,SAAS;EACjC,MAAMT,OAAO,GAAGC,WAAW,IAAI,CAACE,KAAK,CAACO,IAAI;EAC1C,IAAId,QAAQ,GACVC,YAAY,KAAKM,KAAK,CAACO,IAAI,GAAG,KAAK,GAAGrC,eAAe,CAACsC,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;EAE1E,IAAIhB,QAAQ,IAAIvB,eAAe,CAACsC,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;IACjDhB,QAAQ,GAAG,KAAK;IAChBiB,OAAO,CAACC,IAAI,CACV,kEACF,CAAC;EACH;EAEA,MAAMC,UAAU,GAAG1C,eAAe,CAACsC,MAAM,CAACtC,eAAe,CAAC2C,KAAK,CAAC,CAACC,GAAG;;EAEpE;AACF;AACA;AACA;EACE,MAAMC,iBAAiB,GAAGtD,qBAAqB,CAC7CS,eAAe,CAACsC,MAAM,CAACQ,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAC9BA,CAAC,KAAKhD,eAAe,CAAC2C,KAAK,GAAG,CAAC,GAAGK,CAAC,IAAIhD,eAAe,CAAC2C,KAAK,GAAG,CAAC,GAAG,CAAC,CACtE,CACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMM,YAAY,GAAG1D,qBAAqB,CACxCS,eAAe,CAACsC,MAAM,CAACQ,GAAG;EACxB;EACA,CAACC,CAAC,EAAEC,CAAC,KAAMA,CAAC,KAAKhD,eAAe,CAAC2C,KAAK,GAAG,CAAC,GAAG,CAC/C,CACF,CAAC;;EAED;AACF;AACA;EACE,MAAM,CAACO,MAAM,EAAEC,SAAS,CAAC,GAAGrE,KAAK,CAACsE,QAAQ,CAAW,CAACV,UAAU,CAAC,CAAC;EAElE,IAAI,CAACQ,MAAM,CAACG,QAAQ,CAACX,UAAU,CAAC,EAAE;IAChC;IACAS,SAAS,CAAED,MAAM,IAAK,CAAC,GAAGA,MAAM,EAAER,UAAU,CAAC,CAAC;EAChD;EAEA,MAAMY,cAAc,GAAGxE,KAAK,CAACyE,WAAW,CACrCZ,KAAa,IAAK;IACjB5D,QAAQ,CAACyE,QAAQ,CAAC,CAChB,GAAGxD,eAAe,CAACsC,MAAM,CAACQ,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KACjCjE,QAAQ,CAAC0E,MAAM,CAACZ,iBAAiB,CAACG,CAAC,CAAC,EAAE;MACpCU,OAAO,EAAEV,CAAC,KAAKL,KAAK,GAAG,CAAC,GAAGK,CAAC,IAAIL,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAC9CgB,QAAQ,EAAE7B,KAAK,CAACO,IAAI,IAAId,QAAQ,GAAG,GAAG,GAAGY,KAAK,GAAG,CAAC;MAClDyB,eAAe,EAAE,IAAI;MACrBC,MAAM,EAAE1C;IACV,CAAC,CACH,CAAC,CACF,CAAC,CAAC2C,KAAK,CAAC,CAAC;MAAEC;IAAS,CAAC,KAAK;MACzB,IAAIA,QAAQ,EAAE;QACZ;QACA;QACAd,YAAY,CAACe,OAAO,CAAC,CAACC,MAAM,EAAEjB,CAAC,KAAK;UAClC,IAAIA,CAAC,KAAKL,KAAK,EAAE;YACfsB,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;UACpB,CAAC,MAAM;YACLD,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;UACpB;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC,EACD,CACE3C,QAAQ,EACRvB,eAAe,CAACsC,MAAM,EACtBW,YAAY,EACZd,KAAK,EACLU,iBAAiB,EACjB1B,oBAAoB,EACpBW,KAAK,CAET,CAAC;EAEDhD,KAAK,CAACqF,SAAS,CAAC,MAAM;IACpB;IACA;IACAb,cAAc,CAACtD,eAAe,CAAC2C,KAAK,CAAC;IACrC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMyB,mBAAmB,GAAGtF,KAAK,CAACuF,MAAM,CACtCC,SACF,CAAC;EAEDxF,KAAK,CAACqF,SAAS,CAAC,MAAM;IACpB;IACAlB,YAAY,CAACe,OAAO,CAAC,CAACC,MAAM,EAAEjB,CAAC,KAAK;MAAA,IAAAuB,qBAAA;MAClC,IACEvB,CAAC,KAAKhD,eAAe,CAAC2C,KAAK,IAC3BK,CAAC,OAAAuB,qBAAA,GAAKH,mBAAmB,CAACI,OAAO,cAAAD,qBAAA,uBAA3BA,qBAAA,CAA6B5B,KAAK,GACxC;QACAsB,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IAEFZ,cAAc,CAACtD,eAAe,CAAC2C,KAAK,CAAC;EACvC,CAAC,EAAE,CAAC3C,eAAe,CAAC2C,KAAK,EAAEW,cAAc,EAAEL,YAAY,CAAC,CAAC;EAEzD,MAAMwB,cAAc,GAAGtF,iBAAiB,CACrCuF,KAAuC,IAAK;IAC3CtD,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAGsD,KAAK,CAAC;IAEnB,IAAIA,KAAK,CAACC,gBAAgB,EAAE;MAC1B;IACF;IAEA,MAAMhC,KAAK,GAAG3C,eAAe,CAACsC,MAAM,CAACsC,SAAS,CAC3C3C,KAAK,IAAKyC,KAAK,CAACzC,KAAK,CAACW,GAAG,KAAKX,KAAK,CAACW,GACvC,CAAC;IAED,IAAID,KAAK,KAAK3C,eAAe,CAAC2C,KAAK,EAAE;MACnCyB,mBAAmB,CAACI,OAAO,GAAGxE,eAAe;MAC7CsB,aAAa,CAACqB,KAAK,CAAC;IACtB;EACF,CACF,CAAC;EAED,MAAMkC,MAAM,GAAG1F,iBAAiB,CAAEyD,GAAW,IAAK;IAChD,MAAMD,KAAK,GAAG3C,eAAe,CAACsC,MAAM,CAACsC,SAAS,CAC3C3C,KAAK,IAAKA,KAAK,CAACW,GAAG,KAAKA,GAC3B,CAAC;IAEDwB,mBAAmB,CAACI,OAAO,GAAGxE,eAAe;IAC7CsB,aAAa,CAACqB,KAAK,CAAC;EACtB,CAAC,CAAC;EAEF,MAAM;IAAEL;EAAO,CAAC,GAAGtC,eAAe;EAClC,MAAM;IAAE8E;EAAO,CAAC,GAAGhD,KAAK;EAExB,oBACEhD,KAAA,CAAAgB,aAAA,CAACZ,IAAI;IAAC6B,KAAK,EAAE,CAACgE,MAAM,CAACC,SAAS,EAAEjE,KAAK,CAAE;IAACc,MAAM,EAAEA;EAAO,gBACrD/C,KAAA,CAAAgB,aAAA,CAACZ,IAAI;IAAC6B,KAAK,EAAE,CAACgE,MAAM,CAACE,OAAO,EAAE;MAAEC,eAAe,EAAEJ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK;IAAW,CAAC;EAAE,GACpE7C,MAAM,CAACQ,GAAG,CAAC,CAACb,KAAK,EAAEU,KAAK,KAAK;IAAA,IAAAyC,sBAAA;IAC5B,IAAIpD,OAAO,CAAC;MAAEC;IAAM,CAAC,CAAC,KAAK,KAAK,IAAI,CAACiB,MAAM,CAACG,QAAQ,CAACpB,KAAK,CAACW,GAAG,CAAC,EAAE;MAC/D;MACA,OAAO,IAAI;IACb;IAEA,MAAMyC,OAAO,GAAGrF,eAAe,CAAC2C,KAAK,KAAKA,KAAK;IAC/C,MAAM2C,iBAAiB,GACrB,EAAAF,sBAAA,GAAAhB,mBAAmB,CAACI,OAAO,cAAAY,sBAAA,uBAA3BA,sBAAA,CAA6BzC,KAAK,MAAKA,KAAK;IAC9C,MAAM4C,mBAAmB,GACvBtE,qBAAqB,KAAKoE,OAAO,IAAIC,iBAAiB,CAAC;IACzD,MAAME,8BAA8B,GAClCvE,qBAAqB,IAAIoE,OAAO;IAElC,MAAMI,OAAO,GAAGxE,qBAAqB,GACjC4B,iBAAiB,CAACF,KAAK,CAAC,CAAC+C,WAAW,CAAC;MACnCC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC,GACFP,OAAO,GACP,CAAC,GACD,CAAC;IAEL,MAAMQ,YAAY,GAAGR,OAAO,GAAG,CAAC,GAAG7F,YAAY;IAE/C,MAAMsG,GAAG,GAAG7E,qBAAqB,GAC7BgC,YAAY,CAACN,KAAK,CAAC,CAAC+C,WAAW,CAAC;MAC9BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAAC,CAAC,EAAEC,YAAY;IAC/B,CAAC,CAAC,GACFA,YAAY;IAEhB,MAAME,IAAI,GACR7E,kBAAkB,KAAK,UAAU,GAC7B2B,iBAAiB,CAACF,KAAK,CAAC,CAAC+C,WAAW,CAAC;MACnCC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBC,WAAW,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;IAC1B,CAAC,CAAC,GACF,CAAC;IAEP,MAAMI,MAAM,GAAGX,OAAO,GAAG,CAAC,GAAG,CAAC;IAE9B,oBACEvG,KAAA,CAAAgB,aAAA,CAACT,2BAA2B;MAC1BuD,GAAG,EAAEX,KAAK,CAACW,GAAI;MACfqD,aAAa,EAAEZ,OAAO,GAAG,MAAM,GAAG,MAAO;MACzCa,2BAA2B,EAAE,CAACb,OAAQ;MACtCc,yBAAyB,EACvBd,OAAO,GAAG,MAAM,GAAG,qBACpB;MACD1C,KAAK,EAAEA,KAAM;MACbyD,UAAU,EAAEX,OAAQ;MACpB1E,KAAK,EAAE,CAAC9B,UAAU,CAACoH,YAAY,EAAE;QAAEL;MAAO,CAAC,CAAE;MAC7CM,WAAW,EAAE,KAAM;MACnBC,qBAAqB;MACnB;MACA;MACAvH,QAAQ,CAACS,EAAE,KAAK,KAAK,GAAGO,eAAe,CAAC2C,KAAK,KAAKA,KAAK,GAAG;IAC3D,gBAED7D,KAAA,CAAAgB,aAAA,CAACf,QAAQ,CAACG,IAAI,EAAAsH,QAAA,KACPxH,QAAQ,CAACS,EAAE,KAAK,SAAS,IAAI;MAChCgH,8BAA8B,EAAElB;IAClC,CAAC;MACDC,8BAA8B,EAAEA,8BAA+B;MAC/DzE,KAAK,EAAE,CACLgE,MAAM,CAACE,OAAO,EACd;QACEQ,OAAO;QACPiB,SAAS,EAAE,CAAC;UAAEC,UAAU,EAAEZ;QAAK,CAAC,EAAE;UAAEa,UAAU,EAAEd;QAAI,CAAC;MACvD,CAAC;IACD,IAED7F,WAAW,CAAC;MAAEgC,KAAK;MAAE4C;IAAO,CAAC,CACjB,CACY,CAAC;EAElC,CAAC,CACG,CAAC,eACP/F,KAAA,CAAAgB,aAAA,CAACV,mBAAmB;IAClBY,eAAe,EAAEA,eAAgB;IACjCE,UAAU,EAAEA,UAAW;IACvBC,WAAW,EAAEA,WAAY;IACzBC,eAAe,EAAEA,eAAgB;IACjCC,YAAY,EAAEA,YAAa;IAC3BC,QAAQ,EAAEA,QAAS;IACnBC,QAAQ,EAAEA,QAAS;IACnBC,qBAAqB,EAAEA,qBAAsB;IAC7CC,SAAS,EAAEA,SAAU;IACrBC,WAAW,EAAEA,WAAY;IACzBC,aAAa,EAAEA,aAAc;IAC7BC,0BAA0B,EAAEA,0BAA2B;IACvDG,KAAK,EAAEF,QAAS;IAChBG,oBAAoB,EAAEA,oBAAqB;IAC3CF,OAAO,EAAEA,OAAQ;IACjB+F,eAAe,EAAE1F,oBAAqB;IACtCC,UAAU,EAAEqD,cAAe;IAC3BpD,cAAc,EAAEA,cAAe;IAC/BE,QAAQ,EAAEA,QAAS;IACnBE,cAAc,EAAEA,cAAe;IAC/BC,0BAA0B,EAAEA,0BAA2B;IACvDC,OAAO,EAAEA,OAAQ;IACjBE,MAAM,EAAE,GAAGA,MAAM,MAAO;IACxBC,KAAK,EAAEA;EAAM,CACd,CACG,CAAC;AAEX,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA/B,gBAAgB,CAAC+G,QAAQ,GAA6BC,MAKrD,IAAK;EACJ,OAAO,CAAC;IACN9E,KAAK;IACL4C;EAIF,CAAC,kBACC/F,KAAA,CAAAgB,aAAA,CAACJ,cAAc;IACbkD,GAAG,EAAEX,KAAK,CAACW,GAAI;IACfhD,SAAS,EAAEmH,MAAM,CAAC9E,KAAK,CAACW,GAAG,GAAGX,KAAK,CAACW,GAAG,GAAG,EAAE,CAAE;IAC9CX,KAAK,EAAEA,KAAM;IACb4C,MAAM,EAAEA;EAAO,CAChB,CACF;AACH,CAAC;;AAED;AACA9E,gBAAgB,CAACiH,GAAG,GAAG5H,mBAAmB;AAE1C,eAAeW,gBAAgB;AAE/B,MAAMgF,MAAM,GAAG9F,UAAU,CAACgI,MAAM,CAAC;EAC/BjC,SAAS,EAAE;IACTkC,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC;EACDlC,OAAO,EAAE;IACPiC,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}