{"version": 3, "names": ["React", "Animated", "Platform", "View", "BottomNavigationRouteScreen", "Component", "render", "style", "index", "children", "visibility", "rest", "props", "display", "OS", "undefined", "createElement", "_extends", "testID", "createAnimatedComponent"], "sourceRoot": "../../../../src", "sources": ["components/BottomNavigation/BottomNavigationRouteScreen.tsx"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,QAAmB,cAAc;AAOlE,MAAMC,2BAA2B,SAASJ,KAAK,CAACK,SAAS,CAAQ;EAC/DC,MAAMA,CAAA,EAAgB;IACpB,MAAM;MAAEC,KAAK;MAAEC,KAAK;MAAEC,QAAQ;MAAEC,UAAU;MAAE,GAAGC;IAAK,CAAC,GAAG,IAAI,CAACC,KAAK;;IAElE;IACA;IACA;IACA,MAAMC,OAAO,GACXX,QAAQ,CAACY,EAAE,KAAK,KAAK,GAAIJ,UAAU,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM,GAAIK,SAAS;IAE1E,oBACEf,KAAA,CAAAgB,aAAA,CAACb,IAAI,EAAAc,QAAA;MACHC,MAAM,EAAE,gBAAgBV,KAAK,EAAG;MAChCD,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEM;MAAQ,CAAC;IAAE,GACxBF,IAAI,GAEPF,QACG,CAAC;EAEX;AACF;AAEA,eAAeR,QAAQ,CAACkB,uBAAuB,CAACf,2BAA2B,CAAC", "ignoreList": []}