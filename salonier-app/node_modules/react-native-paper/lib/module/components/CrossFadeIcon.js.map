{"version": 3, "names": ["React", "Animated", "StyleSheet", "View", "Icon", "isEqualIcon", "isValidIcon", "useInternalTheme", "CrossFadeIcon", "color", "size", "source", "theme", "themeOverrides", "testID", "currentIcon", "setCurrentIcon", "useState", "previousIcon", "setPreviousIcon", "current", "fade", "useRef", "Value", "scale", "animation", "useEffect", "setValue", "timing", "duration", "toValue", "useNativeDriver", "start", "opacityPrev", "opacityNext", "interpolate", "inputRange", "outputRange", "rotatePrev", "rotateNext", "createElement", "style", "styles", "content", "height", "width", "icon", "opacity", "transform", "rotate", "create", "alignItems", "justifyContent", "position", "top", "left", "right", "bottom"], "sourceRoot": "../../../src", "sources": ["components/CrossFadeIcon.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AAEzD,OAAOC,IAAI,IAAgBC,WAAW,EAAEC,WAAW,QAAQ,QAAQ;AACnE,SAASC,gBAAgB,QAAQ,iBAAiB;AA0BlD,MAAMC,aAAa,GAAGA,CAAC;EACrBC,KAAK;EACLC,IAAI;EACJC,MAAM;EACNC,KAAK,EAAEC,cAAc;EACrBC,MAAM,GAAG;AACJ,CAAC,KAAK;EACX,MAAMF,KAAK,GAAGL,gBAAgB,CAACM,cAAc,CAAC;EAC9C,MAAM,CAACE,WAAW,EAAEC,cAAc,CAAC,GAAGhB,KAAK,CAACiB,QAAQ,CAClD,MAAMN,MACR,CAAC;EACD,MAAM,CAACO,YAAY,EAAEC,eAAe,CAAC,GAAGnB,KAAK,CAACiB,QAAQ,CACpD,IACF,CAAC;EACD,MAAM;IAAEG,OAAO,EAAEC;EAAK,CAAC,GAAGrB,KAAK,CAACsB,MAAM,CAAiB,IAAIrB,QAAQ,CAACsB,KAAK,CAAC,CAAC,CAAC,CAAC;EAE7E,MAAM;IAAEC;EAAM,CAAC,GAAGZ,KAAK,CAACa,SAAS;EAEjC,IAAIV,WAAW,KAAKJ,MAAM,EAAE;IAC1BQ,eAAe,CAAC,MAAMJ,WAAW,CAAC;IAClCC,cAAc,CAAC,MAAML,MAAM,CAAC;EAC9B;EAEAX,KAAK,CAAC0B,SAAS,CAAC,MAAM;IACpB,IAAIpB,WAAW,CAACY,YAAY,CAAC,IAAI,CAACb,WAAW,CAACa,YAAY,EAAEH,WAAW,CAAC,EAAE;MACxEM,IAAI,CAACM,QAAQ,CAAC,CAAC,CAAC;MAEhB1B,QAAQ,CAAC2B,MAAM,CAACP,IAAI,EAAE;QACpBQ,QAAQ,EAAEL,KAAK,GAAG,GAAG;QACrBM,OAAO,EAAE,CAAC;QACVC,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACjB,WAAW,EAAEG,YAAY,EAAEG,IAAI,EAAEG,KAAK,CAAC,CAAC;EAE5C,MAAMS,WAAW,GAAGZ,IAAI;EACxB,MAAMa,WAAW,GAAGhB,YAAY,GAC5BG,IAAI,CAACc,WAAW,CAAC;IACfC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;EACpB,CAAC,CAAC,GACF,CAAC;EAEL,MAAMC,UAAU,GAAGjB,IAAI,CAACc,WAAW,CAAC;IAClCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,QAAQ,EAAE,MAAM;EAChC,CAAC,CAAC;EAEF,MAAME,UAAU,GAAGrB,YAAY,GAC3BG,IAAI,CAACc,WAAW,CAAC;IACfC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,MAAM,EAAE,SAAS;EACjC,CAAC,CAAC,GACF,MAAM;EAEV,oBACErC,KAAA,CAAAwC,aAAA,CAACrC,IAAI;IACHsC,KAAK,EAAE,CACLC,MAAM,CAACC,OAAO,EACd;MACEC,MAAM,EAAElC,IAAI;MACZmC,KAAK,EAAEnC;IACT,CAAC;EACD,GAEDQ,YAAY,gBACXlB,KAAA,CAAAwC,aAAA,CAACvC,QAAQ,CAACE,IAAI;IACZsC,KAAK,EAAE,CACLC,MAAM,CAACI,IAAI,EACX;MACEC,OAAO,EAAEd,WAAW;MACpBe,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAEX;MAAW,CAAC;IACpC,CAAC,CACD;IACFxB,MAAM,EAAE,GAAGA,MAAM;EAAY,gBAE7Bd,KAAA,CAAAwC,aAAA,CAACpC,IAAI;IAACO,MAAM,EAAEO,YAAa;IAACR,IAAI,EAAEA,IAAK;IAACD,KAAK,EAAEA,KAAM;IAACG,KAAK,EAAEA;EAAM,CAAE,CACxD,CAAC,GACd,IAAI,eACRZ,KAAA,CAAAwC,aAAA,CAACvC,QAAQ,CAACE,IAAI;IACZsC,KAAK,EAAE,CACLC,MAAM,CAACI,IAAI,EACX;MACEC,OAAO,EAAEb,WAAW;MACpBc,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAEV;MAAW,CAAC;IACpC,CAAC,CACD;IACFzB,MAAM,EAAE,GAAGA,MAAM;EAAW,gBAE5Bd,KAAA,CAAAwC,aAAA,CAACpC,IAAI;IAACO,MAAM,EAAEI,WAAY;IAACL,IAAI,EAAEA,IAAK;IAACD,KAAK,EAAEA,KAAM;IAACG,KAAK,EAAEA;EAAM,CAAE,CACvD,CACX,CAAC;AAEX,CAAC;AAED,eAAeJ,aAAa;AAE5B,MAAMkC,MAAM,GAAGxC,UAAU,CAACgD,MAAM,CAAC;EAC/BP,OAAO,EAAE;IACPQ,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDN,IAAI,EAAE;IACJO,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV;AACF,CAAC,CAAC", "ignoreList": []}