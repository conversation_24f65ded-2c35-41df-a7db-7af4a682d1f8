{"version": 3, "names": ["React", "Animated", "Easing", "Platform", "StyleSheet", "View", "useInternalTheme", "DURATION", "ActivityIndicator", "animating", "color", "indicatorColor", "hidesWhenStopped", "size", "indicatorSize", "style", "theme", "themeOverrides", "rest", "_theme$colors", "current", "timer", "useRef", "Value", "fade", "rotation", "undefined", "animation", "scale", "startRotation", "useCallback", "timing", "duration", "toValue", "isInteraction", "useNativeDriver", "start", "setValue", "loop", "stopRotation", "stop", "useEffect", "easing", "linear", "OS", "colors", "primary", "frames", "bezier", "containerStyle", "width", "height", "overflow", "createElement", "_extends", "styles", "container", "accessible", "accessibilityRole", "accessibilityState", "busy", "opacity", "collapsable", "map", "index", "inputRange", "Array", "from", "_", "frameIndex", "outputRange", "progress", "direction", "layerStyle", "transform", "rotate", "interpolate", "viewportStyle", "translateY", "offsetStyle", "top", "lineStyle", "borderColor", "borderWidth", "borderRadius", "key", "layer", "create", "justifyContent", "alignItems", "absoluteFillObject"], "sourceRoot": "../../../src", "sources": ["components/ActivityIndicator.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EACRC,MAAM,EACNC,QAAQ,EAERC,UAAU,EACVC,IAAI,QAEC,cAAc;AAErB,SAASC,gBAAgB,QAAQ,iBAAiB;AA2BlD,MAAMC,QAAQ,GAAG,IAAI;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAGA,CAAC;EACzBC,SAAS,GAAG,IAAI;EAChBC,KAAK,EAAEC,cAAc;EACrBC,gBAAgB,GAAG,IAAI;EACvBC,IAAI,EAAEC,aAAa,GAAG,OAAO;EAC7BC,KAAK;EACLC,KAAK,EAAEC,cAAc;EACrB,GAAGC;AACE,CAAC,KAAK;EAAA,IAAAC,aAAA;EACX,MAAMH,KAAK,GAAGV,gBAAgB,CAACW,cAAc,CAAC;EAC9C,MAAM;IAAEG,OAAO,EAAEC;EAAM,CAAC,GAAGrB,KAAK,CAACsB,MAAM,CACrC,IAAIrB,QAAQ,CAACsB,KAAK,CAAC,CAAC,CACtB,CAAC;EACD,MAAM;IAAEH,OAAO,EAAEI;EAAK,CAAC,GAAGxB,KAAK,CAACsB,MAAM,CACpC,IAAIrB,QAAQ,CAACsB,KAAK,CAAC,CAACd,SAAS,IAAIG,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAC3D,CAAC;EAED,MAAMa,QAAQ,GAAGzB,KAAK,CAACsB,MAAM,CAC3BI,SACF,CAAC;EAED,MAAM;IACJC,SAAS,EAAE;MAAEC;IAAM;EACrB,CAAC,GAAGZ,KAAK;EAET,MAAMa,aAAa,GAAG7B,KAAK,CAAC8B,WAAW,CAAC,MAAM;IAC5C;IACA7B,QAAQ,CAAC8B,MAAM,CAACP,IAAI,EAAE;MACpBQ,QAAQ,EAAE,GAAG,GAAGJ,KAAK;MACrBK,OAAO,EAAE,CAAC;MACVC,aAAa,EAAE,KAAK;MACpBC,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;;IAEV;IACA,IAAIX,QAAQ,CAACL,OAAO,EAAE;MACpBC,KAAK,CAACgB,QAAQ,CAAC,CAAC,CAAC;MACjB;MACApC,QAAQ,CAACqC,IAAI,CAACb,QAAQ,CAACL,OAAO,CAAC,CAACgB,KAAK,CAAC,CAAC;IACzC;EACF,CAAC,EAAE,CAACR,KAAK,EAAEJ,IAAI,EAAEH,KAAK,CAAC,CAAC;EAExB,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAId,QAAQ,CAACL,OAAO,EAAE;MACpBK,QAAQ,CAACL,OAAO,CAACoB,IAAI,CAAC,CAAC;IACzB;EACF,CAAC;EAEDxC,KAAK,CAACyC,SAAS,CAAC,MAAM;IACpB,IAAIhB,QAAQ,CAACL,OAAO,KAAKM,SAAS,EAAE;MAClC;MACAD,QAAQ,CAACL,OAAO,GAAGnB,QAAQ,CAAC8B,MAAM,CAACV,KAAK,EAAE;QACxCW,QAAQ,EAAEzB,QAAQ;QAClBmC,MAAM,EAAExC,MAAM,CAACyC,MAAM;QACrB;QACAR,eAAe,EAAEhC,QAAQ,CAACyC,EAAE,KAAK,KAAK;QACtCX,OAAO,EAAE,CAAC;QACVC,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ;IAEA,IAAIzB,SAAS,EAAE;MACboB,aAAa,CAAC,CAAC;IACjB,CAAC,MAAM,IAAIjB,gBAAgB,EAAE;MAC3B;MACAX,QAAQ,CAAC8B,MAAM,CAACP,IAAI,EAAE;QACpBQ,QAAQ,EAAE,GAAG,GAAGJ,KAAK;QACrBK,OAAO,EAAE,CAAC;QACVE,eAAe,EAAE,IAAI;QACrBD,aAAa,EAAE;MACjB,CAAC,CAAC,CAACE,KAAK,CAACG,YAAY,CAAC;IACxB,CAAC,MAAM;MACLA,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAC9B,SAAS,EAAEe,IAAI,EAAEZ,gBAAgB,EAAEiB,aAAa,EAAED,KAAK,EAAEP,KAAK,CAAC,CAAC;EAEpE,MAAMX,KAAK,GAAGC,cAAc,MAAAQ,aAAA,GAAIH,KAAK,CAAC6B,MAAM,cAAA1B,aAAA,uBAAZA,aAAA,CAAc2B,OAAO;EACrD,MAAMjC,IAAI,GACR,OAAOC,aAAa,KAAK,QAAQ,GAC7BA,aAAa,KAAK,OAAO,GACvB,EAAE,GACF,EAAE,GACJA,aAAa,GACbA,aAAa,GACb,EAAE;EAER,MAAMiC,MAAM,GAAI,EAAE,GAAGxC,QAAQ,GAAI,IAAI;EACrC,MAAMmC,MAAM,GAAGxC,MAAM,CAAC8C,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAChD,MAAMC,cAAc,GAAG;IACrBC,KAAK,EAAErC,IAAI;IACXsC,MAAM,EAAEtC,IAAI,GAAG,CAAC;IAChBuC,QAAQ,EAAE;EACZ,CAAC;EAED,oBACEpD,KAAA,CAAAqD,aAAA,CAAChD,IAAI,EAAAiD,QAAA;IACHvC,KAAK,EAAE,CAACwC,MAAM,CAACC,SAAS,EAAEzC,KAAK;EAAE,GAC7BG,IAAI;IACRuC,UAAU;IACVC,iBAAiB,EAAC,aAAa;IAC/BC,kBAAkB,EAAE;MAAEC,IAAI,EAAEnD;IAAU;EAAE,iBAExCT,KAAA,CAAAqD,aAAA,CAACpD,QAAQ,CAACI,IAAI;IACZU,KAAK,EAAE,CAAC;MAAEmC,KAAK,EAAErC,IAAI;MAAEsC,MAAM,EAAEtC,IAAI;MAAEgD,OAAO,EAAErC;IAAK,CAAC,CAAE;IACtDsC,WAAW,EAAE;EAAM,GAElB,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,KAAK,IAAK;IACrB;IACA,MAAMC,UAAU,GAAGC,KAAK,CAACC,IAAI,CAC3B,IAAID,KAAK,CAACnB,MAAM,CAAC,EACjB,CAACqB,CAAC,EAAEC,UAAU,KAAKA,UAAU,IAAItB,MAAM,GAAG,CAAC,CAC7C,CAAC;IACD,MAAMuB,WAAW,GAAGJ,KAAK,CAACC,IAAI,CAAC,IAAID,KAAK,CAACnB,MAAM,CAAC,EAAE,CAACqB,CAAC,EAAEC,UAAU,KAAK;MACnE,IAAIE,QAAQ,GAAI,CAAC,GAAGF,UAAU,IAAKtB,MAAM,GAAG,CAAC,CAAC;MAC9C,MAAMtB,QAAQ,GAAGuC,KAAK,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;MAElD,IAAIO,QAAQ,GAAG,GAAG,EAAE;QAClBA,QAAQ,GAAG,GAAG,GAAGA,QAAQ;MAC3B;MAEA,MAAMC,SAAS,GAAGR,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MAEjC,OAAO,GAAGQ,SAAS,IAAI,GAAG,GAAG,EAAE,CAAC,GAAG9B,MAAM,CAAC6B,QAAQ,CAAC,GAAG9C,QAAQ,KAAK;IACrE,CAAC,CAAC;IAEF,MAAMgD,UAAU,GAAG;MACjBvB,KAAK,EAAErC,IAAI;MACXsC,MAAM,EAAEtC,IAAI;MACZ6D,SAAS,EAAE,CACT;QACEC,MAAM,EAAEtD,KAAK,CAACuD,WAAW,CAAC;UACxBX,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBK,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,KAAK;QAC9D,CAAC;MACH,CAAC;IAEL,CAAC;IAED,MAAMO,aAAa,GAAG;MACpB3B,KAAK,EAAErC,IAAI;MACXsC,MAAM,EAAEtC,IAAI;MACZ6D,SAAS,EAAE,CACT;QACEI,UAAU,EAAEd,KAAK,GAAG,CAACnD,IAAI,GAAG,CAAC,GAAG;MAClC,CAAC,EACD;QACE8D,MAAM,EAAEtD,KAAK,CAACuD,WAAW,CAAC;UAAEX,UAAU;UAAEK;QAAY,CAAC;MACvD,CAAC;IAEL,CAAC;IAED,MAAMS,WAAW,GAAGf,KAAK,GAAG;MAAEgB,GAAG,EAAEnE,IAAI,GAAG;IAAE,CAAC,GAAG,IAAI;IAEpD,MAAMoE,SAAS,GAAG;MAChB/B,KAAK,EAAErC,IAAI;MACXsC,MAAM,EAAEtC,IAAI;MACZqE,WAAW,EAAExE,KAAK;MAClByE,WAAW,EAAEtE,IAAI,GAAG,EAAE;MACtBuE,YAAY,EAAEvE,IAAI,GAAG;IACvB,CAAC;IAED,oBACEb,KAAA,CAAAqD,aAAA,CAACpD,QAAQ,CAACI,IAAI;MAACgF,GAAG,EAAErB,KAAM;MAACjD,KAAK,EAAE,CAACwC,MAAM,CAAC+B,KAAK;IAAE,gBAC/CtF,KAAA,CAAAqD,aAAA,CAACpD,QAAQ,CAACI,IAAI;MAACU,KAAK,EAAE0D;IAAW,gBAC/BzE,KAAA,CAAAqD,aAAA,CAACpD,QAAQ,CAACI,IAAI;MACZU,KAAK,EAAE,CAACkC,cAAc,EAAE8B,WAAW,CAAE;MACrCjB,WAAW,EAAE;IAAM,gBAEnB9D,KAAA,CAAAqD,aAAA,CAACpD,QAAQ,CAACI,IAAI;MAACU,KAAK,EAAE8D;IAAc,gBAClC7E,KAAA,CAAAqD,aAAA,CAACpD,QAAQ,CAACI,IAAI;MAACU,KAAK,EAAEkC,cAAe;MAACa,WAAW,EAAE;IAAM,gBACvD9D,KAAA,CAAAqD,aAAA,CAACpD,QAAQ,CAACI,IAAI;MAACU,KAAK,EAAEkE;IAAU,CAAE,CACrB,CACF,CACF,CACF,CACF,CAAC;EAEpB,CAAC,CACY,CACX,CAAC;AAEX,CAAC;AAED,MAAM1B,MAAM,GAAGnD,UAAU,CAACmF,MAAM,CAAC;EAC/B/B,SAAS,EAAE;IACTgC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EAEDH,KAAK,EAAE;IACL,GAAGlF,UAAU,CAACsF,kBAAkB;IAEhCF,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAejF,iBAAiB", "ignoreList": []}