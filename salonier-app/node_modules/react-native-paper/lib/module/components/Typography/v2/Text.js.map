{"version": 3, "names": ["React", "StyleSheet", "Text", "NativeText", "useInternalTheme", "forwardRef", "style", "theme", "overrideTheme", "rest", "ref", "_theme$fonts", "_theme$colors", "root", "useRef", "useImperativeHandle", "setNativeProps", "args", "_root$current", "current", "createElement", "_extends", "isV3", "fonts", "regular", "color", "colors", "onSurface", "text", "styles", "create", "textAlign"], "sourceRoot": "../../../../../src", "sources": ["components/Typography/v2/Text.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAEEC,UAAU,EACVC,IAAI,IAAIC,UAAU,QAEb,cAAc;AAIrB,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,UAAU,QAAQ,2BAA2B;AAUtD;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMH,IAA+C,GAAGA,CACtD;EAAEI,KAAK;EAAEC,KAAK,EAAEC,aAAa;EAAE,GAAGC;AAAY,CAAC,EAC/CC,GAAG,KACA;EAAA,IAAAC,YAAA,EAAAC,aAAA;EACH,MAAMC,IAAI,GAAGb,KAAK,CAACc,MAAM,CAAoB,IAAI,CAAC;EAClD,MAAMP,KAAK,GAAGH,gBAAgB,CAACI,aAAa,CAAC;EAE7CR,KAAK,CAACe,mBAAmB,CAACL,GAAG,EAAE,OAAO;IACpCM,cAAc,EAAGC,IAAY;MAAA,IAAAC,aAAA;MAAA,QAAAA,aAAA,GAAKL,IAAI,CAACM,OAAO,cAAAD,aAAA,uBAAZA,aAAA,CAAcF,cAAc,CAACC,IAAI,CAAC;IAAA;EACtE,CAAC,CAAC,CAAC;EAEH,oBACEjB,KAAA,CAAAoB,aAAA,CAACjB,UAAU,EAAAkB,QAAA,KACLZ,IAAI;IACRC,GAAG,EAAEG,IAAK;IACVP,KAAK,EAAE,CACL;MACE,IAAI,CAACC,KAAK,CAACe,IAAI,MAAAX,YAAA,GAAIJ,KAAK,CAACgB,KAAK,cAAAZ,YAAA,uBAAXA,YAAA,CAAaa,OAAO,EAAC;MACxCC,KAAK,EAAElB,KAAK,CAACe,IAAI,IAAAV,aAAA,GAAGL,KAAK,CAACmB,MAAM,cAAAd,aAAA,uBAAZA,aAAA,CAAce,SAAS,GAAGpB,KAAK,CAACmB,MAAM,CAACE;IAC7D,CAAC,EACDC,MAAM,CAACD,IAAI,EACXtB,KAAK;EACL,EACH,CAAC;AAEN,CAAC;AAED,MAAMuB,MAAM,GAAG5B,UAAU,CAAC6B,MAAM,CAAC;EAC/BF,IAAI,EAAE;IACJG,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAe1B,UAAU,CAACH,IAAI,CAAC", "ignoreList": []}