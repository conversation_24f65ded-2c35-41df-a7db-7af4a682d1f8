{"version": 3, "names": ["React", "I18nManager", "StyleSheet", "Text", "NativeText", "AnimatedText", "StyledText", "useInternalTheme", "forwardRef", "style", "variant", "theme", "initialTheme", "rest", "ref", "root", "useRef", "writingDirection", "getConstants", "isRTL", "useImperativeHandle", "setNativeProps", "args", "_root$current", "current", "isV3", "font", "fonts", "textStyle", "isValidElement", "children", "type", "Component", "props", "Error", "Object", "keys", "join", "createElement", "_extends", "styles", "text", "color", "colors", "onSurface", "_theme$fonts", "_theme$colors", "default", "regular", "create", "textAlign", "customText"], "sourceRoot": "../../../../src", "sources": ["components/Typography/Text.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,WAAW,EAEXC,UAAU,EACVC,IAAI,IAAIC,UAAU,QAEb,cAAc;AAErB,OAAOC,YAAY,MAAM,gBAAgB;AAEzC,OAAOC,UAAU,MAAM,iBAAiB;AACxC,SAASC,gBAAgB,QAAQ,oBAAoB;AAErD,SAASC,UAAU,QAAQ,wBAAwB;AA6BnD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAML,IAAI,GAAGA,CACX;EAAEM,KAAK;EAAEC,OAAO;EAAEC,KAAK,EAAEC,YAAY;EAAE,GAAGC;AAAoB,CAAC,EAC/DC,GAAY,KACT;EACH,MAAMC,IAAI,GAAGf,KAAK,CAACgB,MAAM,CAAoB,IAAI,CAAC;EAClD;EACA,MAAML,KAAK,GAAGJ,gBAAgB,CAACK,YAAY,CAAC;EAC5C,MAAMK,gBAAgB,GAAGhB,WAAW,CAACiB,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;EAEzEnB,KAAK,CAACoB,mBAAmB,CAACN,GAAG,EAAE,OAAO;IACpCO,cAAc,EAAGC,IAAY;MAAA,IAAAC,aAAA;MAAA,QAAAA,aAAA,GAAKR,IAAI,CAACS,OAAO,cAAAD,aAAA,uBAAZA,aAAA,CAAcF,cAAc,CAACC,IAAI,CAAC;IAAA;EACtE,CAAC,CAAC,CAAC;EAEH,IAAIX,KAAK,CAACc,IAAI,IAAIf,OAAO,EAAE;IACzB,IAAIgB,IAAI,GAAGf,KAAK,CAACgB,KAAK,CAACjB,OAAO,CAAC;IAC/B,IAAIkB,SAAS,GAAG,CAACF,IAAI,EAAEjB,KAAK,CAAC;IAE7B,IACE,aAAAT,KAAK,CAAC6B,cAAc,CAAChB,IAAI,CAACiB,QAAQ,CAAC,KAClCjB,IAAI,CAACiB,QAAQ,CAACC,IAAI,KAAKC,SAAS,IAC/BnB,IAAI,CAACiB,QAAQ,CAACC,IAAI,KAAK1B,YAAY,IACnCQ,IAAI,CAACiB,QAAQ,CAACC,IAAI,KAAKzB,UAAU,CAAC,EACpC;MACA,MAAM;QAAE2B;MAAM,CAAC,GAAGpB,IAAI,CAACiB,QAEtB;;MAED;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIG,KAAK,CAACvB,OAAO,EAAE;QACjBgB,IAAI,GAAGf,KAAK,CAACgB,KAAK,CAACM,KAAK,CAACvB,OAAO,CAAsC;QACtEkB,SAAS,GAAG,CAACnB,KAAK,EAAEiB,IAAI,CAAC;MAC3B;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACO,KAAK,CAACvB,OAAO,EAAE;QAClBkB,SAAS,GAAG,CAACnB,KAAK,EAAEwB,KAAK,CAACxB,KAAK,CAAC;MAClC;IACF;IAEA,IAAI,OAAOiB,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,IAAIQ,KAAK,CACb,WAAWxB,OAAO,kDAAkDyB,MAAM,CAACC,IAAI,CAC7EzB,KAAK,CAACgB,KACR,CAAC,CAACU,IAAI,CAAC,IAAI,CAAC,GACd,CAAC;IACH;IAEA,oBACErC,KAAA,CAAAsC,aAAA,CAAClC,UAAU,EAAAmC,QAAA;MACTzB,GAAG,EAAEC,IAAK;MACVN,KAAK,EAAE,CACL+B,MAAM,CAACC,IAAI,EACX;QAAExB,gBAAgB;QAAEyB,KAAK,EAAE/B,KAAK,CAACgC,MAAM,CAACC;MAAU,CAAC,EACnDhB,SAAS;IACT,GACEf,IAAI,CACT,CAAC;EAEN,CAAC,MAAM;IAAA,IAAAgC,YAAA,EAAAC,aAAA;IACL,MAAMpB,IAAI,GAAGf,KAAK,CAACc,IAAI,GAAGd,KAAK,CAACgB,KAAK,CAACoB,OAAO,IAAAF,YAAA,GAAGlC,KAAK,CAACgB,KAAK,cAAAkB,YAAA,uBAAXA,YAAA,CAAaG,OAAO;IACpE,MAAMpB,SAAS,GAAG;MAChB,GAAGF,IAAI;MACPgB,KAAK,EAAE/B,KAAK,CAACc,IAAI,IAAAqB,aAAA,GAAGnC,KAAK,CAACgC,MAAM,cAAAG,aAAA,uBAAZA,aAAA,CAAcF,SAAS,GAAGjC,KAAK,CAACgC,MAAM,CAACF;IAC7D,CAAC;IACD,oBACEzC,KAAA,CAAAsC,aAAA,CAAClC,UAAU,EAAAmC,QAAA,KACL1B,IAAI;MACRC,GAAG,EAAEC,IAAK;MACVN,KAAK,EAAE,CAAC+B,MAAM,CAACC,IAAI,EAAEb,SAAS,EAAE;QAAEX;MAAiB,CAAC,EAAER,KAAK;IAAE,EAC9D,CAAC;EAEN;AACF,CAAC;AAED,MAAM+B,MAAM,GAAGtC,UAAU,CAAC+C,MAAM,CAAC;EAC/BR,IAAI,EAAE;IACJS,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAMF,MAAMlB,SAAS,GAAGxB,UAAU,CAACL,IAAI,CAAyB;AAE1D,OAAO,MAAMgD,UAAU,GAAGA,CAAA,KAAUnB,SAAwC;AAE5E,eAAeA,SAAS", "ignoreList": []}