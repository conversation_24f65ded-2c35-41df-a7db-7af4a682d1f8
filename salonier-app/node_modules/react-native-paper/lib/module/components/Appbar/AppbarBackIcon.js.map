{"version": 3, "names": ["React", "I18nManager", "Image", "Platform", "StyleSheet", "View", "MaterialCommunityIcon", "AppbarBackIcon", "size", "color", "iosIconSize", "OS", "createElement", "style", "styles", "wrapper", "width", "height", "transform", "scaleX", "getConstants", "isRTL", "source", "require", "icon", "tintColor", "accessibilityIgnoresInvertColors", "name", "direction", "create", "alignItems", "justifyContent", "resizeMode"], "sourceRoot": "../../../../src", "sources": ["components/Appbar/AppbarBackIcon.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AAE7E,OAAOC,qBAAqB,MAAM,0BAA0B;AAE5D,MAAMC,cAAc,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAuC,CAAC,KAAK;EAC3E,MAAMC,WAAW,GAAGF,IAAI,GAAG,CAAC;EAE5B,OAAOL,QAAQ,CAACQ,EAAE,KAAK,KAAK,gBAC1BX,KAAA,CAAAY,aAAA,CAACP,IAAI;IACHQ,KAAK,EAAE,CACLC,MAAM,CAACC,OAAO,EACd;MACEC,KAAK,EAAER,IAAI;MACXS,MAAM,EAAET,IAAI;MACZU,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAElB,WAAW,CAACmB,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,CAAC,CAAC,GAAG;MAAE,CAAC;IACnE,CAAC;EACD,gBAEFrB,KAAA,CAAAY,aAAA,CAACV,KAAK;IACJoB,MAAM,EAAEC,OAAO,CAAC,+BAA+B,CAAE;IACjDV,KAAK,EAAE,CACLC,MAAM,CAACU,IAAI,EACX;MAAEC,SAAS,EAAEhB,KAAK;MAAEO,KAAK,EAAEN,WAAW;MAAEO,MAAM,EAAEP;IAAY,CAAC,CAC7D;IACFgB,gCAAgC;EAAA,CACjC,CACG,CAAC,gBAEP1B,KAAA,CAAAY,aAAA,CAACN,qBAAqB;IACpBqB,IAAI,EAAC,YAAY;IACjBlB,KAAK,EAAEA,KAAM;IACbD,IAAI,EAAEA,IAAK;IACXoB,SAAS,EAAE3B,WAAW,CAACmB,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG;EAAM,CAC7D,CACF;AACH,CAAC;AAED,MAAMP,MAAM,GAAGV,UAAU,CAACyB,MAAM,CAAC;EAC/Bd,OAAO,EAAE;IACPe,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDP,IAAI,EAAE;IACJQ,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAezB,cAAc;;AAE7B;AACA,SAASA,cAAc", "ignoreList": []}