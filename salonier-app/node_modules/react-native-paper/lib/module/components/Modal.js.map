{"version": 3, "names": ["React", "Animated", "Easing", "StyleSheet", "Pressable", "View", "useSafeAreaInsets", "useLatestCallback", "Surface", "useInternalTheme", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "useAnimatedValue", "DEFAULT_DURATION", "AnimatedPressable", "createAnimatedComponent", "Modal", "dismissable", "dismissable<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible", "overlayAccessibilityLabel", "on<PERSON><PERSON><PERSON>", "children", "contentContainerStyle", "style", "theme", "themeOverrides", "testID", "_theme$colors", "onDismissCallback", "scale", "animation", "top", "bottom", "opacity", "visibleInternal", "setVisibleInternal", "useState", "showModalAnimation", "useCallback", "timing", "toValue", "duration", "easing", "out", "cubic", "useNativeDriver", "start", "hideModalAnimation", "finished", "useEffect", "undefined", "onHardwareBackPress", "subscription", "remove", "createElement", "pointerEvents", "accessibilityViewIsModal", "accessibilityLiveRegion", "absoluteFill", "onAccessibilityEscape", "accessibilityLabel", "accessibilityRole", "disabled", "onPress", "importantForAccessibility", "styles", "backdrop", "backgroundColor", "colors", "wrapper", "marginTop", "marginBottom", "content", "container", "create", "flex", "absoluteFillObject", "justifyContent"], "sourceRoot": "../../../src", "sources": ["components/Modal.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EACRC,MAAM,EAENC,UAAU,EACVC,SAAS,EACTC,IAAI,QAEC,cAAc;AAErB,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,OAAOC,iBAAiB,MAAM,qBAAqB;AAEnD,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,gBAAgB,QAAQ,iBAAiB;AAElD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,OAAOC,gBAAgB,MAAM,2BAA2B;AA8CxD,MAAMC,gBAAgB,GAAG,GAAG;AAC5B,MAAMC,iBAAiB,GAAGb,QAAQ,CAACc,uBAAuB,CAACX,SAAS,CAAC;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,KAAKA,CAAC;EACbC,WAAW,GAAG,IAAI;EAClBC,qBAAqB,GAAGD,WAAW;EACnCE,OAAO,GAAG,KAAK;EACfC,yBAAyB,GAAG,aAAa;EACzCC,SAAS,GAAGA,CAAA,KAAM,CAAC,CAAC;EACpBC,QAAQ;EACRC,qBAAqB;EACrBC,KAAK;EACLC,KAAK,EAAEC,cAAc;EACrBC,MAAM,GAAG;AACJ,CAAC,EAAE;EAAA,IAAAC,aAAA;EACR,MAAMH,KAAK,GAAGhB,gBAAgB,CAACiB,cAAc,CAAC;EAC9C,MAAMG,iBAAiB,GAAGtB,iBAAiB,CAACc,SAAS,CAAC;EACtD,MAAM;IAAES;EAAM,CAAC,GAAGL,KAAK,CAACM,SAAS;EACjC,MAAM;IAAEC,GAAG;IAAEC;EAAO,CAAC,GAAG3B,iBAAiB,CAAC,CAAC;EAC3C,MAAM4B,OAAO,GAAGtB,gBAAgB,CAACO,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;EACjD,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,KAAK,CAACqC,QAAQ,CAAClB,OAAO,CAAC;EAErE,MAAMmB,kBAAkB,GAAGtC,KAAK,CAACuC,WAAW,CAAC,MAAM;IACjDtC,QAAQ,CAACuC,MAAM,CAACN,OAAO,EAAE;MACvBO,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAEZ,KAAK,GAAGjB,gBAAgB;MAClC8B,MAAM,EAAEzC,MAAM,CAAC0C,GAAG,CAAC1C,MAAM,CAAC2C,KAAK,CAAC;MAChCC,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAACb,OAAO,EAAEJ,KAAK,CAAC,CAAC;EAEpB,MAAMkB,kBAAkB,GAAGhD,KAAK,CAACuC,WAAW,CAAC,MAAM;IACjDtC,QAAQ,CAACuC,MAAM,CAACN,OAAO,EAAE;MACvBO,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAEZ,KAAK,GAAGjB,gBAAgB;MAClC8B,MAAM,EAAEzC,MAAM,CAAC0C,GAAG,CAAC1C,MAAM,CAAC2C,KAAK,CAAC;MAChCC,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;MAAEE;IAAS,CAAC,KAAK;MACzB,IAAI,CAACA,QAAQ,EAAE;QACb;MACF;MAEAb,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACF,OAAO,EAAEJ,KAAK,CAAC,CAAC;EAEpB9B,KAAK,CAACkD,SAAS,CAAC,MAAM;IACpB,IAAIf,eAAe,KAAKhB,OAAO,EAAE;MAC/B;IACF;IAEA,IAAI,CAACgB,eAAe,IAAIhB,OAAO,EAAE;MAC/BiB,kBAAkB,CAAC,IAAI,CAAC;MACxB,OAAOE,kBAAkB,CAAC,CAAC;IAC7B;IAEA,IAAIH,eAAe,IAAI,CAAChB,OAAO,EAAE;MAC/B,OAAO6B,kBAAkB,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,CAAC7B,OAAO,EAAEmB,kBAAkB,EAAEU,kBAAkB,EAAEb,eAAe,CAAC,CAAC;EAEtEnC,KAAK,CAACkD,SAAS,CAAC,MAAM;IACpB,IAAI,CAAC/B,OAAO,EAAE;MACZ,OAAOgC,SAAS;IAClB;IAEA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;MAChC,IAAInC,WAAW,IAAIC,qBAAqB,EAAE;QACxCW,iBAAiB,CAAC,CAAC;MACrB;MAEA,OAAO,IAAI;IACb,CAAC;IAED,MAAMwB,YAAY,GAAG3C,gBAAgB,CACnCC,WAAW,EACX,mBAAmB,EACnByC,mBACF,CAAC;IACD,OAAO,MAAMC,YAAY,CAACC,MAAM,CAAC,CAAC;EACpC,CAAC,EAAE,CAACrC,WAAW,EAAEC,qBAAqB,EAAEW,iBAAiB,EAAEV,OAAO,CAAC,CAAC;EAEpE,IAAI,CAACgB,eAAe,EAAE;IACpB,OAAO,IAAI;EACb;EAEA,oBACEnC,KAAA,CAAAuD,aAAA,CAACtD,QAAQ,CAACI,IAAI;IACZmD,aAAa,EAAErC,OAAO,GAAG,MAAM,GAAG,MAAO;IACzCsC,wBAAwB;IACxBC,uBAAuB,EAAC,QAAQ;IAChClC,KAAK,EAAErB,UAAU,CAACwD,YAAa;IAC/BC,qBAAqB,EAAE/B,iBAAkB;IACzCF,MAAM,EAAEA;EAAO,gBAEf3B,KAAA,CAAAuD,aAAA,CAACzC,iBAAiB;IAChB+C,kBAAkB,EAAEzC,yBAA0B;IAC9C0C,iBAAiB,EAAC,QAAQ;IAC1BC,QAAQ,EAAE,CAAC9C,WAAY;IACvB+C,OAAO,EAAE/C,WAAW,GAAGY,iBAAiB,GAAGsB,SAAU;IACrDc,yBAAyB,EAAC,IAAI;IAC9BzC,KAAK,EAAE,CACL0C,MAAM,CAACC,QAAQ,EACf;MACEC,eAAe,GAAAxC,aAAA,GAAEH,KAAK,CAAC4C,MAAM,cAAAzC,aAAA,uBAAZA,aAAA,CAAcuC,QAAQ;MACvCjC;IACF,CAAC,CACD;IACFP,MAAM,EAAE,GAAGA,MAAM;EAAY,CAC9B,CAAC,eACF3B,KAAA,CAAAuD,aAAA,CAAClD,IAAI;IACHmB,KAAK,EAAE,CACL0C,MAAM,CAACI,OAAO,EACd;MAAEC,SAAS,EAAEvC,GAAG;MAAEwC,YAAY,EAAEvC;IAAO,CAAC,EACxCT,KAAK,CACL;IACFgC,aAAa,EAAC,UAAU;IACxB7B,MAAM,EAAE,GAAGA,MAAM;EAAW,gBAE5B3B,KAAA,CAAAuD,aAAA,CAAC/C,OAAO;IACNmB,MAAM,EAAE,GAAGA,MAAM,UAAW;IAC5BF,KAAK,EAAEA,KAAM;IACbD,KAAK,EAAE,CAAC;MAAEU;IAAQ,CAAC,EAAEgC,MAAM,CAACO,OAAO,EAAElD,qBAAqB,CAAE;IAC5DmD,SAAS;EAAA,GAERpD,QACM,CACL,CACO,CAAC;AAEpB;AAEA,eAAeN,KAAK;AAEpB,MAAMkD,MAAM,GAAG/D,UAAU,CAACwE,MAAM,CAAC;EAC/BR,QAAQ,EAAE;IACRS,IAAI,EAAE;EACR,CAAC;EACDN,OAAO,EAAE;IACP,GAAGnE,UAAU,CAAC0E,kBAAkB;IAChCC,cAAc,EAAE;EAClB,CAAC;EACD;EACAL,OAAO,EAAE;IACPL,eAAe,EAAE,aAAa;IAC9BU,cAAc,EAAE;EAClB;AACF,CAAC,CAAC", "ignoreList": []}