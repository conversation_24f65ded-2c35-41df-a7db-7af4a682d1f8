export let AdornmentType = /*#__PURE__*/function (AdornmentType) {
  AdornmentType["Icon"] = "icon";
  AdornmentType["Affix"] = "affix";
  return AdornmentType;
}({});
export let AdornmentSide = /*#__PURE__*/function (AdornmentSide) {
  AdornmentSide["Right"] = "right";
  AdornmentSide["Left"] = "left";
  return AdornmentSide;
}({});
export let InputMode = /*#__PURE__*/function (InputMode) {
  InputMode["Outlined"] = "outlined";
  InputMode["Flat"] = "flat";
  return InputMode;
}({});
//# sourceMappingURL=enums.js.map