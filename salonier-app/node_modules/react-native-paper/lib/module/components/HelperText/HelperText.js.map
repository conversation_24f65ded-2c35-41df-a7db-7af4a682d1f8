{"version": 3, "names": ["React", "Animated", "StyleSheet", "getTextColor", "useInternalTheme", "AnimatedText", "HelperText", "style", "type", "visible", "theme", "themeOverrides", "onLayout", "padding", "disabled", "rest", "current", "shown", "useRef", "Value", "textHeight", "scale", "animation", "maxFontSizeMultiplier", "useEffect", "timing", "toValue", "duration", "useNativeDriver", "start", "handleTextLayout", "e", "nativeEvent", "layout", "height", "textColor", "createElement", "_extends", "styles", "text", "color", "opacity", "transform", "translateY", "interpolate", "inputRange", "outputRange", "children", "create", "fontSize", "paddingVertical", "paddingHorizontal"], "sourceRoot": "../../../../src", "sources": ["components/HelperText/HelperText.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EAGRC,UAAU,QAEL,cAAc;AAErB,SAASC,YAAY,QAAQ,SAAS;AACtC,SAASC,gBAAgB,QAAQ,oBAAoB;AAErD,OAAOC,YAAY,MAAM,4BAA4B;AAqCrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAGA,CAAC;EAClBC,KAAK;EACLC,IAAI,GAAG,MAAM;EACbC,OAAO,GAAG,IAAI;EACdC,KAAK,EAAEC,cAAc;EACrBC,QAAQ;EACRC,OAAO,GAAG,QAAQ;EAClBC,QAAQ;EACR,GAAGC;AACE,CAAC,KAAK;EACX,MAAML,KAAK,GAAGN,gBAAgB,CAACO,cAAc,CAAC;EAC9C,MAAM;IAAEK,OAAO,EAAEC;EAAM,CAAC,GAAGjB,KAAK,CAACkB,MAAM,CACrC,IAAIjB,QAAQ,CAACkB,KAAK,CAACV,OAAO,GAAG,CAAC,GAAG,CAAC,CACpC,CAAC;EAED,IAAI;IAAEO,OAAO,EAAEI;EAAW,CAAC,GAAGpB,KAAK,CAACkB,MAAM,CAAS,CAAC,CAAC;EAErD,MAAM;IAAEG;EAAM,CAAC,GAAGX,KAAK,CAACY,SAAS;EAEjC,MAAM;IAAEC,qBAAqB,GAAG;EAAI,CAAC,GAAGR,IAAI;EAE5Cf,KAAK,CAACwB,SAAS,CAAC,MAAM;IACpB,IAAIf,OAAO,EAAE;MACX;MACAR,QAAQ,CAACwB,MAAM,CAACR,KAAK,EAAE;QACrBS,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,GAAG,GAAGN,KAAK;QACrBO,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ,CAAC,MAAM;MACL;MACA5B,QAAQ,CAACwB,MAAM,CAACR,KAAK,EAAE;QACrBS,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,GAAG,GAAGN,KAAK;QACrBO,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACpB,OAAO,EAAEY,KAAK,EAAEJ,KAAK,CAAC,CAAC;EAE3B,MAAMa,gBAAgB,GAAIC,CAAoB,IAAK;IACjDnB,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAGmB,CAAC,CAAC;IACbX,UAAU,GAAGW,CAAC,CAACC,WAAW,CAACC,MAAM,CAACC,MAAM;EAC1C,CAAC;EAED,MAAMC,SAAS,GAAGhC,YAAY,CAAC;IAAEO,KAAK;IAAEI,QAAQ;IAAEN;EAAK,CAAC,CAAC;EAEzD,oBACER,KAAA,CAAAoC,aAAA,CAAC/B,YAAY,EAAAgC,QAAA;IACXzB,QAAQ,EAAEkB,gBAAiB;IAC3BvB,KAAK,EAAE,CACL+B,MAAM,CAACC,IAAI,EACX1B,OAAO,KAAK,MAAM,GAAGyB,MAAM,CAACzB,OAAO,GAAG,CAAC,CAAC,EACxC;MACE2B,KAAK,EAAEL,SAAS;MAChBM,OAAO,EAAExB,KAAK;MACdyB,SAAS,EACPjC,OAAO,IAAID,IAAI,KAAK,OAAO,GACvB,CACE;QACEmC,UAAU,EAAE1B,KAAK,CAAC2B,WAAW,CAAC;UAC5BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CAAC,CAAC1B,UAAU,GAAG,CAAC,EAAE,CAAC;QAClC,CAAC;MACH,CAAC,CACF,GACD;IACR,CAAC,EACDb,KAAK,CACL;IACFgB,qBAAqB,EAAEA;EAAsB,GACzCR,IAAI,GAEPA,IAAI,CAACgC,QACM,CAAC;AAEnB,CAAC;AAED,MAAMT,MAAM,GAAGpC,UAAU,CAAC8C,MAAM,CAAC;EAC/BT,IAAI,EAAE;IACJU,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC;EACDrC,OAAO,EAAE;IACPsC,iBAAiB,EAAE;EACrB;AACF,CAAC,CAAC;AAEF,eAAe7C,UAAU", "ignoreList": []}