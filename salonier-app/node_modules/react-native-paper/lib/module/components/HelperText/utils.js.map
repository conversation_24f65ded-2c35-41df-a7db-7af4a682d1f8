{"version": 3, "names": ["color", "getTextColor", "theme", "disabled", "type", "_theme$colors", "colors", "dark", "error", "isV3", "onSurfaceDisabled", "onSurfaceVariant", "text", "alpha", "rgb", "string"], "sourceRoot": "../../../../src", "sources": ["components/HelperText/utils.ts"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAUzB,OAAO,SAASC,YAAYA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAgB,CAAC,EAAE;EAAA,IAAAC,aAAA;EACjE,MAAM;IAAEC,MAAM;IAAEC;EAAK,CAAC,GAAGL,KAAK;EAE9B,IAAIE,IAAI,KAAK,OAAO,EAAE;IACpB,OAAOE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,KAAK;EACtB;EAEA,IAAIN,KAAK,CAACO,IAAI,EAAE;IACd,IAAIN,QAAQ,EAAE;MACZ,OAAOD,KAAK,CAACI,MAAM,CAACI,iBAAiB;IACvC,CAAC,MAAM;MACL,OAAOR,KAAK,CAACI,MAAM,CAACK,gBAAgB;IACtC;EACF;EAEA,OAAOX,KAAK,CAACE,KAAK,aAALA,KAAK,gBAAAG,aAAA,GAALH,KAAK,CAAEI,MAAM,cAAAD,aAAA,uBAAbA,aAAA,CAAeO,IAAI,CAAC,CAC9BC,KAAK,CAACN,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,CACxBO,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;AACb", "ignoreList": []}