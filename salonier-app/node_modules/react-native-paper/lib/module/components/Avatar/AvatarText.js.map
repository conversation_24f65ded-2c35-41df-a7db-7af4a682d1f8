{"version": 3, "names": ["React", "StyleSheet", "useWindowDimensions", "View", "useInternalTheme", "white", "getContrastingColor", "Text", "defaultSize", "AvatarText", "label", "size", "style", "labelStyle", "color", "customColor", "theme", "themeOverrides", "maxFontSizeMultiplier", "rest", "_theme$colors", "backgroundColor", "colors", "primary", "restStyle", "flatten", "textColor", "fontScale", "createElement", "_extends", "width", "height", "borderRadius", "styles", "container", "text", "fontSize", "lineHeight", "numberOfLines", "displayName", "create", "justifyContent", "alignItems", "textAlign", "textAlignVertical"], "sourceRoot": "../../../../src", "sources": ["components/Avatar/AvatarText.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAEEC,UAAU,EAEVC,mBAAmB,EACnBC,IAAI,QAEC,cAAc;AAErB,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,KAAK,QAAQ,+BAA+B;AAErD,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,OAAOC,IAAI,MAAM,oBAAoB;AAErC,MAAMC,WAAW,GAAG,EAAE;AAiCtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAGA,CAAC;EAClBC,KAAK;EACLC,IAAI,GAAGH,WAAW;EAClBI,KAAK;EACLC,UAAU;EACVC,KAAK,EAAEC,WAAW;EAClBC,KAAK,EAAEC,cAAc;EACrBC,qBAAqB;EACrB,GAAGC;AACE,CAAC,KAAK;EAAA,IAAAC,aAAA;EACX,MAAMJ,KAAK,GAAGZ,gBAAgB,CAACa,cAAc,CAAC;EAC9C,MAAM;IAAEI,eAAe,IAAAD,aAAA,GAAGJ,KAAK,CAACM,MAAM,cAAAF,aAAA,uBAAZA,aAAA,CAAcG,OAAO;IAAE,GAAGC;EAAU,CAAC,GAC7DvB,UAAU,CAACwB,OAAO,CAACb,KAAK,CAAC,IAAI,CAAC,CAAC;EACjC,MAAMc,SAAS,GACbX,WAAW,IACXT,mBAAmB,CAACe,eAAe,EAAEhB,KAAK,EAAE,oBAAoB,CAAC;EACnE,MAAM;IAAEsB;EAAU,CAAC,GAAGzB,mBAAmB,CAAC,CAAC;EAE3C,oBACEF,KAAA,CAAA4B,aAAA,CAACzB,IAAI,EAAA0B,QAAA;IACHjB,KAAK,EAAE,CACL;MACEkB,KAAK,EAAEnB,IAAI;MACXoB,MAAM,EAAEpB,IAAI;MACZqB,YAAY,EAAErB,IAAI,GAAG,CAAC;MACtBU;IACF,CAAC,EACDY,MAAM,CAACC,SAAS,EAChBV,SAAS;EACT,GACEL,IAAI,gBAERnB,KAAA,CAAA4B,aAAA,CAACrB,IAAI;IACHK,KAAK,EAAE,CACLqB,MAAM,CAACE,IAAI,EACX;MACErB,KAAK,EAAEY,SAAS;MAChBU,QAAQ,EAAEzB,IAAI,GAAG,CAAC;MAClB0B,UAAU,EAAE1B,IAAI,GAAGgB;IACrB,CAAC,EACDd,UAAU,CACV;IACFyB,aAAa,EAAE,CAAE;IACjBpB,qBAAqB,EAAEA;EAAsB,GAE5CR,KACG,CACF,CAAC;AAEX,CAAC;AAEDD,UAAU,CAAC8B,WAAW,GAAG,aAAa;AAEtC,MAAMN,MAAM,GAAGhC,UAAU,CAACuC,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDP,IAAI,EAAE;IACJQ,SAAS,EAAE,QAAQ;IACnBC,iBAAiB,EAAE;EACrB;AACF,CAAC,CAAC;AAEF,eAAenC,UAAU", "ignoreList": []}