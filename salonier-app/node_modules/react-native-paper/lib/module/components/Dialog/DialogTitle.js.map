{"version": 3, "names": ["React", "StyleSheet", "useInternalTheme", "Text", "Title", "DialogTitle", "children", "theme", "themeOverrides", "style", "rest", "isV3", "colors", "fonts", "TextComponent", "headerTextStyle", "color", "onSurface", "text", "headlineSmall", "createElement", "_extends", "variant", "accessibilityRole", "styles", "v3Text", "displayName", "create", "marginTop", "marginBottom", "marginHorizontal"], "sourceRoot": "../../../../src", "sources": ["components/Dialog/DialogTitle.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAAoBC,UAAU,QAAmB,cAAc;AAE/D,SAASC,gBAAgB,QAAQ,oBAAoB;AAErD,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,KAAK,MAAM,wBAAwB;AAc1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAGA,CAAC;EACnBC,QAAQ;EACRC,KAAK,EAAEC,cAAc;EACrBC,KAAK;EACL,GAAGC;AACE,CAAC,KAAK;EACX,MAAMH,KAAK,GAAGL,gBAAgB,CAACM,cAAc,CAAC;EAC9C,MAAM;IAAEG,IAAI;IAAEC,MAAM;IAAEC;EAAM,CAAC,GAAGN,KAAK;EAErC,MAAMO,aAAa,GAAGH,IAAI,GAAGR,IAAI,GAAGC,KAAK;EAEzC,MAAMW,eAAe,GAAG;IACtBC,KAAK,EAAEL,IAAI,GAAGC,MAAM,CAACK,SAAS,GAAGL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEM,IAAI;IAC7C,IAAIP,IAAI,GAAGE,KAAK,CAACM,aAAa,GAAG,CAAC,CAAC;EACrC,CAAC;EAED,oBACEnB,KAAA,CAAAoB,aAAA,CAACN,aAAa,EAAAO,QAAA;IACZC,OAAO,EAAC,eAAe;IACvBC,iBAAiB,EAAC,QAAQ;IAC1Bd,KAAK,EAAE,CAACe,MAAM,CAACN,IAAI,EAAEP,IAAI,IAAIa,MAAM,CAACC,MAAM,EAAEV,eAAe,EAAEN,KAAK;EAAE,GAChEC,IAAI,GAEPJ,QACY,CAAC;AAEpB,CAAC;AAEDD,WAAW,CAACqB,WAAW,GAAG,cAAc;AAExC,MAAMF,MAAM,GAAGvB,UAAU,CAAC0B,MAAM,CAAC;EAC/BT,IAAI,EAAE;IACJU,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,gBAAgB,EAAE;EACpB,CAAC;EACDL,MAAM,EAAE;IACNG,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE;EAChB;AACF,CAAC,CAAC;AAEF,eAAexB,WAAW;;AAE1B;AACA,SAASA,WAAW", "ignoreList": []}