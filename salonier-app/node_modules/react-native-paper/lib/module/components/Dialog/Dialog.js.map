{"version": 3, "names": ["React", "Platform", "StyleSheet", "useSafeAreaInsets", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogIcon", "DialogScrollArea", "DialogTitle", "useInternalTheme", "overlay", "Modal", "DIALOG_ELEVATION", "Dialog", "children", "dismissable", "dismissable<PERSON><PERSON><PERSON><PERSON><PERSON>", "on<PERSON><PERSON><PERSON>", "visible", "style", "theme", "themeOverrides", "testID", "right", "left", "isV3", "dark", "mode", "colors", "roundness", "borderRadius", "backgroundColorV2", "surface", "backgroundColor", "elevation", "level3", "createElement", "contentContainerStyle", "marginHorizontal", "Math", "max", "styles", "container", "Children", "toArray", "filter", "child", "map", "i", "isValidElement", "cloneElement", "marginTop", "props", "type", "paddingTop", "Content", "Actions", "Title", "ScrollArea", "Icon", "create", "marginVertical", "OS", "justifyContent"], "sourceRoot": "../../../../src", "sources": ["components/Dialog/Dialog.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAEEC,QAAQ,EAERC,UAAU,QAEL,cAAc;AAErB,SAASC,iBAAiB,QAAQ,gCAAgC;AAElE,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,OAAOC,OAAO,MAAM,sBAAsB;AAE1C,OAAOC,KAAK,MAAM,UAAU;AAmC5B,MAAMC,gBAAwB,GAAG,EAAE;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAGA,CAAC;EACdC,QAAQ;EACRC,WAAW,GAAG,IAAI;EAClBC,qBAAqB,GAAGD,WAAW;EACnCE,SAAS;EACTC,OAAO,GAAG,KAAK;EACfC,KAAK;EACLC,KAAK,EAAEC,cAAc;EACrBC;AACK,CAAC,KAAK;EACX,MAAM;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAGrB,iBAAiB,CAAC,CAAC;EAC3C,MAAMiB,KAAK,GAAGX,gBAAgB,CAACY,cAAc,CAAC;EAC9C,MAAM;IAAEI,IAAI;IAAEC,IAAI;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAU,CAAC,GAAGT,KAAK;EACrD,MAAMU,YAAY,GAAG,CAACL,IAAI,GAAG,CAAC,GAAG,CAAC,IAAII,SAAS;EAE/C,MAAME,iBAAiB,GACrBL,IAAI,IAAIC,IAAI,KAAK,UAAU,GACvBjB,OAAO,CAACE,gBAAgB,EAAEgB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,OAAO,CAAC,GAC1CJ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,OAAO;EACrB,MAAMC,eAAe,GAAGR,IAAI,GACxBL,KAAK,CAACQ,MAAM,CAACM,SAAS,CAACC,MAAM,GAC7BJ,iBAAiB;EAErB,oBACE/B,KAAA,CAAAoC,aAAA,CAACzB,KAAK;IACJI,WAAW,EAAEA,WAAY;IACzBC,qBAAqB,EAAEA,qBAAsB;IAC7CC,SAAS,EAAEA,SAAU;IACrBC,OAAO,EAAEA,OAAQ;IACjBmB,qBAAqB,EAAE,CACrB;MACEP,YAAY;MACZG,eAAe;MACfK,gBAAgB,EAAEC,IAAI,CAACC,GAAG,CAAChB,IAAI,EAAED,KAAK,EAAE,EAAE;IAC5C,CAAC,EACDkB,MAAM,CAACC,SAAS,EAChBvB,KAAK,CACL;IACFC,KAAK,EAAEA,KAAM;IACbE,MAAM,EAAEA;EAAO,GAEdtB,KAAK,CAAC2C,QAAQ,CAACC,OAAO,CAAC9B,QAAQ,CAAC,CAC9B+B,MAAM,CAAEC,KAAK,IAAKA,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,SAAS,CAAC,CAC9DC,GAAG,CAAC,CAACD,KAAK,EAAEE,CAAC,KAAK;IACjB,IAAIvB,IAAI,EAAE;MACR,IAAIuB,CAAC,KAAK,CAAC,iBAAIhD,KAAK,CAACiD,cAAc,CAAmBH,KAAK,CAAC,EAAE;QAC5D,oBAAO9C,KAAK,CAACkD,YAAY,CAACJ,KAAK,EAAE;UAC/B3B,KAAK,EAAE,CAAC;YAAEgC,SAAS,EAAE;UAAG,CAAC,EAAEL,KAAK,CAACM,KAAK,CAACjC,KAAK;QAC9C,CAAC,CAAC;MACJ;IACF;IAEA,IACE6B,CAAC,KAAK,CAAC,iBACPhD,KAAK,CAACiD,cAAc,CAAmBH,KAAK,CAAC,IAC7CA,KAAK,CAACO,IAAI,KAAKhD,aAAa,EAC5B;MACA;MACA,oBAAOL,KAAK,CAACkD,YAAY,CAACJ,KAAK,EAAE;QAC/B3B,KAAK,EAAE,CAAC;UAAEmC,UAAU,EAAE;QAAG,CAAC,EAAER,KAAK,CAACM,KAAK,CAACjC,KAAK;MAC/C,CAAC,CAAC;IACJ;IAEA,OAAO2B,KAAK;EACd,CAAC,CACE,CAAC;AAEZ,CAAC;;AAED;AACAjC,MAAM,CAAC0C,OAAO,GAAGlD,aAAa;AAC9B;AACAQ,MAAM,CAAC2C,OAAO,GAAGpD,aAAa;AAC9B;AACAS,MAAM,CAAC4C,KAAK,GAAGjD,WAAW;AAC1B;AACAK,MAAM,CAAC6C,UAAU,GAAGnD,gBAAgB;AACpC;AACAM,MAAM,CAAC8C,IAAI,GAAGrD,UAAU;AAExB,MAAMmC,MAAM,GAAGvC,UAAU,CAAC0D,MAAM,CAAC;EAC/BlB,SAAS,EAAE;IACT;AACJ;AACA;AACA;AACA;AACA;AACA;IACImB,cAAc,EAAE5D,QAAQ,CAAC6D,EAAE,KAAK,SAAS,GAAG,EAAE,GAAG,CAAC;IAClD5B,SAAS,EAAEtB,gBAAgB;IAC3BmD,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,eAAelD,MAAM", "ignoreList": []}