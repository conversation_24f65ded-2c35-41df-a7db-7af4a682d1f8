{"version": 3, "names": ["React", "Platform", "StyleSheet", "View", "Pressable", "getTouchableRippleColors", "SettingsContext", "useInternalTheme", "forwardRef", "has<PERSON>ou<PERSON><PERSON><PERSON><PERSON>", "ANDROID_VERSION_LOLLIPOP", "ANDROID_VERSION_PIE", "TouchableRipple", "style", "background", "borderless", "disabled", "disabledProp", "rippleColor", "underlayColor", "children", "theme", "themeOverrides", "rest", "ref", "rippleEffectEnabled", "useContext", "onPress", "onLongPress", "onPressIn", "onPressOut", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "calculatedRippleColor", "calculatedUnderlayColor", "useForeground", "OS", "Version", "supported", "androidRipple", "color", "foreground", "undefined", "createElement", "_extends", "styles", "overflowHidden", "android_ripple", "Children", "only", "pressed", "Fragment", "testID", "underlay", "backgroundColor", "create", "overflow", "absoluteFillObject", "zIndex", "Component"], "sourceRoot": "../../../../src", "sources": ["components/TouchableRipple/TouchableRipple.native.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAGEC,QAAQ,EAERC,UAAU,EAEVC,IAAI,QAEC,cAAc;AAGrB,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,wBAAwB,QAAQ,SAAS;AAClD,SAAmBC,eAAe,QAAQ,qBAAqB;AAC/D,SAASC,gBAAgB,QAAQ,oBAAoB;AAErD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,OAAOC,eAAe,MAAM,6BAA6B;AAEzD,MAAMC,wBAAwB,GAAG,EAAE;AACnC,MAAMC,mBAAmB,GAAG,EAAE;AAkB9B,MAAMC,eAAe,GAAGA,CACtB;EACEC,KAAK;EACLC,UAAU;EACVC,UAAU,GAAG,KAAK;EAClBC,QAAQ,EAAEC,YAAY;EACtBC,WAAW;EACXC,aAAa;EACbC,QAAQ;EACRC,KAAK,EAAEC,cAAc;EACrB,GAAGC;AACE,CAAC,EACRC,GAA6B,KAC1B;EACH,MAAMH,KAAK,GAAGd,gBAAgB,CAACe,cAAc,CAAC;EAC9C,MAAM;IAAEG;EAAoB,CAAC,GAAGzB,KAAK,CAAC0B,UAAU,CAAWpB,eAAe,CAAC;EAE3E,MAAM;IAAEqB,OAAO;IAAEC,WAAW;IAAEC,SAAS;IAAEC;EAAW,CAAC,GAAGP,IAAI;EAE5D,MAAMQ,qBAAqB,GAAGtB,eAAe,CAAC;IAC5CkB,OAAO;IACPC,WAAW;IACXC,SAAS;IACTC;EACF,CAAC,CAAC;EAEF,MAAMd,QAAQ,GAAGC,YAAY,IAAI,CAACc,qBAAqB;EAEvD,MAAM;IAAEC,qBAAqB;IAAEC;EAAwB,CAAC,GACtD5B,wBAAwB,CAAC;IACvBgB,KAAK;IACLH,WAAW;IACXC;EACF,CAAC,CAAC;;EAEJ;EACA;EACA,MAAMe,aAAa,GACjBjC,QAAQ,CAACkC,EAAE,KAAK,SAAS,IACzBlC,QAAQ,CAACmC,OAAO,IAAIzB,mBAAmB,IACvCI,UAAU;EAEZ,IAAIH,eAAe,CAACyB,SAAS,EAAE;IAC7B,MAAMC,aAAa,GAAGb,mBAAmB,GACrCX,UAAU,IAAI;MACZyB,KAAK,EAAEP,qBAAqB;MAC5BjB,UAAU;MACVyB,UAAU,EAAEN;IACd,CAAC,GACDO,SAAS;IAEb,oBACEzC,KAAA,CAAA0C,aAAA,CAACtC,SAAS,EAAAuC,QAAA,KACJpB,IAAI;MACRC,GAAG,EAAEA,GAAI;MACTR,QAAQ,EAAEA,QAAS;MACnBH,KAAK,EAAE,CAACE,UAAU,IAAI6B,MAAM,CAACC,cAAc,EAAEhC,KAAK,CAAE;MACpDiC,cAAc,EAAER;IAAc,IAE7BtC,KAAK,CAAC+C,QAAQ,CAACC,IAAI,CAAC5B,QAAQ,CACpB,CAAC;EAEhB;EAEA,oBACEpB,KAAA,CAAA0C,aAAA,CAACtC,SAAS,EAAAuC,QAAA,KACJpB,IAAI;IACRC,GAAG,EAAEA,GAAI;IACTR,QAAQ,EAAEA,QAAS;IACnBH,KAAK,EAAE,CAACE,UAAU,IAAI6B,MAAM,CAACC,cAAc,EAAEhC,KAAK;EAAE,IAEnD,CAAC;IAAEoC;EAAQ,CAAC,kBACXjD,KAAA,CAAA0C,aAAA,CAAA1C,KAAA,CAAAkD,QAAA,QACGD,OAAO,IAAIxB,mBAAmB,iBAC7BzB,KAAA,CAAA0C,aAAA,CAACvC,IAAI;IACHgD,MAAM,EAAC,2BAA2B;IAClCtC,KAAK,EAAE,CACL+B,MAAM,CAACQ,QAAQ,EACf;MAAEC,eAAe,EAAEpB;IAAwB,CAAC;EAC5C,CACH,CACF,EACAjC,KAAK,CAAC+C,QAAQ,CAACC,IAAI,CAAC5B,QAAQ,CAC7B,CAEK,CAAC;AAEhB,CAAC;AAEDR,eAAe,CAACyB,SAAS,GACvBpC,QAAQ,CAACkC,EAAE,KAAK,SAAS,IAAIlC,QAAQ,CAACmC,OAAO,IAAI1B,wBAAwB;AAE3E,MAAMkC,MAAM,GAAG1C,UAAU,CAACoD,MAAM,CAAC;EAC/BT,cAAc,EAAE;IACdU,QAAQ,EAAE;EACZ,CAAC;EACDH,QAAQ,EAAE;IACR,GAAGlD,UAAU,CAACsD,kBAAkB;IAChCC,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AAEF,MAAMC,SAAS,GAAGlD,UAAU,CAACI,eAAe,CAAC;AAE7C,eAAe8C,SAAS", "ignoreList": []}