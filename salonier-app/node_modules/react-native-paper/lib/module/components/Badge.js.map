{"version": 3, "names": ["React", "Animated", "StyleSheet", "useWindowDimensions", "useInternalTheme", "black", "white", "getContrastingColor", "defaultSize", "Badge", "children", "size", "style", "theme", "themeOverrides", "visible", "rest", "_theme$colors", "current", "opacity", "useRef", "Value", "fontScale", "isFirstRendering", "animation", "scale", "useEffect", "timing", "toValue", "duration", "useNativeDriver", "start", "backgroundColor", "isV3", "colors", "error", "notification", "restStyle", "flatten", "textColor", "onError", "borderRadius", "paddingHorizontal", "createElement", "Text", "_extends", "numberOfLines", "color", "fontSize", "fonts", "regular", "lineHeight", "height", "min<PERSON><PERSON><PERSON>", "styles", "container", "create", "alignSelf", "textAlign", "textAlignVertical", "overflow"], "sourceRoot": "../../../src", "sources": ["components/Badge.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EAERC,UAAU,EAEVC,mBAAmB,QACd,cAAc;AAErB,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,SAASC,KAAK,EAAEC,KAAK,QAAQ,4BAA4B;AAEzD,OAAOC,mBAAmB,MAAM,8BAA8B;AAE9D,MAAMC,WAAW,GAAG,EAAE;AAuBtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,KAAK,GAAGA,CAAC;EACbC,QAAQ;EACRC,IAAI,GAAGH,WAAW;EAClBI,KAAK;EACLC,KAAK,EAAEC,cAAc;EACrBC,OAAO,GAAG,IAAI;EACd,GAAGC;AACE,CAAC,KAAK;EAAA,IAAAC,aAAA;EACX,MAAMJ,KAAK,GAAGT,gBAAgB,CAACU,cAAc,CAAC;EAC9C,MAAM;IAAEI,OAAO,EAAEC;EAAQ,CAAC,GAAGnB,KAAK,CAACoB,MAAM,CACvC,IAAInB,QAAQ,CAACoB,KAAK,CAACN,OAAO,GAAG,CAAC,GAAG,CAAC,CACpC,CAAC;EACD,MAAM;IAAEO;EAAU,CAAC,GAAGnB,mBAAmB,CAAC,CAAC;EAE3C,MAAMoB,gBAAgB,GAAGvB,KAAK,CAACoB,MAAM,CAAU,IAAI,CAAC;EAEpD,MAAM;IACJI,SAAS,EAAE;MAAEC;IAAM;EACrB,CAAC,GAAGZ,KAAK;EAETb,KAAK,CAAC0B,SAAS,CAAC,MAAM;IACpB;IACA,IAAIH,gBAAgB,CAACL,OAAO,EAAE;MAC5BK,gBAAgB,CAACL,OAAO,GAAG,KAAK;MAChC;IACF;IAEAjB,QAAQ,CAAC0B,MAAM,CAACR,OAAO,EAAE;MACvBS,OAAO,EAAEb,OAAO,GAAG,CAAC,GAAG,CAAC;MACxBc,QAAQ,EAAE,GAAG,GAAGJ,KAAK;MACrBK,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAAChB,OAAO,EAAEI,OAAO,EAAEM,KAAK,CAAC,CAAC;EAE7B,MAAM;IACJO,eAAe,GAAGnB,KAAK,CAACoB,IAAI,GACxBpB,KAAK,CAACqB,MAAM,CAACC,KAAK,IAAAlB,aAAA,GAClBJ,KAAK,CAACqB,MAAM,cAAAjB,aAAA,uBAAZA,aAAA,CAAcmB,YAAY;IAC9B,GAAGC;EACL,CAAC,GAAInC,UAAU,CAACoC,OAAO,CAAC1B,KAAK,CAAC,IAAI,CAAC,CAAe;EAElD,MAAM2B,SAAS,GAAG1B,KAAK,CAACoB,IAAI,GACxBpB,KAAK,CAACqB,MAAM,CAACM,OAAO,GACpBjC,mBAAmB,CAACyB,eAAe,EAAE1B,KAAK,EAAED,KAAK,CAAC;EAEtD,MAAMoC,YAAY,GAAG9B,IAAI,GAAG,CAAC;EAE7B,MAAM+B,iBAAiB,GAAG7B,KAAK,CAACoB,IAAI,GAAG,CAAC,GAAG,CAAC;EAE5C,oBACEjC,KAAA,CAAA2C,aAAA,CAAC1C,QAAQ,CAAC2C,IAAI,EAAAC,QAAA;IACZC,aAAa,EAAE,CAAE;IACjBlC,KAAK,EAAE,CACL;MACEO,OAAO;MACPa,eAAe;MACfe,KAAK,EAAER,SAAS;MAChBS,QAAQ,EAAErC,IAAI,GAAG,GAAG;MACpB,IAAI,CAACE,KAAK,CAACoB,IAAI,IAAIpB,KAAK,CAACoC,KAAK,CAACC,OAAO,CAAC;MACvCC,UAAU,EAAExC,IAAI,GAAGW,SAAS;MAC5B8B,MAAM,EAAEzC,IAAI;MACZ0C,QAAQ,EAAE1C,IAAI;MACd8B,YAAY;MACZC;IACF,CAAC,EACDY,MAAM,CAACC,SAAS,EAChBlB,SAAS;EACT,GACErB,IAAI,GAEPN,QACY,CAAC;AAEpB,CAAC;AAED,eAAeD,KAAK;AAEpB,MAAM6C,MAAM,GAAGpD,UAAU,CAACsD,MAAM,CAAC;EAC/BD,SAAS,EAAE;IACTE,SAAS,EAAE,UAAU;IACrBC,SAAS,EAAE,QAAQ;IACnBC,iBAAiB,EAAE,QAAQ;IAC3BC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}