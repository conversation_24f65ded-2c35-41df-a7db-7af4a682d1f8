{"version": 3, "names": ["React", "StyleSheet", "View", "DataTableCell", "DataTableHeader", "DataTablePagination", "DataTableRow", "DataTableTitle", "DataTable", "children", "style", "rest", "createElement", "_extends", "styles", "container", "Header", "Title", "Row", "Cell", "Pagination", "create", "width"], "sourceRoot": "../../../../src", "sources": ["components/DataTable/DataTable.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAaC,IAAI,QAAmB,cAAc;AAErE,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC;AACL;AAAA,KAEK,mBAAmB;AAC1B,OAAOC;AACL;AAAA,KAEK,uBAAuB;AAC9B;AACA,OAAOC,YAAY,MAAyC,gBAAgB;AAC5E,OAAOC;AACL;AAAA,KAEK,kBAAkB;AAUzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,KAAK;EAAE,GAAGC;AAAY,CAAC,kBACpDX,KAAA,CAAAY,aAAA,CAACV,IAAI,EAAAW,QAAA,KAAKF,IAAI;EAAED,KAAK,EAAE,CAACI,MAAM,CAACC,SAAS,EAAEL,KAAK;AAAE,IAC9CD,QACG,CACP;;AAED;AACAD,SAAS,CAACQ,MAAM,GAAGZ,eAAe;;AAElC;AACAI,SAAS,CAACS,KAAK,GAAGV,cAAc;;AAEhC;AACAC,SAAS,CAACU,GAAG,GAAGZ,YAAY;;AAE5B;AACAE,SAAS,CAACW,IAAI,GAAGhB,aAAa;;AAE9B;AACAK,SAAS,CAACY,UAAU,GAAGf,mBAAmB;AAE1C,MAAMS,MAAM,GAAGb,UAAU,CAACoB,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,KAAK,EAAE;EACT;AACF,CAAC,CAAC;AAEF,eAAed,SAAS", "ignoreList": []}