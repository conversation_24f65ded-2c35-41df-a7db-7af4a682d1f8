{"version": 3, "names": ["color", "black", "pinkA100", "white", "MD2LightTheme", "configure<PERSON>onts", "MD2DarkTheme", "dark", "mode", "version", "isV3", "colors", "primary", "accent", "background", "surface", "error", "onSurface", "text", "disabled", "alpha", "rgb", "string", "placeholder", "backdrop", "notification", "tooltip", "fonts"], "sourceRoot": "../../../../../src", "sources": ["styles/themes/v2/DarkTheme.tsx"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,KAAK,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,UAAU;AACjD,SAASC,aAAa,QAAQ,cAAc;AAE5C,OAAOC,cAAc,MAAM,aAAa;AAExC,OAAO,MAAMC,YAAsB,GAAG;EACpC,GAAGF,aAAa;EAChBG,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,UAAU;EAChBC,OAAO,EAAE,CAAC;EACVC,IAAI,EAAE,KAAK;EACXC,MAAM,EAAE;IACN,GAAGP,aAAa,CAACO,MAAM;IACvBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,SAAS;IACpBC,IAAI,EAAEf,KAAK;IACXgB,QAAQ,EAAEnB,KAAK,CAACG,KAAK,CAAC,CAACiB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACjDC,WAAW,EAAEvB,KAAK,CAACG,KAAK,CAAC,CAACiB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACpDE,QAAQ,EAAExB,KAAK,CAACC,KAAK,CAAC,CAACmB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IAChDG,YAAY,EAAEvB,QAAQ;IACtBwB,OAAO,EAAE;EACX,CAAC;EACDC,KAAK,EAAEtB,cAAc,CAAC;IAAEK,IAAI,EAAE;EAAM,CAAC;AACvC,CAAC", "ignoreList": []}