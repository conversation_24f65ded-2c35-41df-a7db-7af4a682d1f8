/* eslint-disable prettier/prettier */
import PaperProvider from "react-native-paper/lib/module/core/PaperProvider";
import BottomNavigation from "react-native-paper/lib/module/components/BottomNavigation/BottomNavigation";
import Button from "react-native-paper/lib/module/components/Button/Button";
import FAB from "react-native-paper/lib/module/components/FAB";
import Appbar from "react-native-paper/lib/module/components/Appbar";
import * as MD2Colors from "react-native-paper/lib/module/styles/themes/v2/colors";
import { MD3Colors } from "react-native-paper/lib/module/styles/themes/v3/tokens";
import { NonExistent, NonExistentSecond as Stuff } from "react-native-paper/lib/module/index.js";
import { ThemeProvider } from "react-native-paper/lib/module/core/theming";
import { withTheme } from "react-native-paper/lib/module/core/theming";
import { DefaultTheme } from "react-native-paper/lib/module/core/theming";
