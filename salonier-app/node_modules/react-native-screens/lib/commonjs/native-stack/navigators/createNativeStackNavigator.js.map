{"version": 3, "names": ["_native", "require", "React", "_interopRequireWildcard", "_NativeStackView", "_interopRequireDefault", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_extends", "assign", "bind", "arguments", "length", "apply", "NativeStackNavigator", "initialRouteName", "children", "screenOptions", "rest", "state", "descriptors", "navigation", "useNavigationBuilder", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect", "dangerouslyGetParent", "undefined", "console", "warn", "addListener", "isFocused", "requestAnimationFrame", "index", "defaultPrevented", "dispatch", "StackActions", "popToTop", "target", "key", "createElement", "_default", "exports", "createNavigatorFactory"], "sourceRoot": "../../../../src", "sources": ["native-stack/navigators/createNativeStackNavigator.tsx"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAWA,IAAAC,KAAA,GAAAC,uBAAA,CAAAF,OAAA;AAMA,IAAAG,gBAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAAuD,SAAAI,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAH,wBAAAG,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA,OAAAS,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA,YAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAAA,SAAAW,SAAA,WAAAA,QAAA,GAAAR,MAAA,CAAAS,MAAA,GAAAT,MAAA,CAAAS,MAAA,CAAAC,IAAA,eAAAb,CAAA,aAAAT,CAAA,MAAAA,CAAA,GAAAuB,SAAA,CAAAC,MAAA,EAAAxB,CAAA,UAAAM,CAAA,GAAAiB,SAAA,CAAAvB,CAAA,YAAAK,CAAA,IAAAC,CAAA,OAAAU,cAAA,CAAAC,IAAA,CAAAX,CAAA,EAAAD,CAAA,MAAAI,CAAA,CAAAJ,CAAA,IAAAC,CAAA,CAAAD,CAAA,aAAAI,CAAA,KAAAW,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAEvD,SAASG,oBAAoBA,CAAC;EAC5BC,gBAAgB;EAChBC,QAAQ;EACRC,aAAa;EACb,GAAGC;AACsB,CAAC,EAAE;EAC5B,MAAM;IAAEC,KAAK;IAAEC,WAAW;IAAEC;EAAW,CAAC,GAAG,IAAAC,4BAAoB,EAM7DC,mBAAW,EAAE;IACbR,gBAAgB;IAChBC,QAAQ;IACRC;EACF,CAAC,CAAC;;EAEF;EACA;EACAjC,KAAK,CAACwC,SAAS,CAAC,MAAM;IACpB;IACA,IAAIH,UAAU,EAAEI,oBAAoB,KAAKC,SAAS,EAAE;MAClDC,OAAO,CAACC,IAAI,CACV,2LACF,CAAC;IACH;EACF,CAAC,EAAE,CAACP,UAAU,CAAC,CAAC;EAEhBrC,KAAK,CAACwC,SAAS,CACb;EACE;EACCH,UAAU,EAA+CQ,WAAW,GACnE,UAAU,EACTzC,CAAM,IAAK;IACV,MAAM0C,SAAS,GAAGT,UAAU,CAACS,SAAS,CAAC,CAAC;;IAExC;IACA;IACAC,qBAAqB,CAAC,MAAM;MAC1B,IACEZ,KAAK,CAACa,KAAK,GAAG,CAAC,IACfF,SAAS,IACT,CAAE1C,CAAC,CAAgC6C,gBAAgB,EACnD;QACA;QACA;QACAZ,UAAU,CAACa,QAAQ,CAAC;UAClB,GAAGC,oBAAY,CAACC,QAAQ,CAAC,CAAC;UAC1BC,MAAM,EAAElB,KAAK,CAACmB;QAChB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CACF,CAAC,EACH,CAACjB,UAAU,EAAEF,KAAK,CAACa,KAAK,EAAEb,KAAK,CAACmB,GAAG,CACrC,CAAC;EAED,oBACEtD,KAAA,CAAAuD,aAAA,CAACrD,gBAAA,CAAAI,OAAe,EAAAkB,QAAA,KACVU,IAAI;IACRC,KAAK,EAAEA,KAAM;IACbE,UAAU,EAAEA,UAAW;IACvBD,WAAW,EAAEA;EAAY,EAC1B,CAAC;AAEN;;AAEA;AACA;AACA;AAFA,IAAAoB,QAAA,GAAAC,OAAA,CAAAnD,OAAA,GAGe,IAAAoD,8BAAsB,EAKnC5B,oBAAoB,CAAC", "ignoreList": []}