{"version": 3, "names": ["_reactNativeIsEdgeToEdge", "require", "EDGE_TO_EDGE", "exports", "isEdgeToEdge", "transformEdgeToEdgeProps", "props", "statusBarColor", "statusBarTranslucent", "navigationBarColor", "navigationBarTranslucent", "rest", "__DEV__", "controlEdgeToEdgeValues"], "sourceRoot": "../../../../src", "sources": ["components/helpers/edge-to-edge.tsx"], "mappings": ";;;;;;;AAAA,IAAAA,wBAAA,GAAAC,OAAA;AAMO,MAAMC,YAAY,GAAAC,OAAA,CAAAD,YAAA,GAAG,IAAAE,qCAAY,EAAC,CAAC;AAEnC,SAASC,wBAAwBA,CAACC,KAAkB,EAAe;EACxE,MAAM;IACJ;IACAC,cAAc;IACdC,oBAAoB;IACpBC,kBAAkB;IAClBC,wBAAwB;IACxB,GAAGC;EACL,CAAC,GAAGL,KAAK;EAET,IAAIM,OAAO,EAAE;IACX,IAAAC,gDAAuB,EAAC;MACtBN,cAAc;MACdC,oBAAoB;MACpBC,kBAAkB;MAClBC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOC,IAAI;AACb", "ignoreList": []}