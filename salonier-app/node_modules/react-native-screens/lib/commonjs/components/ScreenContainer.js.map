{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_reactNative", "require", "_react", "_interopRequireDefault", "_core", "_ScreenContainerNativeComponent", "_ScreenNavigationContainerNativeComponent", "e", "__esModule", "ScreenContainer", "props", "enabled", "screensEnabled", "hasTwoStates", "rest", "isNativePlatformSupported", "ScreenNavigationContainer", "Platform", "OS", "ScreenNavigationContainerNativeComponent", "ScreenContainerNativeComponent", "createElement", "View", "_default"], "sourceRoot": "../../../src", "sources": ["components/ScreenContainer.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAEb,IAAAC,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,KAAA,GAAAH,OAAA;AAGA,IAAAI,+BAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,yCAAA,GAAAH,sBAAA,CAAAF,OAAA;AAA0G,SAAAE,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAR,OAAA,EAAAQ,CAAA;AAF1G;;AAIA,SAASE,eAAeA,CAACC,KAA2B,EAAE;EACpD,MAAM;IAAEC,OAAO,GAAG,IAAAC,oBAAc,EAAC,CAAC;IAAEC,YAAY;IAAE,GAAGC;EAAK,CAAC,GAAGJ,KAAK;EAEnE,IAAIC,OAAO,IAAII,+BAAyB,EAAE;IACxC,IAAIF,YAAY,EAAE;MAChB,MAAMG,yBAAyB,GAC7BC,qBAAQ,CAACC,EAAE,KAAK,KAAK,GACjBC,iDAAwC,GACxCC,uCAA8B;MACpC,oBAAOlB,MAAA,CAAAH,OAAA,CAAAsB,aAAA,CAACL,yBAAyB,EAAKF,IAAO,CAAC;IAChD;IACA,oBAAOZ,MAAA,CAAAH,OAAA,CAAAsB,aAAA,CAAChB,+BAAA,CAAAN,OAA8B,EAAKe,IAAO,CAAC;EACrD;EACA,oBAAOZ,MAAA,CAAAH,OAAA,CAAAsB,aAAA,CAACrB,YAAA,CAAAsB,IAAI,EAAKR,IAAO,CAAC;AAC3B;AAAC,IAAAS,QAAA,GAAA1B,OAAA,CAAAE,OAAA,GAEcU,eAAe", "ignoreList": []}