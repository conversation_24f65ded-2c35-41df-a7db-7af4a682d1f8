{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_Screen", "_reactNativeReanimated", "e", "__esModule", "default", "_extends", "Object", "assign", "bind", "n", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "AnimatedScreen", "Animated", "createAnimatedComponent", "InnerScreen", "ReanimatedScreen", "React", "forwardRef", "props", "ref", "createElement", "displayName", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["reanimated/ReanimatedScreen.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAIA,IAAAE,sBAAA,GAAAH,sBAAA,CAAAC,OAAA;AAA+C,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,SAAA,WAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAP,CAAA,MAAAA,CAAA,GAAAQ,SAAA,CAAAC,MAAA,EAAAT,CAAA,UAAAU,CAAA,GAAAF,SAAA,CAAAR,CAAA,YAAAW,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAJ,CAAA,CAAAI,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAJ,CAAA,KAAAJ,QAAA,CAAAW,KAAA,OAAAN,SAAA,KAD/C;AAGA,MAAMO,cAAc,GAAGC,8BAAQ,CAACC,uBAAuB,CACrDC,mBACF,CAAC;AAED,MAAMC,gBAAgB,gBAAGC,cAAK,CAACC,UAAU,CACvC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACd,oBACE5B,MAAA,CAAAO,OAAA,CAAAsB,aAAA,CAACT;EACC;EAAA,EAAAZ,QAAA;IACAoB,GAAG,EAAEA;EAAI,GACLD,KAAK,CACV,CAAC;AAEN,CACF,CAAC;AAEDH,gBAAgB,CAACM,WAAW,GAAG,kBAAkB;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAzB,OAAA,GAEnCiB,gBAAgB", "ignoreList": []}