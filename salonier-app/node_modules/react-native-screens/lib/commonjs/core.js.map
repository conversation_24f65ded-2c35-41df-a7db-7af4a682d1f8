{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "enableFreeze", "enableScreens", "freezeEnabled", "isNativePlatformSupported", "screensEnabled", "_reactNative", "require", "Platform", "OS", "ENABLE_SCREENS", "shouldEnableScreens", "UIManager", "getViewManagerConfig", "console", "error", "ENABLE_FREEZE", "shouldEnableReactFreeze"], "sourceRoot": "../../src", "sources": ["core.ts"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,YAAA,GAAAA,YAAA;AAAAF,OAAA,CAAAG,aAAA,GAAAA,aAAA;AAAAH,OAAA,CAAAI,aAAA,GAAAA,aAAA;AAAAJ,OAAA,CAAAK,yBAAA;AAAAL,OAAA,CAAAM,cAAA,GAAAA,cAAA;AAEb,IAAAC,YAAA,GAAAC,OAAA;AAEO,MAAMH,yBAAyB,GAAAL,OAAA,CAAAK,yBAAA,GACpCI,qBAAQ,CAACC,EAAE,KAAK,KAAK,IACrBD,qBAAQ,CAACC,EAAE,KAAK,SAAS,IACzBD,qBAAQ,CAACC,EAAE,KAAK,SAAS;AAE3B,IAAIC,cAAc,GAAGN,yBAAyB;AAEvC,SAASF,aAAaA,CAACS,mBAAmB,GAAG,IAAI,EAAE;EACxDD,cAAc,GAAGC,mBAAmB;EAEpC,IAAI,CAACP,yBAAyB,EAAE;IAC9B;EACF;EAEA,IAAIM,cAAc,IAAI,CAACE,sBAAS,CAACC,oBAAoB,CAAC,WAAW,CAAC,EAAE;IAClEC,OAAO,CAACC,KAAK,CACX,wGACF,CAAC;EACH;AACF;AAEA,IAAIC,aAAa,GAAG,KAAK;AAElB,SAASf,YAAYA,CAACgB,uBAAuB,GAAG,IAAI,EAAE;EAC3D,IAAI,CAACb,yBAAyB,EAAE;IAC9B;EACF;EAEAY,aAAa,GAAGC,uBAAuB;AACzC;AAEO,SAASZ,cAAcA,CAAA,EAAG;EAC/B,OAAOK,cAAc;AACvB;AAEO,SAASP,aAAaA,CAAA,EAAG;EAC9B,OAAOa,aAAa;AACtB", "ignoreList": []}