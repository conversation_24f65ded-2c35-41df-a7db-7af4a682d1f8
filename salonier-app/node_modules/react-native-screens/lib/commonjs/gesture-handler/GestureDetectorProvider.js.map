{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_contexts", "_ScreenGestureDetector", "e", "__esModule", "default", "GHWrapper", "props", "createElement", "GestureDetectorProvider", "GHContext", "Provider", "value", "children"], "sourceRoot": "../../../src", "sources": ["gesture-handler/GestureDetectorProvider.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,sBAAA,GAAAH,sBAAA,CAAAC,OAAA;AAA4D,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE5D,SAASG,SAASA,CAACC,KAA2B,EAAE;EAC9C,oBAAOT,MAAA,CAAAO,OAAA,CAAAG,aAAA,CAACN,sBAAA,CAAAG,OAAqB,EAAKE,KAAQ,CAAC;AAC7C;AAEe,SAASE,uBAAuBA,CAACF,KAE/C,EAAE;EACD,oBACET,MAAA,CAAAO,OAAA,CAAAG,aAAA,CAACP,SAAA,CAAAS,SAAS,CAACC,QAAQ;IAACC,KAAK,EAAEN;EAAU,GAAEC,KAAK,CAACM,QAA6B,CAAC;AAE/E", "ignoreList": []}