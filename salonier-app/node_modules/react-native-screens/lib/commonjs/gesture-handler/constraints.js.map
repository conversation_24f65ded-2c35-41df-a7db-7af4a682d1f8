{"version": 3, "names": ["_reactNativeReanimated", "require", "_defaults", "SupportedGestures", "getAnimationForTransition", "goBackGesture", "customTransitionAnimation", "transitionAnimation", "ScreenTransition", "SwipeRight", "Error", "includes", "AnimationForGesture", "undefined", "checkBoundaries", "event", "translationX", "translationY", "checkIfTransitionCancelled", "distanceX", "requiredXDistance", "distanceY", "requiredYDistance", "isTransitionCanceled", "Math", "abs", "isCanceledHorizontally", "isCanceledVertically"], "sourceRoot": "../../../src", "sources": ["gesture-handler/constraints.ts"], "mappings": ";;;;;;;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAMA,IAAAC,SAAA,GAAAD,OAAA;AAGA,MAAME,iBAAiB,GAAG,CACxB,YAAY,EACZ,WAAW,EACX,WAAW,EACX,SAAS,EACT,iBAAiB,EACjB,eAAe,EACf,qBAAqB,CACtB;AAEM,SAASC,yBAAyBA,CACvCC,aAAwC,EACxCC,yBAA+D,EAC/D;EACA,IAAIC,mBAAmB,GAAGC,uCAAgB,CAACC,UAAU;EACrD,IAAIH,yBAAyB,EAAE;IAC7BC,mBAAmB,GAAGD,yBAAyB;IAC/C,IAAI,CAACD,aAAa,EAAE;MAClB,MAAM,IAAIK,KAAK,CACb,mFACF,CAAC;IACH;EACF,CAAC,MAAM;IACL,IAAI,CAAC,CAACL,aAAa,IAAIF,iBAAiB,CAACQ,QAAQ,CAACN,aAAa,CAAC,EAAE;MAChEE,mBAAmB,GAAGK,6BAAmB,CAACP,aAAa,CAAC;IAC1D,CAAC,MAAM,IAAIA,aAAa,KAAKQ,SAAS,EAAE;MACtC,MAAM,IAAIH,KAAK,CACb,mEAAmEL,aAAa,GAClF,CAAC;IACH;EACF;EACA,OAAOE,mBAAmB;AAC5B;AAEO,SAASO,eAAeA,CAC7BT,aAAiC,EACjCU,KAAwD,EACxD;EACA,SAAS;;EACT,IAAIV,aAAa,KAAK,YAAY,IAAIU,KAAK,CAACC,YAAY,GAAG,CAAC,EAAE;IAC5DD,KAAK,CAACC,YAAY,GAAG,CAAC;EACxB,CAAC,MAAM,IAAIX,aAAa,KAAK,WAAW,IAAIU,KAAK,CAACC,YAAY,GAAG,CAAC,EAAE;IAClED,KAAK,CAACC,YAAY,GAAG,CAAC;EACxB,CAAC,MAAM,IAAIX,aAAa,KAAK,WAAW,IAAIU,KAAK,CAACE,YAAY,GAAG,CAAC,EAAE;IAClEF,KAAK,CAACE,YAAY,GAAG,CAAC;EACxB,CAAC,MAAM,IAAIZ,aAAa,KAAK,SAAS,IAAIU,KAAK,CAACE,YAAY,GAAG,CAAC,EAAE;IAChEF,KAAK,CAACE,YAAY,GAAG,CAAC;EACxB;AACF;AAEO,SAASC,0BAA0BA,CACxCb,aAAiC,EACjCc,SAAiB,EACjBC,iBAAyB,EACzBC,SAAiB,EACjBC,iBAAyB,EACzB;EACA,SAAS;;EACT,IAAIC,oBAAoB,GAAG,KAAK;EAChC,IAAIlB,aAAa,KAAK,YAAY,EAAE;IAClCkB,oBAAoB,GAAGJ,SAAS,GAAGC,iBAAiB;EACtD,CAAC,MAAM,IAAIf,aAAa,KAAK,WAAW,EAAE;IACxCkB,oBAAoB,GAAG,CAACJ,SAAS,GAAGC,iBAAiB;EACvD,CAAC,MAAM,IAAIf,aAAa,KAAK,iBAAiB,EAAE;IAC9CkB,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CAACN,SAAS,CAAC,GAAGC,iBAAiB;EAChE,CAAC,MAAM,IAAIf,aAAa,KAAK,SAAS,EAAE;IACtCkB,oBAAoB,GAAG,CAACF,SAAS,GAAGC,iBAAiB;EACvD,CAAC,MAAM,IAAIjB,aAAa,KAAK,WAAW,EAAE;IACxCkB,oBAAoB,GAAGF,SAAS,GAAGC,iBAAiB;EACtD,CAAC,MAAM,IAAIjB,aAAa,KAAK,eAAe,EAAE;IAC5CkB,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CAACJ,SAAS,CAAC,GAAGC,iBAAiB;EAChE,CAAC,MAAM,IAAIjB,aAAa,KAAK,qBAAqB,EAAE;IAClD,MAAMqB,sBAAsB,GAAGF,IAAI,CAACC,GAAG,CAACN,SAAS,CAAC,GAAGC,iBAAiB;IACtE,MAAMO,oBAAoB,GAAGH,IAAI,CAACC,GAAG,CAACJ,SAAS,CAAC,GAAGC,iBAAiB;IACpEC,oBAAoB,GAAGG,sBAAsB,IAAIC,oBAAoB;EACvE;EACA,OAAOJ,oBAAoB;AAC7B", "ignoreList": []}