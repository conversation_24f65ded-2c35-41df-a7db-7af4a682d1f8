{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "getShadowNodeWrapperAndTagFromRef", "isF<PERSON><PERSON>", "global", "RN$Bridgeless", "findHostInstance_DEPRECATED", "getInternalInstanceHandleFromPublicInstance", "ref", "undefined", "require", "e", "_ref", "_internalInstanceHandle", "scrollViewRef", "getScrollResponder", "getNativeScrollRef", "otherScrollViewRef", "textInputRef", "__internalInstanceHandle", "stateNode", "node", "resolvedRef", "shadowNodeWrapper", "tag", "_nativeTag", "__nativeTag", "hostInstance"], "sourceRoot": "../../../src", "sources": ["gesture-handler/fabricUtils.ts"], "mappings": "AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,iCAAA,GAAAA,iCAAA;AAAAF,OAAA,CAAAG,QAAA,GAAAA,QAAA;AAIb;;AAIO,SAASA,QAAQA,CAAA,EAAG;EACzB,OAAO,CAAC,CAAEC,MAAM,CAAiBC,aAAa;AAChD;AAMA,IAAIC,2BAAmD;AAEvD,IAAIC,2CAEH;;AAED;AACO,SAASL,iCAAiCA,CAACM,GAAgB,EAGhE;EACA;EACA,IAAIF,2BAA2B,KAAKG,SAAS,EAAE;IAC7C,IAAI;MACFH,2BAA2B,GACzBI,OAAO,CAAC,mDAAmD,CAAC,CAACJ,2BAA2B;IAC5F,CAAC,CAAC,OAAOK,CAAC,EAAE;MACVL,2BAA2B,GAAIM,IAAa,IAAK,IAAI;IACvD;EACF;EAEA,IAAIL,2CAA2C,KAAKE,SAAS,EAAE;IAC7D,IAAI;MACFF,2CAA2C,GACzCG,OAAO,CAAC,wFAAwF,CAAC,CAC9FH,2CAA2C,KAC5CK,IAAS,IAAKA,IAAI,CAACC,uBAAuB,CAAC;IACjD,CAAC,CAAC,OAAOF,CAAC,EAAE;MACVJ,2CAA2C,GAAIK,IAAS,IACtDA,IAAI,CAACC,uBAAuB;IAChC;EACF;;EAEA;EACA;EACA,MAAMC,aAAa,GAAGN,GAAG,EAAEO,kBAAkB,GAAG,CAAC,EAAEC,kBAAkB,GAAG,CAAC;EACzE;EACA,MAAMC,kBAAkB,GAAGT,GAAG,EAAEQ,kBAAkB,GAAG,CAAC;EACtD;EACA,MAAME,YAAY,GAAGV,GAAG,EAAEW,wBAAwB,EAAEC,SAAS,EAAEC,IAAI;EAEnE,IAAIC,WAAW;EACf,IAAIR,aAAa,EAAE;IACjBQ,WAAW,GAAG;MACZC,iBAAiB,EAAET,aAAa,CAACK,wBAAwB,CAACC,SAAS,CAACC,IAAI;MACxEG,GAAG,EAAEV,aAAa,CAACW;IACrB,CAAC;EACH,CAAC,MAAM,IAAIR,kBAAkB,EAAE;IAC7BK,WAAW,GAAG;MACZC,iBAAiB,EACfN,kBAAkB,CAACE,wBAAwB,CAACC,SAAS,CAACC,IAAI;MAC5DG,GAAG,EAAEP,kBAAkB,CAACS;IAC1B,CAAC;EACH,CAAC,MAAM,IAAIR,YAAY,EAAE;IACvBI,WAAW,GAAG;MACZC,iBAAiB,EAAEL,YAAY;MAC/BM,GAAG,EAAGhB,GAAG,EAAUkB;IACrB,CAAC;EACH,CAAC,MAAM;IACL,MAAMC,YAAY,GAAGrB,2BAA2B,CAACE,GAAG,CAAC;IACrDc,WAAW,GAAG;MACZC,iBAAiB,EACfhB,2CAA2C,CAACoB,YAAY,CAAC,CAACP,SAAS,CAChEC,IAAI;MACTG,GAAG,EAAGG,YAAY,EAAUF;IAC9B,CAAC;EACH;EAEA,OAAOH,WAAW;AACpB", "ignoreList": []}