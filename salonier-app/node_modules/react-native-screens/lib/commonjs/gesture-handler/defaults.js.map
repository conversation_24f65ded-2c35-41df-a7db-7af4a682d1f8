{"version": 3, "names": ["_reactNativeGestureHandler", "require", "_reactNativeReanimated", "DefaultEvent", "exports", "absoluteX", "absoluteY", "handlerTag", "numberOfPointers", "state", "translationX", "translationY", "velocityX", "velocityY", "x", "y", "pointerType", "PointerType", "TOUCH", "DefaultScreenDimensions", "width", "height", "pageX", "pageY", "AnimationForGesture", "swipeRight", "ScreenTransition", "SwipeRight", "swipeLeft", "SwipeLeft", "swipeDown", "SwipeDown", "swipeUp", "SwipeUp", "horizontalSwipe", "Horizontal", "verticalSwipe", "Vertical", "twoDimensionalSwipe", "TwoDimensional"], "sourceRoot": "../../../src", "sources": ["gesture-handler/defaults.ts"], "mappings": ";;;;;;AAAA,IAAAA,0BAAA,GAAAC,OAAA;AAKA,IAAAC,sBAAA,GAAAD,OAAA;AAEO,MAAME,YAA+D,GAAAC,OAAA,CAAAD,YAAA,GAAG;EAC7EE,SAAS,EAAE,CAAC;EACZC,SAAS,EAAE,CAAC;EACZC,UAAU,EAAE,CAAC;EACbC,gBAAgB,EAAE,CAAC;EACnBC,KAAK,EAAE,CAAC;EACRC,YAAY,EAAE,CAAC;EACfC,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,CAAC;EACZC,SAAS,EAAE,CAAC;EACZC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EAEJ;EACA;EACA;EACA;EACAC,WAAW,EAAEC,sCAAW,CAACC;AAC3B,CAAC;AAEM,MAAMC,uBAAuB,GAAAf,OAAA,CAAAe,uBAAA,GAAG;EACrCC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTP,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJO,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE;AACT,CAAC;AAEM,MAAMC,mBAAmB,GAAApB,OAAA,CAAAoB,mBAAA,GAAG;EACjCC,UAAU,EAAEC,uCAAgB,CAACC,UAAU;EACvCC,SAAS,EAAEF,uCAAgB,CAACG,SAAS;EACrCC,SAAS,EAAEJ,uCAAgB,CAACK,SAAS;EACrCC,OAAO,EAAEN,uCAAgB,CAACO,OAAO;EACjCC,eAAe,EAAER,uCAAgB,CAACS,UAAU;EAC5CC,aAAa,EAAEV,uCAAgB,CAACW,QAAQ;EACxCC,mBAAmB,EAAEZ,uCAAgB,CAACa;AACxC,CAAC", "ignoreList": []}