{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "e", "__esModule", "default", "GHContext", "exports", "React", "createContext", "props", "createElement", "Fragment", "children", "RNSScreensRefContext"], "sourceRoot": "../../src", "sources": ["contexts.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAiD,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAG1C,MAAMG,SAAS,GAAAC,OAAA,CAAAD,SAAA,gBAAGE,cAAK,CAACC,aAAa,CACzCC,KAA8C,iBAAKV,MAAA,CAAAK,OAAA,CAAAM,aAAA,CAAAX,MAAA,CAAAK,OAAA,CAAAO,QAAA,QAAGF,KAAK,CAACG,QAAW,CAC1E,CAAC;AAEM,MAAMC,oBAAoB,GAAAP,OAAA,CAAAO,oBAAA,gBAC/BN,cAAK,CAACC,aAAa,CAAmD,IAAI,CAAC", "ignoreList": []}