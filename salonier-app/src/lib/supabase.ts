import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// These will be replaced with actual Supabase credentials
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Database table names
export const TABLES = {
  USERS: 'users',
  BRANDS: 'brands',
  PRODUCT_LINES: 'product_lines',
  SHADES: 'shades',
  CLIENTS: 'clients',
  HAIR_ANALYSES: 'hair_analyses',
  FORMULATIONS: 'formulations',
  HAIR_HISTORY: 'hair_history',
  USER_PREFERENCES: 'user_preferences',
} as const;
