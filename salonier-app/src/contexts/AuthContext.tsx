import React, { createContext, useContext, useEffect, useState } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '../lib/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Modo de desarrollo - cambiar a false cuando tengas Supabase configurado
const DEVELOPMENT_MODE = true;

interface AuthContextType {
  session: Session | null;
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, fullName: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (DEVELOPMENT_MODE) {
      // Modo de desarrollo - verificar si hay una sesión guardada localmente
      const checkDevSession = async () => {
        try {
          const savedSession = await AsyncStorage.getItem('dev_session');
          if (savedSession) {
            const sessionData = JSON.parse(savedSession);
            setSession(sessionData);
            setUser(sessionData.user);
          }
        } catch (error) {
          console.log('Error loading dev session:', error);
        }
        setLoading(false);
      };
      checkDevSession();
    } else {
      // Modo producción con Supabase
      supabase.auth.getSession().then(({ data: { session } }) => {
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);
      });

      const {
        data: { subscription },
      } = supabase.auth.onAuthStateChange((_event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);
      });

      return () => subscription.unsubscribe();
    }
  }, []);

  const signIn = async (email: string, password: string) => {
    if (DEVELOPMENT_MODE) {
      // Modo de desarrollo - simular login exitoso
      const mockSession = {
        user: {
          id: '123',
          email: email,
          user_metadata: {
            full_name: 'Estilista Demo',
          },
        },
      };

      try {
        await AsyncStorage.setItem('dev_session', JSON.stringify(mockSession));
        setSession(mockSession as any);
        setUser(mockSession.user as any);
        return { error: null };
      } catch (error) {
        return { error: { message: 'Error en modo desarrollo' } };
      }
    } else {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      return { error };
    }
  };

  const signUp = async (email: string, password: string, fullName: string) => {
    if (DEVELOPMENT_MODE) {
      // Modo de desarrollo - simular registro exitoso
      const mockSession = {
        user: {
          id: '123',
          email: email,
          user_metadata: {
            full_name: fullName,
          },
        },
      };

      try {
        await AsyncStorage.setItem('dev_session', JSON.stringify(mockSession));
        setSession(mockSession as any);
        setUser(mockSession.user as any);
        return { error: null };
      } catch (error) {
        return { error: { message: 'Error en modo desarrollo' } };
      }
    } else {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      });
      return { error };
    }
  };

  const signOut = async () => {
    if (DEVELOPMENT_MODE) {
      await AsyncStorage.removeItem('dev_session');
      setSession(null);
      setUser(null);
    } else {
      await supabase.auth.signOut();
    }
  };

  const value = {
    session,
    user,
    loading,
    signIn,
    signUp,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
