import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';

export default function ProfileScreen() {
  const { signOut, user } = useAuth();

  const handleLogout = () => {
    Alert.alert(
      'Cerrar Sesión',
      '¿Estás seguro de que quieres cerrar sesión?',
      [
        { text: 'Cancelar', style: 'cancel' },
        { text: 'Cerrar Sesión', style: 'destructive', onPress: signOut },
      ]
    );
  };

  const profileOptions = [
    {
      icon: 'person' as keyof typeof MaterialIcons.glyphMap,
      title: 'Información Personal',
      subtitle: 'Editar perfil y preferencias',
      onPress: () => console.log('Edit profile'),
    },
    {
      icon: 'palette' as keyof typeof MaterialIcons.glyphMap,
      title: 'Marcas Prefer<PERSON>',
      subtitle: 'Configurar catálogo de productos',
      onPress: () => console.log('Preferred brands'),
    },
    {
      icon: 'settings' as keyof typeof MaterialIcons.glyphMap,
      title: 'Configuración',
      subtitle: 'Notificaciones y preferencias',
      onPress: () => console.log('Settings'),
    },
    {
      icon: 'help' as keyof typeof MaterialIcons.glyphMap,
      title: 'Ayuda y Soporte',
      subtitle: 'Tutoriales y contacto',
      onPress: () => console.log('Help'),
    },
    {
      icon: 'info' as keyof typeof MaterialIcons.glyphMap,
      title: 'Acerca de Salonier',
      subtitle: 'Versión y términos de uso',
      onPress: () => console.log('About'),
    },
  ];

  const renderOption = (option: any) => (
    <TouchableOpacity
      key={option.title}
      style={styles.optionCard}
      onPress={option.onPress}
    >
      <View style={styles.optionIcon}>
        <MaterialIcons name={option.icon} size={24} color="#6366f1" />
      </View>
      <View style={styles.optionInfo}>
        <Text style={styles.optionTitle}>{option.title}</Text>
        <Text style={styles.optionSubtitle}>{option.subtitle}</Text>
      </View>
      <MaterialIcons name="chevron-right" size={24} color="#6b7280" />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Profile Header */}
        <View style={styles.profileHeader}>
          <View style={styles.avatar}>
            <MaterialIcons name="person" size={40} color="white" />
          </View>
          <Text style={styles.name}>
            {user?.user_metadata?.full_name || 'Estilista Profesional'}
          </Text>
          <Text style={styles.email}>{user?.email || '<EMAIL>'}</Text>
          <View style={styles.statsContainer}>
            <View style={styles.stat}>
              <Text style={styles.statNumber}>0</Text>
              <Text style={styles.statLabel}>Formulaciones</Text>
            </View>
            <View style={styles.stat}>
              <Text style={styles.statNumber}>0</Text>
              <Text style={styles.statLabel}>Clientes</Text>
            </View>
            <View style={styles.stat}>
              <Text style={styles.statNumber}>0%</Text>
              <Text style={styles.statLabel}>Éxito</Text>
            </View>
          </View>
        </View>

        {/* Options */}
        <View style={styles.optionsContainer}>
          {profileOptions.map(renderOption)}
        </View>

        {/* Logout Button */}
        <View style={styles.logoutContainer}>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <MaterialIcons name="logout" size={20} color="#ef4444" />
            <Text style={styles.logoutText}>Cerrar Sesión</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  profileHeader: {
    backgroundColor: 'white',
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#6366f1',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  email: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 24,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  stat: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#6366f1',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
  },
  optionsContainer: {
    padding: 20,
  },
  optionCard: {
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f9ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  optionInfo: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 2,
  },
  optionSubtitle: {
    fontSize: 14,
    color: '#6b7280',
  },
  logoutContainer: {
    padding: 20,
    paddingTop: 0,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ef4444',
    marginLeft: 8,
  },
});
