import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

interface QuickActionProps {
  icon: keyof typeof MaterialIcons.glyphMap;
  title: string;
  subtitle: string;
  onPress: () => void;
  gradient: string[];
}

const QuickAction: React.FC<QuickActionProps> = ({
  icon,
  title,
  subtitle,
  onPress,
  gradient,
}) => (
  <TouchableOpacity onPress={onPress} style={styles.actionContainer}>
    <LinearGradient colors={gradient} style={styles.actionGradient}>
      <MaterialIcons name={icon} size={32} color="white" />
      <Text style={styles.actionTitle}>{title}</Text>
      <Text style={styles.actionSubtitle}>{subtitle}</Text>
    </LinearGradient>
  </TouchableOpacity>
);

export default function HomeScreen() {
  const quickActions = [
    {
      icon: 'palette' as keyof typeof MaterialIcons.glyphMap,
      title: 'Nueva Formulación',
      subtitle: 'Crear fórmula de color',
      gradient: ['#6366f1', '#8b5cf6'],
      onPress: () => {
        // Navigate to formulation screen
        console.log('Navigate to formulation');
      },
    },
    {
      icon: 'camera-alt' as keyof typeof MaterialIcons.glyphMap,
      title: 'Análisis Capilar',
      subtitle: 'Diagnosticar cabello',
      gradient: ['#10b981', '#059669'],
      onPress: () => {
        console.log('Navigate to hair analysis');
      },
    },
    {
      icon: 'calculate' as keyof typeof MaterialIcons.glyphMap,
      title: 'Calculadora',
      subtitle: 'Cantidades exactas',
      gradient: ['#f59e0b', '#d97706'],
      onPress: () => {
        console.log('Navigate to calculator');
      },
    },
    {
      icon: 'build' as keyof typeof MaterialIcons.glyphMap,
      title: 'Correcciones',
      subtitle: 'Solucionar problemas',
      gradient: ['#ef4444', '#dc2626'],
      onPress: () => {
        console.log('Navigate to corrections');
      },
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.greeting}>¡Hola, Estilista!</Text>
          <Text style={styles.subtitle}>
            Listo para crear fórmulas perfectas
          </Text>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Acciones Rápidas</Text>
          <View style={styles.actionsGrid}>
            {quickActions.map((action, index) => (
              <QuickAction
                key={index}
                icon={action.icon}
                title={action.title}
                subtitle={action.subtitle}
                gradient={action.gradient}
                onPress={action.onPress}
              />
            ))}
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Actividad Reciente</Text>
          <View style={styles.recentCard}>
            <Text style={styles.recentText}>
              No hay formulaciones recientes
            </Text>
            <Text style={styles.recentSubtext}>
              Crea tu primera formulación para comenzar
            </Text>
          </View>
        </View>

        {/* Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Estadísticas</Text>
          <View style={styles.statsContainer}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>0</Text>
              <Text style={styles.statLabel}>Formulaciones</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>0</Text>
              <Text style={styles.statLabel}>Clientes</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>0%</Text>
              <Text style={styles.statLabel}>Éxito</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 10,
  },
  greeting: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionContainer: {
    width: '48%',
    marginBottom: 16,
  },
  actionGradient: {
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
    minHeight: 120,
    justifyContent: 'center',
  },
  actionTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
  actionSubtitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
  },
  recentCard: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  recentText: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 4,
  },
  recentSubtext: {
    fontSize: 14,
    color: '#9ca3af',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6366f1',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
  },
});
