import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

export default function FormulationScreen() {
  const steps = [
    {
      id: 1,
      title: 'Seleccionar Cliente',
      subtitle: 'Elige el cliente para la formulación',
      icon: 'person' as keyof typeof MaterialIcons.glyphMap,
      completed: false,
    },
    {
      id: 2,
      title: 'Aná<PERSON>is Capilar',
      subtitle: 'Diagnosticar estado del cabello',
      icon: 'camera-alt' as keyof typeof MaterialIcons.glyphMap,
      completed: false,
    },
    {
      id: 3,
      title: 'Objetivo de Color',
      subtitle: 'Definir resultado deseado',
      icon: 'palette' as keyof typeof MaterialIcons.glyphMap,
      completed: false,
    },
    {
      id: 4,
      title: 'Selección de Productos',
      subtitle: 'Elegir marcas y tonos',
      icon: 'shopping-cart' as keyof typeof MaterialIcons.glyphMap,
      completed: false,
    },
    {
      id: 5,
      title: 'Cálculo de Cantidades',
      subtitle: 'Cantidades exactas y ratios',
      icon: 'calculate' as keyof typeof MaterialIcons.glyphMap,
      completed: false,
    },
    {
      id: 6,
      title: 'Revisión y Alertas',
      subtitle: 'Verificar compatibilidad',
      icon: 'warning' as keyof typeof MaterialIcons.glyphMap,
      completed: false,
    },
  ];

  const renderStep = (step: any, index: number) => (
    <TouchableOpacity key={step.id} style={styles.stepCard}>
      <View style={styles.stepHeader}>
        <View style={[
          styles.stepNumber,
          step.completed && styles.stepNumberCompleted
        ]}>
          {step.completed ? (
            <MaterialIcons name="check" size={20} color="white" />
          ) : (
            <Text style={styles.stepNumberText}>{step.id}</Text>
          )}
        </View>
        <View style={styles.stepInfo}>
          <Text style={styles.stepTitle}>{step.title}</Text>
          <Text style={styles.stepSubtitle}>{step.subtitle}</Text>
        </View>
        <MaterialIcons name={step.icon} size={24} color="#6366f1" />
      </View>
      {index < steps.length - 1 && <View style={styles.stepConnector} />}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.header}>
          <Text style={styles.title}>Nueva Formulación</Text>
          <Text style={styles.subtitle}>
            Sigue los pasos para crear una fórmula perfecta
          </Text>
        </View>

        <View style={styles.stepsContainer}>
          {steps.map((step, index) => renderStep(step, index))}
        </View>

        <View style={styles.footer}>
          <TouchableOpacity style={styles.startButton}>
            <Text style={styles.startButtonText}>Comenzar Formulación</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
  },
  stepsContainer: {
    padding: 20,
  },
  stepCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    position: 'relative',
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#e5e7eb',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stepNumberCompleted: {
    backgroundColor: '#10b981',
  },
  stepNumberText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6b7280',
  },
  stepInfo: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 2,
  },
  stepSubtitle: {
    fontSize: 14,
    color: '#6b7280',
  },
  stepConnector: {
    position: 'absolute',
    left: 31,
    bottom: -12,
    width: 2,
    height: 12,
    backgroundColor: '#e5e7eb',
  },
  footer: {
    padding: 20,
  },
  startButton: {
    backgroundColor: '#6366f1',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  startButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
