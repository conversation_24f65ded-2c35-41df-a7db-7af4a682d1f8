import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

export default function HistoryScreen() {
  // Mock data - will be replaced with real data from Supabase
  const formulations = [
    {
      id: '1',
      clientName: '<PERSON> García',
      date: '2024-06-15',
      result: 'Rubio ceniza nivel 8',
      satisfaction: 9,
      products: ['Wella 8/1', 'Wella 8/0'],
    },
    {
      id: '2',
      clientName: '<PERSON> Rodríguez',
      date: '2024-06-10',
      result: 'Castaño chocolate nivel 6',
      satisfaction: 8,
      products: ['L\'Oréal 6.35', 'L\'Oréal 6.0'],
    },
  ];

  const renderFormulation = ({ item }: { item: any }) => (
    <TouchableOpacity style={styles.formulationCard}>
      <View style={styles.cardHeader}>
        <View style={styles.clientInfo}>
          <Text style={styles.clientName}>{item.clientName}</Text>
          <Text style={styles.date}>{item.date}</Text>
        </View>
        <View style={styles.satisfactionContainer}>
          <MaterialIcons name="star" size={16} color="#f59e0b" />
          <Text style={styles.satisfaction}>{item.satisfaction}/10</Text>
        </View>
      </View>
      
      <Text style={styles.result}>{item.result}</Text>
      
      <View style={styles.productsContainer}>
        <Text style={styles.productsLabel}>Productos:</Text>
        <Text style={styles.products}>{item.products.join(', ')}</Text>
      </View>
      
      <View style={styles.cardFooter}>
        <TouchableOpacity style={styles.actionButton}>
          <MaterialIcons name="visibility" size={16} color="#6366f1" />
          <Text style={styles.actionText}>Ver detalles</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton}>
          <MaterialIcons name="copy" size={16} color="#10b981" />
          <Text style={styles.actionText}>Duplicar</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Historial</Text>
        <TouchableOpacity style={styles.filterButton}>
          <MaterialIcons name="filter-list" size={24} color="#6366f1" />
        </TouchableOpacity>
      </View>

      {formulations.length === 0 ? (
        <View style={styles.emptyState}>
          <MaterialIcons name="history" size={64} color="#d1d5db" />
          <Text style={styles.emptyTitle}>No hay formulaciones</Text>
          <Text style={styles.emptySubtitle}>
            Crea tu primera formulación para ver el historial
          </Text>
        </View>
      ) : (
        <FlatList
          data={formulations}
          renderItem={renderFormulation}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.list}
          showsVerticalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  filterButton: {
    padding: 8,
  },
  list: {
    padding: 20,
  },
  formulationCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  clientInfo: {
    flex: 1,
  },
  clientName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 2,
  },
  date: {
    fontSize: 14,
    color: '#6b7280',
  },
  satisfactionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  satisfaction: {
    fontSize: 14,
    fontWeight: '600',
    color: '#f59e0b',
    marginLeft: 4,
  },
  result: {
    fontSize: 16,
    color: '#1f2937',
    marginBottom: 12,
    fontWeight: '500',
  },
  productsContainer: {
    marginBottom: 12,
  },
  productsLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 2,
  },
  products: {
    fontSize: 14,
    color: '#374151',
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  actionText: {
    fontSize: 14,
    color: '#6366f1',
    marginLeft: 4,
    fontWeight: '500',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#6b7280',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#9ca3af',
    textAlign: 'center',
  },
});
