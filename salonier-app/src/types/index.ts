// Core Types for Salonier Application

export interface User {
  id: string;
  email: string;
  full_name: string;
  professional_level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  specialties: string[];
  years_experience: number;
  preferred_brands: string[];
  created_at: string;
  updated_at: string;
}

export interface Brand {
  id: string;
  name: string;
  logo_url?: string;
  default_ratios: {
    permanent: string;
    high_lift: string;
    demi_permanent: string;
  };
  product_lines: ProductLine[];
}

export interface ProductLine {
  id: string;
  brand_id: string;
  name: string;
  type: 'permanent' | 'demi_permanent' | 'high_lift' | 'toner';
  shades: Shade[];
}

export interface Shade {
  id: string;
  product_line_id: string;
  code: string;
  name: string;
  level: number;
  tone: string;
  undertones: string[];
  coverage_percentage: number;
  lifting_power?: number;
}

export interface Client {
  id: string;
  user_id: string;
  name: string;
  phone?: string;
  email?: string;
  hair_history: HairHistory[];
  allergies: string[];
  notes: string;
  created_at: string;
  updated_at: string;
}

export interface HairAnalysis {
  id: string;
  natural_level: number;
  gray_percentage: number;
  dominant_undertones: string[];
  porosity: 'low' | 'medium' | 'high' | 'extreme';
  elasticity: 'good' | 'compromised' | 'damaged';
  texture: 'fine' | 'medium' | 'coarse';
  density: 'low' | 'medium' | 'high';
  length: 'short' | 'medium' | 'long';
  length_cm: number;
  previous_chemical_services: boolean;
  last_service_date?: string;
  last_service_type?: string;
  photos: string[];
}

export interface Formulation {
  id: string;
  user_id: string;
  client_id: string;
  hair_analysis: HairAnalysis;
  target_result: {
    level: number;
    tone: string;
    coverage_goal: string;
  };
  formula: Formula;
  application_technique: 'global' | 'root_touch_up' | 'partial' | 'correction';
  processing_time: number;
  developer_volume: 10 | 20 | 30 | 40;
  total_quantity_ml: number;
  session_notes: string;
  result_photos: string[];
  client_satisfaction: number;
  created_at: string;
}

export interface Formula {
  products: FormulaProduct[];
  ratio: string;
  mixing_instructions: string;
  application_notes: string;
}

export interface FormulaProduct {
  shade_id: string;
  quantity_ml: number;
  percentage: number;
}

export interface HairHistory {
  id: string;
  client_id: string;
  formulation_id: string;
  service_date: string;
  result_rating: number;
  notes: string;
}

export interface Alert {
  type: 'safety' | 'compatibility' | 'technique' | 'timing';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  recommendation?: string;
}

export interface CalculationParams {
  hair_analysis: HairAnalysis;
  target_result: {
    level: number;
    tone: string;
    coverage_goal: string;
  };
  application_technique: string;
  selected_products: Shade[];
  developer_volume: number;
}

export interface QuantityCalculation {
  total_quantity_ml: number;
  product_quantities: {
    shade_id: string;
    quantity_ml: number;
  }[];
  developer_quantity_ml: number;
  safety_margin_ml: number;
  alerts: Alert[];
}
