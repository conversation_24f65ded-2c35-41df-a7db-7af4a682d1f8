-- Salonier Database Schema
-- This file contains the database structure for the Salonier application

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    professional_level TEXT CHECK (professional_level IN ('beginner', 'intermediate', 'advanced', 'expert')) DEFAULT 'intermediate',
    specialties TEXT[] DEFAULT '{}',
    years_experience INTEGER DEFAULT 0,
    preferred_brands TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Brands table
CREATE TABLE public.brands (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    logo_url TEXT,
    default_ratios JSONB DEFAULT '{"permanent": "1:1.5", "high_lift": "1:2", "demi_permanent": "1:1"}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Product lines table
CREATE TABLE public.product_lines (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    brand_id UUID REFERENCES public.brands(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    type TEXT CHECK (type IN ('permanent', 'demi_permanent', 'high_lift', 'toner')) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(brand_id, name)
);

-- Shades table
CREATE TABLE public.shades (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    product_line_id UUID REFERENCES public.product_lines(id) ON DELETE CASCADE,
    code TEXT NOT NULL,
    name TEXT NOT NULL,
    level INTEGER CHECK (level >= 1 AND level <= 12) NOT NULL,
    tone TEXT NOT NULL,
    undertones TEXT[] DEFAULT '{}',
    coverage_percentage INTEGER CHECK (coverage_percentage >= 0 AND coverage_percentage <= 100) DEFAULT 100,
    lifting_power INTEGER CHECK (lifting_power >= 0 AND lifting_power <= 5),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(product_line_id, code)
);

-- Clients table
CREATE TABLE public.clients (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    phone TEXT,
    email TEXT,
    allergies TEXT[] DEFAULT '{}',
    notes TEXT DEFAULT '',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Hair analyses table
CREATE TABLE public.hair_analyses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    client_id UUID REFERENCES public.clients(id) ON DELETE CASCADE,
    natural_level INTEGER CHECK (natural_level >= 1 AND natural_level <= 12) NOT NULL,
    gray_percentage INTEGER CHECK (gray_percentage >= 0 AND gray_percentage <= 100) DEFAULT 0,
    dominant_undertones TEXT[] DEFAULT '{}',
    porosity TEXT CHECK (porosity IN ('low', 'medium', 'high', 'extreme')) DEFAULT 'medium',
    elasticity TEXT CHECK (elasticity IN ('good', 'compromised', 'damaged')) DEFAULT 'good',
    texture TEXT CHECK (texture IN ('fine', 'medium', 'coarse')) DEFAULT 'medium',
    density TEXT CHECK (density IN ('low', 'medium', 'high')) DEFAULT 'medium',
    length TEXT CHECK (length IN ('short', 'medium', 'long')) DEFAULT 'medium',
    length_cm INTEGER DEFAULT 15,
    previous_chemical_services BOOLEAN DEFAULT FALSE,
    last_service_date DATE,
    last_service_type TEXT,
    photos TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Formulations table
CREATE TABLE public.formulations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    client_id UUID REFERENCES public.clients(id) ON DELETE CASCADE,
    hair_analysis_id UUID REFERENCES public.hair_analyses(id) ON DELETE CASCADE,
    target_result JSONB NOT NULL, -- {level, tone, coverage_goal}
    formula JSONB NOT NULL, -- {products: [{shade_id, quantity_ml, percentage}], ratio, mixing_instructions, application_notes}
    application_technique TEXT CHECK (application_technique IN ('global', 'root_touch_up', 'partial', 'correction')) DEFAULT 'global',
    processing_time INTEGER DEFAULT 30, -- minutes
    developer_volume INTEGER CHECK (developer_volume IN (10, 20, 30, 40)) DEFAULT 20,
    total_quantity_ml INTEGER NOT NULL,
    session_notes TEXT DEFAULT '',
    result_photos TEXT[] DEFAULT '{}',
    client_satisfaction INTEGER CHECK (client_satisfaction >= 1 AND client_satisfaction <= 10),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Hair history table (for tracking client's color history)
CREATE TABLE public.hair_history (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    client_id UUID REFERENCES public.clients(id) ON DELETE CASCADE,
    formulation_id UUID REFERENCES public.formulations(id) ON DELETE CASCADE,
    service_date DATE NOT NULL,
    result_rating INTEGER CHECK (result_rating >= 1 AND result_rating <= 10),
    notes TEXT DEFAULT '',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User preferences table
CREATE TABLE public.user_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE UNIQUE,
    preferred_ratios JSONB DEFAULT '{}',
    default_developer_volume INTEGER CHECK (default_developer_volume IN (10, 20, 30, 40)) DEFAULT 20,
    favorite_techniques TEXT[] DEFAULT '{}',
    ai_learning_data JSONB DEFAULT '{}', -- Store AI learning patterns
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Row Level Security (RLS) Policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.hair_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.formulations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.hair_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- Users can only see their own data
CREATE POLICY "Users can view own profile" ON public.users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid() = id);

-- Clients policies
CREATE POLICY "Users can view own clients" ON public.clients FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own clients" ON public.clients FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own clients" ON public.clients FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own clients" ON public.clients FOR DELETE USING (auth.uid() = user_id);

-- Hair analyses policies
CREATE POLICY "Users can view analyses of own clients" ON public.hair_analyses FOR SELECT 
USING (EXISTS (SELECT 1 FROM public.clients WHERE clients.id = hair_analyses.client_id AND clients.user_id = auth.uid()));

CREATE POLICY "Users can insert analyses for own clients" ON public.hair_analyses FOR INSERT 
WITH CHECK (EXISTS (SELECT 1 FROM public.clients WHERE clients.id = hair_analyses.client_id AND clients.user_id = auth.uid()));

CREATE POLICY "Users can update analyses of own clients" ON public.hair_analyses FOR UPDATE 
USING (EXISTS (SELECT 1 FROM public.clients WHERE clients.id = hair_analyses.client_id AND clients.user_id = auth.uid()));

-- Formulations policies
CREATE POLICY "Users can view own formulations" ON public.formulations FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own formulations" ON public.formulations FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own formulations" ON public.formulations FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own formulations" ON public.formulations FOR DELETE USING (auth.uid() = user_id);

-- Hair history policies
CREATE POLICY "Users can view history of own clients" ON public.hair_history FOR SELECT 
USING (EXISTS (SELECT 1 FROM public.clients WHERE clients.id = hair_history.client_id AND clients.user_id = auth.uid()));

CREATE POLICY "Users can insert history for own clients" ON public.hair_history FOR INSERT 
WITH CHECK (EXISTS (SELECT 1 FROM public.clients WHERE clients.id = hair_history.client_id AND clients.user_id = auth.uid()));

-- User preferences policies
CREATE POLICY "Users can view own preferences" ON public.user_preferences FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own preferences" ON public.user_preferences FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own preferences" ON public.user_preferences FOR UPDATE USING (auth.uid() = user_id);

-- Brands, product_lines, and shades are public (read-only for users)
ALTER TABLE public.brands ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_lines ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.shades ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view brands" ON public.brands FOR SELECT TO authenticated USING (true);
CREATE POLICY "Anyone can view product lines" ON public.product_lines FOR SELECT TO authenticated USING (true);
CREATE POLICY "Anyone can view shades" ON public.shades FOR SELECT TO authenticated USING (true);

-- Functions for updated_at timestamps
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.brands FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.product_lines FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.shades FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.clients FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.hair_analyses FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.formulations FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.user_preferences FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
