# Documento de Especificación de Producto: Salonier

**Versión**: 5.1 (Validado por Estilistas Expertos en Coloración)  
**Fecha**: 17 de junio de 2025  
**Proyecto**: Salonier - Asistente IA de Formulación de Coloración Capilar Profesional

---

## 1. Introducción

### 1.1. Propósito de Salonier
Salonier es **el primer asistente IA especializado en formulación precisa de coloración capilar para estilistas profesionales**. Su propósito es eliminar los errores más costosos del colorista: cálculos incorrectos de cantidades, ratios de mezcla imprecisos, y falta de documentación consistente.

**Salonier no reemplaza el expertise del colorista** - lo amplifica mediante cálculos exactos, alertas inteligentes y documentación automática, permitiendo al profesional enfocarse en lo que mejor hace: el arte y la técnica de la coloración.

### 1.2. Misión del Producto
**"Que ningún colorista profesional vuelva a desperdiciar producto, tiempo o cliente por errores de cálculo o falta de documentación"**

Una herramienta que convierte la experiencia e intuición del estilista en fórmulas científicamente precisas y consistentemente replicables.

---

## 2. Validación del Mercado y Problema

### 2.1. Pain Points Críticos Validados
**Basado en investigación con estilistas profesionales activos:**

**1. Errores de Cálculo Costosos (Problema #1)**
- 73% de coloristas han desperdiciado producto por cálculos incorrectos
- Error promedio: $45-85 por sesión fallida
- Problema más común: ratios incorrectos 1:1 vs 1:2 vs 1:1.5

**2. Inconsistencia en Replicación (Problema #2)**
- 68% luchan por replicar exactamente una fórmula exitosa
- Documentación manual en libretas físicas (primitiva para 2025)
- "Hacer el mismo error dos veces es catastrófico"

**3. Gestión de Complejidad (Problema #3)**
- Promedio 4-7 marcas diferentes por salón
- 15-20 minutos por formulación compleja
- Strand tests obligatorios pero mal documentados

**4. Casos Especiales Críticos**
- Formulación para % variables de canas (30%, 50%, 75%+)
- Correcciones de color (casos más complejos y lucrativos)
- Técnicas de filling para cambios de nivel drásticos

### 2.2. Oportunidad de Mercado
- **Mercado global de coloración capilar:** $42.3B USD proyectado 2032
- **Estilistas profesionales especializados en color:** ~85,000 solo en USA
- **Disposición a pagar:** $79-199/mes (validado con 50+ profesionales)
- **ROI garantizado:** Se paga sola evitando 1-2 errores mensuales

---

## 3. Funcionalidades Core (MVP Validado)

### 3.1. Configuración Profesional de Estilista

#### Setup de Marcas y Preferencias
- **Selección de catálogo:** 100+ marcas (Wella, L'Oréal, Schwarzkopf, etc.)
- **Líneas específicas por marca:** Koleston, Majirel, Igora, etc.
- **Preferencias de ratios:** 1:1, 1:1.5, 1:2 según marca y técnica
- **Configuración de oxidantes:** 10, 20, 30, 40vol disponibles
- **Técnicas preferidas:** Global, balayage, corrección, filling

#### Perfil Profesional
```
EJEMPLO DE CONFIGURACIÓN:
Marcas principales: Wella Koleston + L'Oréal Majirel
Ratios preferidos: 1:1.5 (Wella), 1:2 (lifting)
Especialidades: Correcciones, rubios complejos
Nivel experiencia: Avanzado (10+ años)
Tipos de cliente: 60% canas, 30% fashion color, 10% correcciones
```

### 3.2. Diagnóstico Capilar Híbrido Inteligente

#### Análisis Visual IA (45 segundos)
**Captura optimizada:**
- **2-3 fotos máximo** con guías de iluminación automáticas
- **Detección automática:** Nivel base, % canas estimado, reflejos dominantes
- **Análisis de historial:** Procesado vs virgen (por textura visual)

**Output IA inicial:**
```
DETECCIÓN AUTOMÁTICA:
✓ Nivel natural: 6
✓ Canas estimadas: 40%
✓ Reflejos dominantes: Dorados
✓ Estado: Procesado químicamente (raíces 3cm)
✓ Longitud: Media (hasta hombros)
```

#### Validación y Corrección Profesional (60 segundos)
**El estilista ajusta TODOS los parámetros críticos:**

**Información que SOLO el estilista puede determinar:**
- **Porosidad real** (tacto): Baja/Media/Alta/Extrema
- **Elasticidad** (prueba física): Buena/Comprometida/Dañada
- **Textura** (visual + tacto): Fino/Medio/Grueso
- **Densidad real:** Baja/Media/Alta

**Historial crítico:**
- **Último servicio químico:** Fecha y productos usados
- **Sensibilidades conocidas:** PPD, amoniaco, cuero cabelludo
- **Hábitos de cuidado:** Plancha diaria, tratamientos, frecuencia lavado
- **Medicaciones/hormonas:** Que afecten respuesta al color

```
EJEMPLO DE AJUSTE:
IA detectó: "Canas 40%, reflejos dorados"
ESTILISTA corrige: "Canas 50%, reflejos naranjas por exposición solar"
IA actualiza: Formulación ajustada para mayor cobertura + neutralización
```

### 3.3. Calculadora de Cantidades Ultra-Precisa

#### Cálculo Inteligente por Zona
**Basado en múltiples factores:**
- **Longitud específica:** Corto/Medio/Largo (cm exactos)
- **Densidad del cabello:** Impacta cantidad necesaria 30-50%
- **Técnica de aplicación:** Global/Parcial/Corrección
- **Porosidad:** Alta = -20% cantidad, Baja = +15% cantidad

```
EJEMPLO DE CÁLCULO REAL:
Cliente: Melena media, densidad alta, porosidad media
Técnica: Retoque raíces + refrescar medios

CANTIDADES CALCULADAS:
├── Raíces (50% canas): 45ml mezcla
├── Medios (refresh): 25ml mezcla  
├── Puntas (no tocar): 0ml
└── TOTAL NECESARIO: 70ml mezcla + 10% seguridad = 77ml

FÓRMULA EXACTA:
├── Wella 7/1: 35ml
├── Wella 7/0: 12ml (para canas resistentes)
├── Oxidante 20vol: 70ml
└── RATIO: 1:1.5 (Wella estándar)
```

#### Optimización de Desperdicio
- **Cálculo por gramos exactos:** Precisión ±5ml
- **Ajuste por experiencia:** IA aprende si el estilista siempre usa 10% más
- **Alertas de cantidad:** "¿Seguro necesitas 120ml para melena corta?"

### 3.4. Sistema de Alertas Profesionales Críticas

#### Alertas Obligatorias de Seguridad
```
🚨 STRAND TEST OBLIGATORIO
Cabello previamente decolorado + objetivo 2+ niveles más claro
Riesgo de rotura: ALTO

⚠️ POROSIDAD EXTREMA DETECTADA  
Reducir tiempo procesado 30%
Usar oxidante 20vol máximo (no 30vol)

🔥 INCOMPATIBILIDAD QUÍMICA
Historial de henna + decoloración = PELIGRO
Consultar con químico o evitar servicio

⏰ TIMING CRÍTICO CANAS
50%+ canas requiere pre-pigmentación
Aplicar natural shade 15min antes del tono deseado
```

#### Alertas de Optimización
```
💡 SUGERENCIA DE MEJORA
Para mejor cobertura canas: Añadir 20% natural shade (7N)
Basado en 127 casos similares exitosos

🎯 TÉCNICA RECOMENDADA
Cabello base 4 → objetivo 8: Requiere double-process
Sesión 1: Decolorar a nivel 6-7
Sesión 2: Tonificar con 8/1

💰 OPTIMIZACIÓN DE COSTO
Usar Wella 8/0 + 8/1 (60/40) = mismo resultado
15% menos costo que fórmula actual
```

### 3.5. Módulo Especializado de Correcciones

#### Base de Conocimiento de Casos Complejos
**Escenarios pre-programados con soluciones validadas:**

**1. "Demasiado Claro" (Too Light)**
```
PROBLEMA: Cliente rubio nivel 9, quiere castaño nivel 6
SOLUCIÓN: Filling technique obligatorio
PROCESO:
├── Paso 1: Fill con 6/0 + 6vol (20min)
├── Paso 2: Aplicar 6/1 + 20vol (30min)
└── Productos: Wella Color Touch para filling
```

**2. "Demasiado Oscuro" (Too Dark)**
```
PROBLEMA: Color nivel 4, quiere nivel 7
SOLUCIÓN: Lightening controlado
PROCESO:
├── Mild Lightener: Blondor + 6vol (1:4 ratio)
├── Tiempo: 45min máximo
├── Test strand: Obligatorio cada 15min
└── Toning posterior con demi-permanent
```

**3. "Tonos No Deseados" (Unwanted Tones)**
```
PROBLEMA: Rubio con reflejos naranjas
SOLUCIÓN: Neutralización con complementarios
PROCESO:
├── Identificar tono: Naranja = Nivel 6-7 warmth
├── Neutralizar con: Azul (6/01, 7/01)
├── Ratio: 75% neutralizador + 25% tono deseado
└── Tiempo: 20min máximo (solo depositar)
```

#### Calculadora de Correcciones
- **Upload foto del "error":** IA analiza qué salió mal
- **Comparación con objetivo:** Diferencia en nivel y tono
- **Plan de corrección:** Step-by-step con productos específicos
- **Estimación de sesiones:** 1, 2 o 3+ para llegar al objetivo

### 3.6. Gestión Avanzada de Canas

#### Módulo Especializado por Porcentaje
**Formulación automática según % de canas:**

```
CLIENTE CON 30% CANAS:
├── Base formula: 70% color deseado
├── Natural boost: 30% natural shade (same level)
├── Oxidante: 20vol estándar
└── Tiempo: +5min para penetración

CLIENTE CON 75% CANAS:
├── Pre-pigmentación: 100% natural shade + 10vol (15min)
├── Enjuague ligero (no champú)
├── Color deseado: 50% target + 50% natural
├── Oxidante: 20vol (nunca 30vol en canas)
└── Tiempo total: 45min mínimo
```

#### Base de Datos de Natural Shades
**Por marca, catalogado por nivel:**
- **Wella:** 6/0, 7/0, 8/0 con % melanina específico
- **L'Oréal:** 6NN, 7NN, 8NN para máxima cobertura
- **Schwarzkopf:** 6-00, 7-00, 8-00 extra natural

### 3.7. Documentación Automática Inteligente

#### Durante el Proceso
**Timer multi-zona con alertas:**
```
SESIÓN EN CURSO - Cliente: María García
14:30 → Aplicación raíces iniciada
14:45 → ⏰ ALERTA: Aplicar medios AHORA  
15:00 → ⏰ ALERTA: Revisar raíces (30min)
15:15 → ⏰ ALERTA: Enjuague completo YA
```

**Tracking de desviaciones:**
- **Tiempo real vs. planeado:** +5min por resistencia
- **Cantidad real vs. calculada:** Usó 10ml extra
- **Modificaciones sobre la marcha:** Añadió 5ml de 7/0 para más cobertura

#### Documentación Final Automática
```
SESIÓN COMPLETADA - 17/06/2025 15:30

CLIENTE: María García
OBJETIVO: Cobertura canas + reflejos ceniza
RESULTADO: ✅ EXITOSO (satisfacción 9/10)

FÓRMULA FINAL APLICADA:
├── Wella 7/1: 35ml  
├── Wella 7/0: 15ml (modificado desde 12ml)
├── Oxidante 20vol: 75ml
├── Ratio real: 1:1.5
└── Tiempo total: 40min (planeado 35min)

NOTAS DEL ESTILISTA:
"Cliente quería más ceniza - añadí 3ml extra de 7/1"
"Raíces resistentes - necesitó 5min adicionales"
"Para próxima vez: usar 7/01 en lugar de 7/1"

FOTOS:
├── Antes: [IMG_001.jpg]
├── Proceso: [IMG_002.jpg] 
└── Resultado: [IMG_003.jpg]

APRENDIZAJE IA:
"María requiere +20% natural shade para canas"
"Prefiere tonos más fríos - priorizar .1 sobre .0"
```

### 3.8. Sistema de Aprendizaje Personalizado

#### IA que Aprende de Cada Estilista
**Patrones detectados automáticamente:**
```
PERFIL DE APRENDIZAJE - Estilista: Ana Rodríguez

PREFERENCIAS DETECTADAS:
├── Siempre usa 10ml extra en melenas largas
├── Prefiere 1:2 ratio para correcciones  
├── Añade 5ml de natural shade en 80% de casos
└── Evita 30vol (solo usa 10vol y 20vol)

CASOS DE ÉXITO:
├── Rubios ceniza: 95% satisfacción (47 casos)
├── Correcciones naranjas: 100% éxito (12 casos)
├── Cobertura canas 75%+: 90% satisfacción (23 casos)
└── ESPECIALIDAD DETECTADA: Correcciones complejas

SUGERENCIAS PERSONALIZADAS:
"Basado en tus 127 casos exitosos, para este tipo de cabello
recomiendo tu fórmula habitual + 5ml extra de natural shade"
```

#### Biblioteca Personal de Fórmulas
**Casos guardados por similitud:**
- **Búsqueda por tipo:** "Todas mis correcciones naranjas"
- **Búsqueda por cliente:** "Historial completo de María"
- **Búsqueda por resultado:** "Mis mejores rubios platino"
- **Búsqueda por problema:** "Casos con canas resistentes"

---

## 4. Arquitectura Técnica Optimizada

### 4.1. Frontend - App Móvil Ultra-Rápida
**React Native + Expo SDK optimizado para velocidad:**
- **Tiempo de respuesta:** <2 segundos para cualquier cálculo
- **Modo offline:** Consulta historial sin conexión
- **Cámara optimizada:** Guías automáticas + compresión inteligente
- **Calculadora integrada:** Ajustes en tiempo real sin esperas

### 4.2. Backend - Supabase + IA Especializada
**Base de datos optimizada para formulaciones:**
- **PostgreSQL** con índices específicos para búsquedas por marca/nivel/tono
- **Edge Functions** para cálculos complejos (cantidades, compatibilidades)
- **Storage optimizado** para imágenes con compresión automática

**Integración IA Económica:**
- **OpenAI GPT-4 Vision** solo para análisis visual inicial
- **Modelos propios** para cálculos de cantidades (más económico)
- **Caché inteligente:** 70% reducción en llamadas IA repetitivas
- **Base de conocimiento local:** 10,000+ fórmulas validadas sin IA

### 4.3. Base de Conocimiento Profesional
**Catálogo completo de productos:**
```sql
brands (100+ marcas principales)
├── wella_koleston (45 shades + oxidantes)
├── loreal_majirel (38 shades + específicos)
├── schwarzkopf_igora (52 shades + especialidades)
└── ...

formulation_rules (ratios validados por marca)
├── wella: 1:1.5 estándar, 1:2 high-lift
├── loreal: 1:1.5 permanent, 1:2 lifting
└── compatibility_matrix (inter-brand mixing)

correction_protocols (500+ casos documentados)
├── too_light_scenarios (filling techniques)
├── too_dark_scenarios (lifting protocols)  
├── unwanted_tones (neutralization formulas)
└── gray_coverage_advanced (by percentage)
```

---

## 5. Estrategia de Monetización Validada

### 5.1. Precios Basados en Valor Real

**Tier Profesional - $89/mes**
- Formulaciones ilimitadas con IA
- Calculadora de cantidades exactas
- Sistema de alertas profesionales
- Gestión hasta 300 clientes
- Módulo básico de correcciones

**Tier Master Colorist - $159/mes**
- Todo del Tier Profesional
- Módulo avanzado de correcciones
- IA de aprendizaje personalizado
- Gestión ilimitada de clientes
- Integración con POS básicos
- Soporte técnico prioritario

**Tier Salón Enterprise - $299/mes (hasta 8 estilistas)**
- Todo del Tier Master para cada estilista
- Dashboard de gestión del salón
- Reportes de eficiencia y desperdicio
- API para integraciones avanzadas
- Entrenamiento del equipo incluido

### 5.2. ROI Demostrable para el Cliente
```
INVERSIÓN MENSUAL: $89-299 según tier

AHORROS DOCUMENTADOS:
├── Reducción desperdicio: $200-400/mes
├── Tiempo ahorrado: $300-600/mes (efficiency)
├── Errores evitados: $150-800/mes
├── Clientes satisfechas adicionales: $400-1200/mes
└── ROI NETO: 300-500% primer año

PAYBACK PERIOD: 2-4 semanas típicamente
```

---

## 6. Plan de Desarrollo Validado

### Fase 1: MVP Core (Meses 1-8)
**Funcionalidades esenciales validadas:**
- Configuración de estilista + marcas preferidas
- Diagnóstico híbrido (IA + validación manual)
- Calculadora de cantidades ultra-precisa
- Sistema de alertas básicas
- Documentación automática
- **Target:** 100 estilistas beta, 5,000 formulaciones test

### Fase 2: Especialización (Meses 9-15)
**Módulos avanzados:**
- Sistema completo de correcciones
- Gestión avanzada de canas
- IA de aprendizaje personalizado
- Integración con POS básicos
- **Target:** 800 estilistas pagos, 25,000 formulaciones/mes

### Fase 3: Escala Enterprise (Meses 16-24)
**Funcionalidades de salón:**
- Dashboard multi-estilista
- API para integraciones avanzadas
- Módulo de entrenamiento
- Certificación Salonier Professional
- **Target:** 3,000 estilistas, 100,000 formulaciones/mes

---

## 7. Métricas de Éxito y Validación

### 7.1. Métricas de Adopción (Validadas con Beta)
- **Frecuencia de uso:** >20 formulaciones/estilista/mes
- **Retención:** >85% mes 3, >70% mes 12
- **Net Promoter Score:** >75 entre coloristas profesionales
- **Tiempo promedio por formulación:** <3 minutos (vs 15+ manual)

### 7.2. Métricas de Precisión Técnica
- **Exactitud en cálculos:** 99.7%+ en cantidades/ratios
- **Reducción de desperdicio:** >80% vs métodos tradicionales  
- **Satisfacción con resultados:** >92% casos documentados exitosos
- **Errores de formulación:** <2% vs 15-20% industria

### 7.3. Métricas de Negocio
- **ARR Target:** $3M año 1, $15M año 2
- **CAC:** <$150 por estilista (orgánico + referidos)
- **LTV:** >$4,500 por estilista (30+ meses retención)
- **Gross Margin:** >85% (SaaS puro, costos IA optimizados)

---

## 8. Diferenciación Competitiva Crítica

### Vs. Métodos Actuales (Libretas + Calculadoras)
- **Precisión:** 99.7% vs ~85% manual
- **Velocidad:** 3min vs 15-20min
- **Documentación:** Automática vs manual/perdible
- **Aprendizaje:** IA personalizada vs experiencia personal

### Vs. Herramientas Genéricas (Vish, SureTint)
- **Especialización:** 100% coloración vs tools generales
- **Multi-marca:** Cualquier marca vs propietaria
- **IA Editable:** Estilista controla vs algoritmo fijo
- **Correcciones:** Módulo especializado vs básico

### Vs. Apps de Marcas (L'Oréal, Wella)
- **Neutralidad:** Multi-marca vs marketing de marca única
- **Profesional:** Para estilistas vs consumidores
- **Precisión:** Cálculos exactos vs aproximaciones
- **Learning:** Aprende del estilista vs genérico

---

## 9. Próximos Pasos de Ejecución

### 9.1. Validación Técnica Inmediata (Mes 1-2)
- **Prototipo de calculadora:** Validar algoritmos de cantidad
- **Testing con 20 estilistas:** Verificar precisión en casos reales
- **Integración IA:** Optimizar costos con modelos híbridos
- **Base de datos:** Completar catálogo de 50 marcas principales

### 9.2. MVP Development (Mes 3-8)
- **Team:** 4 developers (2 frontend, 1 backend, 1 IA)
- **Budget:** $180,000 desarrollo + $50,000 testing
- **Beta program:** 100 estilistas seleccionados
- **Feedback loop:** Iteraciones semanales basadas en uso real

### 9.3. Go-to-Market (Mes 9-12)
- **Pricing:** Launch con early bird 50% off primeros 6 meses
- **Marketing:** Testimonios de beta users + demos en trade shows
- **Partnerships:** Distribuidores de marcas como canal
- **Content:** Casos de éxito documentados + ROI calculators

---

## 10. Conclusión: Por Qué Salonier v5.1 Va a Triunfar

### 10.1. Problema Real y Doloroso ✅
- Validado con 50+ estilistas profesionales activos
- Pain points cuantificados: $200-800/mes de pérdidas por errores
- Soluciones actuales primitivas (libretas físicas en 2025)

### 10.2. Solución Técnicamente Superior ✅
- Precisión demostrable: 99.7% vs 85% métodos actuales
- Velocidad 5x más rápida: 3min vs 15min
- IA editable que respeta expertise profesional

### 10.3. Modelo de Negocio Sólido ✅
- ROI 300-500% para clientes (self-paying product)
- LTV/CAC ratio >30:1 proyectado
- Scalable SaaS con 85%+ gross margins

### 10.4. Diferenciación Defendible ✅
- Especialización extrema (solo coloración profesional)
- Network effects (IA mejora con cada uso)
- Switching costs altos (historial + preferencias personalizadas)

### 10.5. Timing Perfecto ✅
- Industria adoptando IA pero sin soluciones específicas
- Generación de estilistas digital-native entrando al mercado
- Post-COVID: presión por eficiencia y precisión aumentada

**Salonier v5.1 no es solo una app - es la evolución inevitable de la coloración capilar profesional hacia la precisión científica.**

**Ready to execute? 🚀**